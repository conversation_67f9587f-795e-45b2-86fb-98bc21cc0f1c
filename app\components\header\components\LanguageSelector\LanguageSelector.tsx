"use client";
import React from "react";
import Image from "next/image";
import { useTranslation } from "@/app/hooks/useTranslation";
import './LanguageSelector.scss';
// Country import removed as it's no longer needed
import { languageMap, languageFlags } from "@/i18n";
import { useLanguage } from "@/app/contexts/languageContext";

interface LanguageSelectorProps {
  handleLanguageSelection: (language: string) => void;
  isRTL?: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  handleLanguageSelection,
  // isRTL
}) => {
  const { t } = useTranslation("common");
  const { currentLanguage, changeLanguage } = useLanguage();

  // Filter to only show the specified languages
  const filteredLanguages = ['en', 'ar', 'es', 'fr', 'hi'];

  return (
    <div className="language-selector">
      {/* Supported languages section */}
      <div className="language-section">
        <h3 className="section-title">{t("common.suggestedLanguages", "Supported languages")}</h3>

        <div className="language-list">
          {Object.entries(languageMap)
            .filter(([code]) => filteredLanguages.includes(code))
            .map(([code, name]) => (
              <div
                className="language-item"
                key={code}
                onClick={() => {
                  handleLanguageSelection(name);
                  changeLanguage(code);
                }}
              >
                <div className="language-info">
                  <div className="flag-container">
                    <Image
                      className="flag-image"
                      alt={`${name} flag`}
                      width={24}
                      height={18}
                      src={languageFlags[code]}
                    />
                  </div>
                  <span className="language-name">{name}</span>
                </div>

                <div className="selection-indicator">
                  {currentLanguage === code && (
                    <i className="fa-solid fa-check"></i>
                  )}
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default LanguageSelector;
