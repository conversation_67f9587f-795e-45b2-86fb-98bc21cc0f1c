"use client";
import React from "react";
import { useTranslation } from "@/app/hooks/useTranslation";

// const features = [
//   {
//     icon: "fa-ticket",
//     description: "Book Cheap Air tickets to any destination across the world.",
//     priceDetails: "Best Price Guarantee",
//   },
//   {
//     icon: "fa-bed",
//     description: "Choose from more than 1000 Best rated Hotels.",
//     priceDetails: "From cheap hotels to luxurious resorts.",
//   },
//   {
//     icon: "fa-umbrella-beach",
//     description:
//       "Get the Best deals on Holiday Packages to multiple destinations.",
//     priceDetails: "",
//   },
//   {
//     icon: "fa-plane-circle-check",
//     description:
//       "Find Budget Airlines and Full service Airlines in one screen.",
//     priceDetails: "",
//   },
//   {
//     icon: "fa-face-smile",
//     description: "Experience the convenience and savings.",
//     priceDetails: "Browse cheap flight tickets in a user-friendly website.",
//   },
//   {
//     icon: "fa-plane-departure",
//     description: "Book Airline tickets from a wide choice of Airlines.",
//     priceDetails: "",
//   },
// ];

interface Feature {
  icon: string;
  description: string;
  priceDetails?:string;
}

interface FeatureHighlightsProps {
  features: Feature[];
}

const FeatureHighlights: React.FC<FeatureHighlightsProps> = ({features}) => {
  const { t } = useTranslation();

  return (
    <div className="w-full  mt-[30px] mb-8 md:mb-12  md:mt-10 ">
      {/* Header Section */}
      <div className="mb-6 md:mb-8">
        <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 md:text-2xl">{t('featureHighlights.title')}</h3>
      </div>

      {/* Feature Cards Container */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-x-7 md:gap-y-10 p-6 md:p-8 py-10 md:py-12 rounded-lg" style={{ backgroundColor: 'var(--primary-color)' }}>
        {features?.map((feature, index) => {
          // Determine which feature key to use based on the icon
          let featureKey = '';
          switch (feature.icon) {
            case 'fa-bed':
              featureKey = 'topRatedHotels';
              break;
            case 'fa-star':
              featureKey = 'verifiedReviews';
              break;
            case 'fa-location-dot':
              featureKey = 'primeLocations';
              break;
            case 'fa-tags':
              featureKey = 'exclusiveDiscounts';
              break;
            case 'fa-shield-halved':
              featureKey = 'secureBooking';
              break;
            case 'fa-calendar-check':
              featureKey = 'flexibleBookings';
              break;
            default:
              // If no matching icon, use the provided text
              return (
                <div className="flex flex-col items-center text-center text-white" key={index}>
                  <div className="mb-4 md:mb-5">
                    <i className={`fa-solid ${feature?.icon} text-3xl md:text-4xl`}></i>
                  </div>
                  <div>
                    <p className="text-sm md:text-base font-medium mb-2 md:mb-3">{feature?.description}</p>
                    <p className="text-xs md:text-sm font-light">{feature?.priceDetails}</p>
                  </div>
                </div>
              );
          }

          // Use translations for the matched feature
          return (
            <div className="flex flex-col items-center text-center text-white" key={index}>
              <div className="mb-4 md:mb-5">
                <i className={`fa-solid ${feature?.icon} text-3xl md:text-4xl`}></i>
              </div>
              <div>
                <p className="text-sm md:text-base font-medium mb-2 md:mb-3">{t(`featureHighlights.features.${featureKey}.description`)}</p>
                <p className="text-xs md:text-sm font-light">{t(`featureHighlights.features.${featureKey}.priceDetails`)}</p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default FeatureHighlights;
