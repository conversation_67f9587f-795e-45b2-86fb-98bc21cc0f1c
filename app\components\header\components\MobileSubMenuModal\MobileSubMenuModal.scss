@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.slide-from-right-sub-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: z-index(modal) + 1;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }

  .modal-content {
    background: white;
    width: 100%;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.5s ease-in-out;
    overflow: hidden; /* Important to prevent scrolling issues */

    &.cancellation-policy-modal {
      max-width: 440px;
      width: 100%;
    }

    .header {
      background-color: $primary-color;
      color: white;
      padding: 0;
      flex-shrink: 0;
      height: 60px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;

      .header-content {
        height: 100%;
        display: flex;
        align-items: center;
        padding: 0 16px;

        .back-btn {
          background-color: rgba(255, 255, 255, 0.3);
          border: 1px solid rgba(255, 255, 255, 0.5);
          border-radius: 20px;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 8px 16px;
          cursor: pointer;
          transition: all 0.2s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          height: 36px;
          min-width: 80px;
          margin-right: 8px;

          i {
            font-size: 14px;
            margin-right: 6px;
          }

          .back-text {
            font-size: 14px;
            font-weight: 600;
            white-space: nowrap;
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(1px);
          }

          &.close-only {
            i {
              font-size: 14px;
            }
          }
        }

        h2 {
          font-size: 18px;
          font-weight: 600;
          margin: 0;
          flex: 1;
          text-align: center;
          padding-right: 80px; /* Balance with back button width */

          @media (max-width: $breakpoint-md) {
            font-size: 16px;
          }
        }
      }
    }

    .content {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
  }

  &.show .modal-content {
    transform: translateX(0);
  }

  // RTL support
  &.rtl {
    .modal-content {
      right: auto;
      left: 0;
      transform: translateX(-100%);
    }

    &.show .modal-content {
      transform: translateX(0);
    }

    .header {
      .header-content {
        flex-direction: row-reverse;

        .back-btn {
          margin-right: 0;
          margin-left: 8px;

          i {
            margin-right: 0;
            margin-left: 6px;
          }
        }

        h2 {
          padding-right: 0;
          padding-left: 80px;
        }
      }
    }

    .content {
      text-align: right;

      .common-content-container {
        &__header {
          text-align: right;
        }

        &__content {
          flex-direction: row-reverse;
        }

        &__label-with-icon {
          flex-direction: row-reverse;

          .common-content-container__img {
            margin-right: 0;
            margin-left: 10px;
          }
        }

        &__label-with-icon-description {
          text-align: right;
        }

        &__label-icon {
          margin-right: 0;
          margin-left: 10px;
        }

        &__header-input {
          .searchIcon {
            left: auto;
            right: 10px;
          }

          input {
            padding: 6px 36px 6px 12px;
          }
        }
      }
    }
  }
}
