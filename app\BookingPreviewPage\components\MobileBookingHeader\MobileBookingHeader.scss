@import '../../../../styles/globals.scss';
@import '../../../../styles/variable.scss';

.mobile-booking-header {
  background: #fff;
  padding: 10px 16px 6px;
  border-bottom: 1px solid #e5e7eb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  &__top {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;

    .back-button {
      background: none;
      border: none;
      padding: 4px;
      color: #374151;
      cursor: pointer;

      i {
        font-size: 18px;
      }
    }

    .hotel-name {
      font-size: 15px;
      font-weight: 600;
      color: #111827;
      margin: 0;
      flex: 1;
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &__details {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;

    .booking-info {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #6b7280;

      .info-item {
        display: flex;
        align-items: center;
        gap: 4px;

        i {
          color: $primary-color;
          font-size: 12px;
        }

        span {
          font-weight: 500;
        }
      }
    }

    .dates-info {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #374151;
      font-weight: 500;

      .date-range {
        font-size: 11px;
      }

      .nights-count {
        font-size: 10px;
        color: $primary-color;
        background-color: rgba($primary-color, 0.1);
        padding: 2px 5px;
        border-radius: 6px;
        font-weight: 600;
      }
    }
  }
}

// Add padding to body when mobile header is present
// .booking-preview-page {
//   @media (max-width: 780px) {
//     .booking-preview-container {
//       padding-top: 60px;
//     }
//   }
// }

// Hide on desktop
@media (min-width: 781px) {
  .mobile-booking-header {
    display: none;
  }
}
