@use "/styles/variable" as *;

.selected-hotel-card {
  background-color: $white_color;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  width: 100%;
  max-width: 350px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid $border_color;

    h3 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 280px;
    }

    .close-btn {
      background: none;
      border: none;
      color: $gray_color;
      cursor: pointer;
      font-size: 14px;
      padding: 5px;

      &:hover {
        color: $black_color;
      }
    }
  }

  .card-content {
    display: flex;
    padding: 15px;

    .hotel-image {
      width: 100px;
      height: 100px;
      flex-shrink: 0;
      margin-right: 15px;

      .image-container {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        overflow: hidden;
      }

      .no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $light_gray_color;
        border-radius: 4px;
        font-size: 12px;
        color: $gray_color;
        text-align: center;
        padding: 5px;
      }
    }

    .hotel-details {
      flex: 1;

      .rating {
        margin-bottom: 8px;

        i {
          color: $yellow_color;
          font-size: 14px;
          margin-right: 2px;
        }
      }

      .location {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 13px;
        color: $gray_color;

        i {
          margin-right: 5px;
          color: $primary_color;
        }

        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .price {
        margin-bottom: 8px;
        font-size: 14px;

        .price-label {
          font-weight: 400;
          color: $gray_color;
          margin-right: 5px;
        }

        .price-value {
          font-weight: 700;
          color: $primary_color;
        }
      }

      .user-rating {
        display: flex;
        align-items: center;

        .rating-badge {
          background-color: $primary_color;
          color: $white_color;
          font-weight: 700;
          padding: 4px 8px;
          border-radius: 4px;
          margin-right: 8px;
          font-size: 14px;
        }

        .rating-text {
          display: flex;
          flex-direction: column;

          .rating-category {
            font-size: 13px;
            font-weight: 600;
          }

          .rating-count {
            font-size: 12px;
            color: $gray_color;
          }
        }
      }
    }
  }

  .card-footer {
    padding: 12px 15px;
    border-top: 1px solid $border_color;
    text-align: right;

    .view-details-btn {
      display: inline-block;
      padding: 8px 15px;
      background-color: $primary_color;
      color: $white_color;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 600;
      text-decoration: none;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: darken($primary_color, 10%);
      }
    }
  }

  // RTL support
  html[dir="rtl"] & {
    .card-header {
      h3 {
        text-align: right;
      }
    }

    .card-content {
      .hotel-image {
        margin-right: 0;
        margin-left: 15px;
      }

      .hotel-details {
        .location {
          i {
            margin-right: 0;
            margin-left: 5px;
          }
        }

        .price {
          .price-label {
            margin-right: 0;
            margin-left: 5px;
          }
        }

        .user-rating {
          .rating-badge {
            margin-right: 0;
            margin-left: 8px;
          }
        }
      }
    }

    .card-footer {
      text-align: left;
    }
  }
}
