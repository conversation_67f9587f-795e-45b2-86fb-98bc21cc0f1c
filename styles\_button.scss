@use "sass:color";
@use '@styles/mixins' as *;
@use '@styles/variable' as *;
.dy_primary_bttn {
    padding: 10px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    font-size: 15px;
    font-weight: 500;
    line-height: 20px;
    transition-duration: 0.4s;
    background: $primary-color;
    color: #fff;
    border-radius: 5px;
    outline: 0;
    border: 1px solid $primary-color;
    min-width: 100px;
    cursor: pointer;
    .btn_icon{
        display: none;
        font-size: 20px;
    }
    &.active{
        .btn_icon{
            display: block;
            animation: spin .5s linear infinite;
        }
    }
    &:disabled{
        background-color: #ccc;
        border-color: #ccc;
    }
}

.secondary_bttn{
    color: $primary_color;
    font-size: 12px;
    width: 75px;
    height: 25px;
    border: 1px solid $black_color3;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover{
        //background-color: darken($white_color, 5%);    
        background-color: color.adjust($white_color1, $lightness: -5%);
 
    }
}

.header-bttn {
    font-size: 16px;
    font-weight: 600;
    color: $white_color;
    background-color: $primary_color;
    border-radius: 8px;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    &:hover {
        // border: 1px solid $primary_color;
        background-color: white;
        color: $primary_color;
        box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    &:active {
        transform: scale(0.98);
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
    }
}

.book-button {
    width: 150px;
    background-color: $primary-color;
    border: none;
    color: white;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 8px 30px;
    border-radius: 6px;
    transition: transform 0.2s ease-in-out background-color 0.2s ease;
    height: auto;
  
    &:hover {
      background-color: $primary-color;
      transform: scale(1.1);
    }
  }

  .linkBttn {
    position: relative;
    text-decoration: none;
    color: #ccc;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
  
    &::before {
      content: "";
      left: 0;
      bottom: -1px;
      width: 100%;
      height: 1px;
      position: absolute;
      background: $primary-color;
    }
  
    &::after {
      content: "";
      left: 0;
      bottom: -2px;
      width: 0%;
      height: 2px;
      position: absolute;
      background: $primary-color;
      @include transition(all 0.5s ease 0.1s);
    }
  
    &:hover {
      color: $primary-color !important;
  
      &::after {
        width: 100%;
      }
    }
  }

.link-button{
    font-weight: 700;
    font-size: 14px;
    color: $primary-color;
    text-decoration: underline;
    cursor: pointer;
}

.hotel-info-header-btn{
  padding: 10px 15px 10px 20px;
  color: $primary_color;
  transition: background-color 0.2s ease;
  cursor: pointer;
  border-radius: 10px;
  font-weight: 500;
  white-space: nowrap;

  @media (max-width: $breakpoint-md) {
    font-size: 14px;
    padding: 8px 13px 8px 18px;
    
  }

  .fa-solid{
      margin-left: 5px;
      font-size: 12px;
  }

  &:hover{
      background-color: rgba(8, 119, 103, 0.05);
  }
}

.hotel-info-header-reserveBtn{
  padding: 10px 15px 10px 20px;
  color: #fff;
  background-color: $primary_color;
  transition: background-color 0.2s ease;
  cursor: pointer;
  border-radius: 10px;
  font-weight: 500;
  white-space: nowrap;

   @media (max-width: $isMobile) {
    display: none;
    
  }

  @media (max-width: $breakpoint-md) {
    font-size: 14px;
    padding: 8px 13px 8px 18px;
    
  }

  .fa-solid{
      margin-left: 5px;
      font-size: 12px;
  }

  &:hover{
      background-color: color.adjust($primary_color, $lightness: -10%);
  }
}
