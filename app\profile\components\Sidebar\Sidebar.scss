@use "/styles/variable" as *;

.sidebar-container {
  background-color: #ffffff;
  border-radius: 13px;
  border-style: solid;
  border-width: 1px;
  border-color: #e0e0e0;
  &__sidebar {
    width: 100%;
    height: 100%;
    padding: 5px;

    ul {
      margin: 8px 0;

      li {
        display: flex;
        align-items: center;
        background-color: transparent;
        fill: inherit;
        color: inherit;
        padding: 8px 20px;
        border-radius: 10px;
        font-size: 14px;
        font-weight:700;
        transition: color 0.2s ease background-color 0.2s ease;
        margin-bottom: 5px;

        .fa-solid {
          margin-right: 14px;
          font-size: 18px;
        }

        &:hover, &.active {
          color: $primary_color;
          background-color: #F5EEE7;
        }

     
      }
    }
  }
}
