.shimmer-filter {
  width: 100%;
  height: auto;
  border-radius: 8px;
  background: #fff;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);

  // Hide on mobile screens (width < 950px)
  @media (max-width: 949px) {
    display: none;
  }
}

.shimmer-title,.shimmer-resetBtn,
.shimmer-checkboxTitle,
.shimmer-checkbox-label,
.shimmer-checkbox-count,
.shimmer-slider-title,
.shimmer-slider-price,
.shimmer-slider,
.shimmer-slider-shape,
.shimmer-counter-title,
.shimmer-counter-counter,
.shimmer-counter-label {
  background: linear-gradient(90deg, #e0e0e0 25%, #f5f5f5 50%, #e0e0e0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 5px;
}

.shimmer-filter-head{
  padding: 8px;
  // border-bottom: 1px solid rgba(0, 0, 0 ,0.1);

  display: flex;
  flex-direction: row;
  justify-content: space-between;

}

.shimmer-title {
  height: 18px;
  width: 35%;
}

.shimmer-resetBtn{
  height: 18px;
  width: 20%;
  border-radius: 13px !important;

}

.shimmer-checkbox-container {
  padding: 12px 16px 8px 13px;
  // border-bottom: 1px solid rgba(0, 0, 0 ,0.1);

  &:last-child{
    border: none;
  }


  .shimmer-checkboxTitle {
    height: 12px;
    width: 65%;
  }

  .shimmer-checkbox {
    margin: 10px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .shimmer-checkbox-label {
      width: 60%;
      height: 12px;
    }

    .shimmer-checkbox-count {
      width: 10%;
      height: 12px;
    }
  }
}

.shimmer-slider-container {
  padding: 12px 16px 8px 13px;
  // border-bottom: 1px solid rgba(0, 0, 0 ,0.1);


  .shimmer-slider-title {
    height: 12px;
    width: 75%;
  }

  .shimmer-slider-price {
    height: 12px;
    width: 45%;
    margin: 12px 0 5px 0;
  }

  .shimmer-slider {
    height: 8px;
    width: 100%;
    margin-top: 8px;
    position: relative;
    margin-bottom: 8px;

    .shimmer-slider-shape {
      width: 17px;
      height: 17px;
      border-radius: 50%;
      position: absolute;
      top: -5px;
    }

    .shape1 {
      left: 0;
    }

    .shape2 {
      right: 0;
    }
  }
}

.shimmer-counter-container {
  padding: 12px 16px 8px 13px;
  // border-bottom: 1px solid rgba(0, 0, 0 ,0.1);


  .shimmer-counter-title {
    height: 12px;
    width: 70%;
    margin-bottom: 10px;
  }

  .shimmer-counter {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .shimmer-counter-label {
      height: 12px;
      width: 30%;
    }
    .shimmer-counter-counter {
      height: 35px;
      width: 50%;
    }
  }
}

// Container for FilterShimmer when moved outside isNotMobile condition
.filter-shimmer-container {
  // Hide on mobile screens (width < 950px)
  @media (max-width: 949px) {
    display: none;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
