"use client";
import React from "react";
import Image from "next/image";

const destinations = [
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Rajasthan.png",
    location: "Rajasthan",
    subtitle: "Royal Heritage",
    hotels: "1,200+ hotels",
    isPopular: true,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Himachal.png",
    location: "Himachal",
    subtitle: "Mountain Paradise",
    hotels: "850+ hotels",
    isPopular: false,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Andaman1.png",
    location: "Andaman",
    subtitle: "Tropical Islands",
    hotels: "320+ hotels",
    isPopular: true,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/goa.jpg",
    location: "Goa",
    subtitle: "Beach Paradise",
    hotels: "2,100+ hotels",
    isPopular: true,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Kashmir.jpg",
    location: "Kashmir",
    subtitle: "Heaven on Earth",
    hotels: "650+ hotels",
    isPopular: false,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Northeast.png",
    location: "Northeast",
    subtitle: "Hidden Gems",
    hotels: "420+ hotels",
    isPopular: false,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Leh-Ladakh-1.png",
    location: "Leh Ladakh",
    subtitle: "Adventure Awaits",
    hotels: "280+ hotels",
    isPopular: true,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Maldives.png",
    location: "Maldives",
    subtitle: "Luxury Resorts",
    hotels: "150+ hotels",
    isPopular: true,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Switzerland1.png",
    location: "Switzerland",
    subtitle: "Alpine Beauty",
    hotels: "890+ hotels",
    isPopular: false,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Dubai.png",
    location: "Dubai",
    subtitle: "Modern Luxury",
    hotels: "1,800+ hotels",
    isPopular: true,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Kerala.jpg",
    location: "Kerala",
    subtitle: "God's Own Country",
    hotels: "1,500+ hotels",
    isPopular: true,
  },
  {
    image:
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Srilanka-1.png",
    location: "Sri Lanka",
    subtitle: "Pearl of the Ocean",
    hotels: "720+ hotels",
    isPopular: false,
  },
];

function DestinationGrid() {

  return (
    <div className="w-full mt-[30px]  md:mt-10 ">
      <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 mb-6 md:mb-8 md:text-2xl">Popular Destinations</h3>

      {/* Responsive Grid Layout for All Screen Sizes */}
      <div className="w-full mb-5">
        <div className="hidden lg:grid lg:grid-cols-3 lg:gap-5 xl:gap-[18px] 2xl:gap-4">
          {destinations.slice(0, 9).map((destination, index) => (
            <div className="w-full" key={index}>
              <div className="relative h-[200px] rounded-2xl overflow-hidden shadow-[0_4px_12px_rgba(0,0,0,0.1)] transition-all duration-300 cursor-pointer bg-white hover:-translate-y-1 hover:shadow-[0_8px_24px_rgba(0,0,0,0.15)] group">
                <div className="absolute inset-0 z-[1] rounded-2xl overflow-hidden">
                  <Image
                    src={destination.image}
                    alt={destination.location}
                    fill
                    className="object-cover rounded-2xl transition-opacity duration-300 group-hover:opacity-90"
                  />
                  {/* Overlay that brightens on hover instead of scaling */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 transition-all duration-300 group-hover:bg-opacity-5 rounded-2xl"></div>
                  {destination.isPopular && (
                    <div className="absolute top-3 left-3 bg-gradient-to-br from-[#ff6b6b] to-[#ff8e8e] rounded-[20px] px-3 py-[6px] text-[11px] font-bold text-white shadow-[0_3px_8px_rgba(0,0,0,0.3)] z-[4]">
                      <span className="flex items-center gap-1">🔥 Popular</span>
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 h-[70%] bg-gradient-to-t from-[rgba(0,0,0,0.8)] via-[rgba(0,0,0,0.4)] to-transparent z-[2] rounded-b-2xl"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-3 z-[3] flex justify-between items-end">
                  <div className="flex-1">
                    <h3 className="text-sm font-bold text-white my-0 mb-[2px] [text-shadow:0_2px_4px_rgba(0,0,0,0.5)] leading-[1.1]">{destination.location}</h3>
                    <p className="text-[10px] text-[rgba(255,255,255,0.9)] my-0 mb-[3px] [text-shadow:0_1px_2px_rgba(0,0,0,0.5)]">{destination.subtitle}</p>
                    <span className="text-[8px] text-[rgba(255,255,255,0.8)] bg-[rgba(255,255,255,0.2)] px-[6px] py-[2px] rounded-[10px] border border-[rgba(255,255,255,0.3)]">{destination.hotels}</span>
                  </div>
                  <div className="bg-[rgba(255,255,255,0.2)] border border-[rgba(255,255,255,0.3)] rounded-[20px] px-[10px] py-[6px] flex items-center gap-1 text-white text-[10px] font-semibold transition-all duration-300 hover:bg-[rgba(255,255,255,0.3)] hover:translate-x-0.5">
                    <span>Explore</span>
                    <i className="fa-solid fa-arrow-right text-[8px] transition-transform duration-300 hover:translate-x-0.5"></i>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile Scroll Layout */}
        <div className="flex gap-4 overflow-x-auto pb-4 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden lg:hidden">
          {destinations.slice(0, 9).map((destination, index) => (
            <div className="flex-[0_0_auto] w-[280px] sm:w-[260px]" key={index}>
              <div className="relative h-[200px] rounded-2xl overflow-hidden shadow-[0_4px_12px_rgba(0,0,0,0.1)] transition-all duration-300 cursor-pointer bg-white active:scale-[0.98] group">
                <div className="absolute inset-0 z-[1] rounded-2xl overflow-hidden">
                  <Image
                    src={destination.image}
                    alt={destination.location}
                    fill
                    className="object-cover rounded-2xl transition-opacity duration-300 group-active:opacity-90"
                  />
                  {/* Overlay that brightens on active instead of scaling */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 transition-all duration-300 group-active:bg-opacity-5 rounded-2xl"></div>
                  {destination.isPopular && (
                    <div className="absolute top-3 left-3 bg-gradient-to-br from-[#ff6b6b] to-[#ff8e8e] rounded-[20px] px-3 py-[6px] text-[11px] font-bold text-white shadow-[0_3px_8px_rgba(0,0,0,0.3)] z-[4]">
                      <span className="flex items-center gap-1">🔥 Popular</span>
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 h-[70%] bg-gradient-to-t from-[rgba(0,0,0,0.8)] via-[rgba(0,0,0,0.4)] to-transparent z-[2] rounded-b-2xl"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4 z-[3] flex justify-between items-end">
                  <div className="flex-1">
                    <h3 className="text-base font-bold text-white my-0 mb-[3px] [text-shadow:0_2px_4px_rgba(0,0,0,0.5)] leading-[1.1]">{destination.location}</h3>
                    <p className="text-[11px] text-[rgba(255,255,255,0.9)] my-0 mb-1 [text-shadow:0_1px_2px_rgba(0,0,0,0.5)]">{destination.subtitle}</p>
                    <span className="text-[9px] text-[rgba(255,255,255,0.8)] bg-[rgba(255,255,255,0.2)] px-[7px] py-[3px] rounded-[10px] border border-[rgba(255,255,255,0.3)]">{destination.hotels}</span>
                  </div>
                  <div className="bg-[rgba(255,255,255,0.2)] border border-[rgba(255,255,255,0.3)] rounded-[20px] px-3 py-[7px] flex items-center gap-[5px] text-white text-[11px] font-semibold transition-all duration-300 hover:bg-[rgba(255,255,255,0.3)]">
                    <span>Explore</span>
                    <i className="fa-solid fa-arrow-right text-[9px] transition-transform duration-300"></i>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default DestinationGrid;
