"use client";
import React from "react";
import { getAmenityIcon, getAmenityConfig, getAmenitiesByCategory, getAllCategories } from "../../data/amenities";

interface AmenitiesShowcaseProps {
  amenities?: string[];
}

const AmenitiesShowcase: React.FC<AmenitiesShowcaseProps> = ({ amenities = [] }) => {
  const categories = getAllCategories();

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Hotel Amenities System</h2>
      
      {/* Sample Amenities */}
      {amenities.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4 text-gray-700">Current Hotel Amenities</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {amenities.map((amenity, index) => {
              const config = getAmenityConfig(amenity);
              return (
                <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                  {getAmenityIcon(amenity, 20, "mr-3 text-blue-600")}
                  <div>
                    <div className="font-medium text-sm text-gray-800">{config.name}</div>
                    <div className="text-xs text-gray-500 capitalize">{config.category}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Categories Overview */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4 text-gray-700">Amenities by Category</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map(category => {
            const categoryAmenities = getAmenitiesByCategory(category);
            return (
              <div key={category} className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 capitalize mb-3">{category}</h4>
                <div className="space-y-2">
                  {categoryAmenities.slice(0, 5).map((config, index) => (
                    <div key={index} className="flex items-center text-sm">
                      {getAmenityIcon(config.name, 16, "mr-2 text-gray-600")}
                      <span className="text-gray-700">{config.name}</span>
                    </div>
                  ))}
                  {categoryAmenities.length > 5 && (
                    <div className="text-xs text-gray-500">
                      +{categoryAmenities.length - 5} more...
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Usage Examples */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-blue-800">Usage Examples</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center">
            {getAmenityIcon("Free WiFi", 16, "mr-2 text-blue-600")}
            <code className="bg-white px-2 py-1 rounded text-blue-800">getAmenityIcon("Free WiFi")</code>
          </div>
          <div className="flex items-center">
            {getAmenityIcon("Swimming Pool", 16, "mr-2 text-blue-600")}
            <code className="bg-white px-2 py-1 rounded text-blue-800">getAmenityIcon("Swimming Pool")</code>
          </div>
          <div className="flex items-center">
            {getAmenityIcon("Pet Friendly", 16, "mr-2 text-blue-600")}
            <code className="bg-white px-2 py-1 rounded text-blue-800">getAmenityIcon("Pet Friendly")</code>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AmenitiesShowcase;
