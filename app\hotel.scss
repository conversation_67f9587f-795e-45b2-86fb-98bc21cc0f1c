@use '/styles/zIndex' as *;
@use "/styles/variable" as *;

.hotel-page-container {
  width: 100%;
@media(max-width: $isMobile) {
   //padding-top: 40px;

}
  .hotel-searchbar-landing-main-container {

    width: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: cover;
    background-position: center;
    flex-direction: column;

    // Desktop styles (default)
    height: 500px;
    //padding: 40px 20px;
padding: 40px 0;
    background-attachment: fixed;
    border-end-start-radius: 16px;
    border-end-end-radius: 16px;

    // Mobile and tablet styles (≤950px) - Full space utilization
    @media (max-width: 950px) {
      height: auto;
      min-height: auto; // Let content determine height
      padding: 0; // Remove all padding for full space utilization
      margin: 0; // Remove any margin
      width: 100vw; // Full viewport width
      position: relative;
      left: 50%;
      right: 50%;
      margin-left: -50vw;
      margin-right: -50vw;
      background: none !important; // Force remove background image/gradient
      background-color: transparent !important; // Transparent background
      background-image: none !important; // Ensure no background image
      border-radius: 0; // Remove border radius for full-width design
    }

    // Hero text section styling
    .hero-text-section {
      text-align: center;
      margin-bottom: 85px;
      z-index: z-index(base);
      padding: 0 20px;

      @media (max-width: $isMobile) {
        display: none;
      }


      .hero-heading {
        font-size: 40px;
        font-weight: 700;
        color: white;
        margin-bottom: 15px;
        text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
        line-height: 1.2;
        letter-spacing: -0.01em;

        @media (max-width: 950px) {
          font-size: 36px;
          margin-bottom: 16px;
        }

        @media (max-width: 480px) {
          font-size: 28px;
          margin-bottom: 14px;
        }
      }

      .hero-description {
        font-size: 15px;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.92);
        max-width: 650px;
        margin: 0 auto;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        line-height: 1.5;

        @media (max-width: 950px) {
          font-size: 15px;
          max-width: 550px;
          padding: 0 25px;
        }

        @media (max-width: 480px) {
          font-size: 14px;
          max-width: 320px;
          padding: 0 15px;
        }
      }

      // Adjust spacing for mobile
      @media (max-width: 950px) {
        padding: 0 15px;
      }

      @media (max-width: 480px) {
        padding: 0 10px;
      }
    }

    .search-bar-landing {
      width: 100%;
      //max-width: 1200px;
      z-index: z-index(base);

      // Desktop styles (>950px)
      @media (min-width: 951px) {
        //width: 85%;
        //max-width: 1000px;
      }

      // Mobile and tablet styles (≤950px) - Full width
      @media (max-width: 950px) {
        width: 100%;
        max-width: 100%;
      }
    }

    // Add a subtle overlay pattern for better visual depth
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(
        ellipse at center,
        transparent 0%,
        rgba(0, 0, 0, 0.1) 100%
      );
      pointer-events: none;
      z-index: 1;
    }

    // Ensure search bar is above the overlay
    .search-bar-landing {
      position: relative;
      z-index: z-index(base);
    }

    // Services Navigation Grid - Reference-Inspired Design
    .services-navigation-container {
      width: 100%;
      max-width: 1200px;
      z-index: z-index(base);
      position: relative;
      padding: 24px 20px;
      background: white;

      .services-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        padding: 0;

        .service-item {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 24px 20px;
          background: white;
          border-radius: 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 4px 20px rgba(0, 59, 149, 0.08);
          border: 1px solid rgba(0, 59, 149, 0.1);
          min-height: 130px;



          &:hover {
            transform: translateY(-6px);
            box-shadow: 0 12px 30px rgba(0, 59, 149, 0.12);
            border-color: rgba(0, 59, 149, 0.15);
          }

          &:active {
            transform: translateY(-3px);
            transition: all 0.2s ease;
          }

          .service-icon {
            position: relative;
            z-index: 2;
            margin-bottom: 14px;
            transition: all 0.3s ease;
            padding: 12px;
            border-radius: 12px;
            background: rgba(0, 59, 149, 0.05);

            img {
              width: 54px;
              height: 54px;
              object-fit: contain;
              transition: all 0.3s ease;
            }
          }

          .service-label {
            position: relative;
            z-index: 2;
            font-size: 16px;
            font-weight: 600;
            color: #003b95;
            text-align: center;
            line-height: 1.2;
            margin: 0;
          }

          &:hover .service-icon {
            transform: translateY(-2px) scale(1.05);
            background: rgba(0, 59, 149, 0.1);
          }
        }

        // Mobile responsive adjustments
        @media (max-width: 480px) {
          gap: 16px;

          .service-item {
            padding: 20px 16px;
            min-height: 115px;
            border-radius: 14px;

            .service-icon {
              padding: 10px;
              margin-bottom: 12px;

              img {
                width: 48px;
                height: 48px;
              }
            }

            .service-label {
              font-size: 15px;
            }

            &:hover {
              transform: translateY(-4px);
            }
          }
        }

        // Very small screens
        @media (max-width: 360px) {
          gap: 12px;

          .service-item {
            padding: 18px 12px;
            min-height: 105px;
            border-radius: 12px;

            .service-icon {
              padding: 8px;
              margin-bottom: 10px;

              img {
                width: 44px;
                height: 44px;
              }
            }

            .service-label {
              font-size: 14px;
            }

            &:hover {
              transform: translateY(-3px);
            }
          }
        }
      }
    }
  }

  // .main-page-content-container {
  //   padding-top: 60px;
  //   max-width: 1150px;
  //   margin: auto;
  // }
  .footer-main-container {
    border-top: 2px solid #c8d4dd;
    //background-color: #F7F8FD;
    background-color: #f9f7f5;

  }
}
