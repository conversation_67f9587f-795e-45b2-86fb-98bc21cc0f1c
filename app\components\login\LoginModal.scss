@use "/styles/variable" as *;

.login-modal {
  // Google sign-in button styling
  .google-signin-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 8px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: white;
    color: #5f6368;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f8f9fa;
    }

    svg {
      width: 18px;
      height: 18px;
    }
  }

  // Divider styling
  .divider {
    display: flex;
    align-items: center;
    margin: 16px 0;

    &::before,
    &::after {
      content: "";
      flex: 1;
      border-top: 1px solid #e0e0e0;
    }

    span {
      padding: 0 10px;
      color: #70757a;
      font-size: 14px;
    }
  }

  // OTP input styling
  .otp-input-container {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;

    input {
      width: 48px;
      height: 48px;
      text-align: center;
      font-size: 18px;
      border: 1px solid $border-color;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
        outline: none;
      }
    }
  }

  // Verification badge styling
  .verification-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;

    &.verified {
      background-color: rgba(#10b981, 0.1);
      color: #10b981;
    }

    &.pending {
      background-color: rgba(#f59e0b, 0.1);
      color: #f59e0b;
    }

    i {
      margin-right: 4px;
    }
  }

  // Timer styling
  .resend-timer {
    font-size: 12px;
    color: $gray_color;

    .countdown {
      font-weight: 600;
      color: $primary-color;
    }
  }

  // Form transitions
  .form-transition {
    transition: all 0.3s ease;

    &.slide-enter {
      opacity: 0;
      transform: translateX(20px);
    }

    &.slide-enter-active {
      opacity: 1;
      transform: translateX(0);
    }

    &.slide-exit {
      opacity: 1;
      transform: translateX(0);
    }

    &.slide-exit-active {
      opacity: 0;
      transform: translateX(-20px);
    }
  }

  // Responsive adjustments
  @media (max-width: $breakpoint-sm) {
    .otp-input-container {
      input {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }
    }
  }

  // RTL support
  html[dir="rtl"] & {
    .verification-badge {
      i {
        margin-right: 0;
        margin-left: 4px;
      }
    }

    .form-transition {
      &.slide-enter {
        transform: translateX(-20px);
      }

      &.slide-exit-active {
        transform: translateX(20px);
      }
    }
  }
}

// Override existing styles from LoginSignupPopup.scss
.login-signup-container {
  .otp-input {
    width: 48px;
    height: 48px;
    text-align: center;
    font-size: 18px;
    border: 1px solid $border-color;
    border-radius: 8px;

    &:focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      outline: none;
    }
  }

  .verification-button {
    min-width: 80px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;

    &.primary {
      background-color: $primary-color;
      color: white;

      &:hover:not(:disabled) {
        background-color: darken($primary-color, 10%);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    &.success {
      background-color: rgba(#10b981, 0.1);
      color: #10b981;
      cursor: default;
    }
  }
}
