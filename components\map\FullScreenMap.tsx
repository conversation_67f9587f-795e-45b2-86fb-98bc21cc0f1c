'use client';

import React, { useEffect } from 'react';
import MapWrapper from './MapWrapper';
import { MapMarker } from './Map';
import { useTranslation } from '@/app/hooks/useTranslation';
import FullScreenHotelSearchMap from './FullScreenHotelSearchMap';
import { Hotel as HotelType } from '@/app/HotelSearchResult/hotel-search-result.model';
import { HotelFilterData } from '@/app/components/utilities/helpers/hotel/filterHotels';
import './FullScreenMap.scss';

interface FullScreenMapProps {
  isOpen: boolean;
  onClose: () => void;
  center: [number, number];
  zoom: number;
  markers: MapMarker[];
  title?: string;
  hotels?: Array<HotelType>;
  destination?: {
    name: string;
    latitude: number;
    longitude: number;
  };
  filterData?: HotelFilterData;
  onFilterChange?: (filterData: HotelFilterData | undefined) => void;
  isHotelSearchMap?: boolean;
  onHotelSelect?: (hotelId: number) => void;
}

const FullScreenMap: React.FC<FullScreenMapProps> = ({
  isOpen,
  onClose,
  center,
  zoom,
  markers,
  title,
  hotels = [],
  destination,
  filterData,
  onFilterChange,
  isHotelSearchMap = false,
  onHotelSelect
}) => {
  const { t } = useTranslation();

  // Force re-render when map is opened to ensure tooltips are displayed correctly
  useEffect(() => {
    if (isOpen) {
      // Add a small delay to ensure the map is fully rendered
      const timer = setTimeout(() => {
        // Force a window resize event to trigger Leaflet to recalculate positions
        window.dispatchEvent(new Event('resize'));
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // If this is a hotel search map, use the enhanced full-screen hotel search map
  if (isHotelSearchMap && hotels.length > 0) {
    return (
      <FullScreenHotelSearchMap
        isOpen={isOpen}
        onClose={onClose}
        hotels={hotels}
        destination={destination}
        filterData={filterData}
        onFilterChange={onFilterChange}
        onHotelSelect={onHotelSelect}
      />
    );
  }

  // Otherwise, use the standard full-screen map
  return (
    <div className={`fullscreen-map ${isOpen ? 'show' : ''}`}>
      <div className="fullscreen-map__header">
        <h2>{title || t('map.mapView')}</h2>
        <div className="header-buttons">
          <button className="minimize-btn" onClick={onClose} aria-label="Minimize map">
            <i className="fa-solid fa-compress"></i>
          </button>
          <button className="close-btn" onClick={onClose} aria-label="Close map">
            <i className="fa-solid fa-xmark"></i>
          </button>
        </div>
      </div>

      <div className="fullscreen-map__content">
        <MapWrapper
          center={center}
          zoom={zoom}
          markers={markers}
          style={{ height: '100%', width: '100%' }}
          autoCenter={true}
          fitBounds={markers.length > 1}
        />
      </div>
    </div>
  );
};

export default FullScreenMap;
