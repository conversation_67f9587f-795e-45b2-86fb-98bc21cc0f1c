@use "/styles/variable" as *;

.hotel-facilities-section {
  padding: 30px 0;

  @media (max-width: $isMobile) {
    padding: 15px 0 30px 0;
    
  }
  
  .section-title {
    font-size: 22px;
    font-weight: 600;
    color: #333;
    margin-bottom: 25px;
    text-align: center;
    position: relative;

    @media (max-width: $isMobile) {
      display: none;
      
    }
    
    &:after {
      content: '';
      display: block;
      width: 40px;
      height: 2px;
      background-color: $primary-color;
      margin: 8px auto 0;
    }
  }
  
  .facilities-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    
    @media (max-width: 992px) {
      flex-direction: column;
    }
    
    .facilities-column {
      flex: 1;
      min-width: 250px;
      
      @media (max-width: 992px) {
        width: 100%;
      }
    }
    
    .facility-box {
      margin-bottom: 20px;
      
      .facility-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        
        i {
          color: $secondary-color;
          font-size: 18px;
          margin-right: 10px;
        }
        
        h3 {
          font-size: 18px;
          font-weight: 500;
          color: $primary-color;
          margin: 0;
        }
      }
      
      .facility-content {
        padding-left: 28px;
        
        .facility-item {
          position: relative;
          font-size: 14px;
          color: #555;
          margin-bottom: 8px;
          padding-left: 12px;
          
          &:before {
            content: '•';
            position: absolute;
            left: 0;
            color: $primary-color;
          }
        }
      }
    }
  }
}