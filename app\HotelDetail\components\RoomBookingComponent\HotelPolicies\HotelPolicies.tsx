"use client";
import React from "react";
import "./HotelPolicies.scss";
import { HouseRulesAndPolicies } from "@/app/HotelDetail/hotel-detail-result.model";
import DOMPurify from 'dompurify'

interface HotelPoliciesProps {
  houseRulesAndPolicies?: HouseRulesAndPolicies;
}

function HotelPolicies({ houseRulesAndPolicies }: HotelPoliciesProps) {
  return (
    <div className="hotel-policies-section">
      <h6>Hotel Policies</h6>

      <div className="hotel-policies-container">
        <div className="flex gap-x-8">
          <div className="hotel-checkInOut-details">
            <div className="label">
              <i className="fa-solid fa-person-walking-arrow-right"></i>
              <span>Check-in</span>
            </div>
            <div className="details">
              From {houseRulesAndPolicies?.checkinTime}
            </div>
          </div>

          <div className="hotel-checkInOut-details">
            <div className="label">
              <i className="fa-solid fa-person-walking-arrow-loop-left"></i>
              <span>Check-Out </span>
            </div>
            <div className="details">
              Till {houseRulesAndPolicies?.checkoutTime}
            </div>
          </div>
        </div>

        <div>
          {
            houseRulesAndPolicies?.rules.map((item,index)=>(
              <div key={index}>
                {index === 0 && item.policyType == "CHILD_AND_EXTRA_BED_POLICY" && (


            <div className="children-extra-beds">
              <div className="children-extra-beds__heading">
                <i className="fa-solid fa-children"></i>
                <span>{item.heading}</span>
              </div>

              <div className="children-extra-beds__intro-list">
                <ul className="children-extra-beds__intro">
                  <li>All children are welcome.</li>
                </ul>

                <ul className="children-extra-beds__list">
                  {item.subRules.map(
                    (subrules, index) => (
                      <li key={index} className="children-extra-beds__item">
                        <span>{subrules?.subHeading}</span>
                        <p>{subrules?.details}</p>
                      </li>
                    )
                  )}
                </ul>
              </div>
            </div>
                ) 

              }
                {index === 1 && item.policyType == "OTHER_INFO" && (
            <div className="propertyInformation">
              <div className="propertyInformation__heading">
                <i className="fa-regular fa-file"></i>
                <span>{item?.heading}</span>
              </div>

              <ul className="propertyInformation__list">
                {item?.subRules?.[0].details.map(
                  (details, index) => (
                    <li key={index} className="propertyInformation__item">
                      {details}
                    </li>
                  )
                )}

 
              </ul>
            </div>
          )}

           {index === 2 && item.policyType == "ANNOUNCEMENT" && (
            <div className="children-extra-beds">
              <div className="children-extra-beds__heading">
                <i className="fa-solid fa-building"></i>
                <span>{item?.heading}</span>
              </div>

              <div className="children-extra-beds__intro-list">
                <ul className="children-extra-beds__intro">
                 <li
                          dangerouslySetInnerHTML={{
                            __html: DOMPurify.sanitize(item?.subRules?.[0].details?.[0] || ""),
                          }}
                        ></li>
                </ul>

          
              </div>
            </div>
          )}
              </div>
              
            ))
          }
      

    

        </div>
      </div>
    </div>
  );
}

export default HotelPolicies;
