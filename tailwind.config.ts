import type { Config } from "tailwindcss";

export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      zIndex: {
        hide: "-1",
        auto: "0",
        base: "1",
        header: "50",
        dropdown: "100",
        sticky: "200",
        overlay: "300",
        modal: "400",
        popover: "500",
        toast: "600",
        tooltip: "700",
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        'primary-color': 'var(--primary-color)',
        'primary-color-dark': 'var(--primary-color-dark)',
        'secondary-color': 'var(--secondary-color)',
      },
      screens: {
         'ismobile' : {'max': '950px'},
         'max-md768': {'max': '768px'},
          'max-sm':   {'max': '639px'},
      }
    },
  },
  plugins: [],
} satisfies Config;
