# Technical Context

## Technologies Used
- **Frontend Framework**: Next.js with React
- **Styling**: SCSS modules with BEM methodology
- **Maps**: Leaflet for interactive maps
- **HTTP Client**: Axios for API requests
- **Internationalization**: Custom translation hooks and context
- **Icons**: Mix of custom icons from app/assets/icons and Font Awesome

## Development Setup
- Next.js application structure
- TypeScript for type safety
- Component-based architecture
- SCSS for styling

## Technical Constraints
- Support for multiple languages including RTL languages
- Responsive design for various screen sizes
- Accessibility requirements
- Performance optimization for map components

## Map Implementation
- Leaflet for interactive maps
- Custom markers for hotel locations
- Tooltips for displaying hotel information
- Full-screen mode for enhanced map viewing
- Filtering capabilities for hotel search

## Data Models
- Hotel model with detailed information
- Location data with latitude and longitude
- Filter data for hotel search
- Booking information for reservations

## API Integration
- Fetching hotel data from API endpoints
- Filtering and sorting on the client side
- Mock data for development and testing
