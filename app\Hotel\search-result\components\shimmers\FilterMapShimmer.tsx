"use client";
import React from "react";
import "./FilterMapShimmer.scss";

function FilterMapShimmer() {
  return (
    <div className="filter-map-shimmer-container">
      {/* Map Shimmer */}
      <div className="map-shimmer-wrapper">
        <div className="map-shimmer-background">
          {/* Map background shimmer */}
          <div className="map-shimmer-effect"></div>
          
          {/* Show map button shimmer */}
          <div className="map-button-shimmer-container">
            <div className="map-button-shimmer">
              <div className="shimmer-icon"></div>
              <div className="shimmer-text"></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Filter Shimmer */}
      <div className="shimmer-filter">
        <div className="shimmer-filter-head">
          <div className="shimmer-title"></div>
          <div className="shimmer-resetBtn"></div>
        </div>
        
        <div className="shimmer-checkbox-container">
          <div className="shimmer-checkboxTitle"></div>
          <div className="shimmer-checkbox">
            <div className="shimmer-checkbox-label"></div>
            <div className="shimmer-checkbox-count"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FilterMapShimmer;