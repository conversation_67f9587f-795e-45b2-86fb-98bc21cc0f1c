"use client";
import React from "react";
import "./ShimmerInfoHeader.scss";


function ShimmerInfoHeader() {
  return (
    <div className="shimmer-hotel-info-header">
      <div className="shimmer-info-header">
        <div className="heading">
            <h2></h2>
          <div className="rating">

          </div>
        </div>

        <div className="buttons">
          <div className="hotel-info-header-btn"></div>

          <div className="hotel-info-header-btn"></div>

          <div className="hotel-info-header-reserveBtn">
          
          </div>
        </div>
      </div>

      <div className="review-location">
        <div className="review">
          <div className="rating"></div>

          <div className="rating-detail">
            <p className="detail detail1"></p>
            <p className="detail detail2"></p>
          </div>
        </div>

        <div className="location">
          <div className="icon">
           
          </div>
          <div className="details">
            <div className="detail detail1">
  
            </div>

            <div className="link">
              {" "}
              <div className="detail detail2"></div>
            </div>
          </div>
        </div>

        

        
      </div>
    </div>
  );
}

export default ShimmerInfoHeader;
