"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import "./Reviews.scss";
import Image from "next/image";

// Define interfaces for type safety
interface ReviewImage {
  url: string;
  alt?: string;
}

interface Review {
  id: number;
  hotelName: string;
  location: string;
  rating: number;
  date: string;
  comment: string;
  images?: string[];
}

interface BookedHotel {
  id: number;
  hotelName: string;
  location: string;
  bookingDate: string;
  checkInDate: string;
  checkOutDate: string;
  reviewed: boolean;
}

const Reviews = () => {
  const router = useRouter();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [bookedHotels, setBookedHotels] = useState<BookedHotel[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showAddReviewForm, setShowAddReviewForm] = useState<boolean>(false);
  const [selectedHotel, setSelectedHotel] = useState<BookedHotel | null>(null);
  
  // Form state
  const [rating, setRating] = useState<number>(5);
  const [comment, setComment] = useState<string>("");
  const [images, setImages] = useState<File[]>([]);
  const [previewImages, setPreviewImages] = useState<string[]>([]);

  // Mock data - replace with your actual API call
  useEffect(() => {
    // Simulate API call
    const fetchData = async () => {
      try {
     
        const mockReviews: Review[] = [
          {
            id: 1,
            hotelName: "Seaside Resort",
            location: "Miami Beach",
            rating: 4.5,
            date: "2025-03-15",
            comment: "Beautiful hotel with amazing views and excellent service!",
            images: ["https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768"]
          },
          {
            id: 2,
            hotelName: "Mountain Lodge",
            location: "Aspen",
            rating: 5,
            date: "2025-02-20",
            comment: "Perfect winter getaway. The staff was incredibly helpful and accommodating.",
            images: ["https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768"]
          }
        ];
        
        const mockBookedHotels: BookedHotel[] = [
          {
            id: 101,
            hotelName: "Grand Plaza Hotel",
            location: "New York City",
            bookingDate: "2025-01-10",
            checkInDate: "2025-04-05",
            checkOutDate: "2025-04-10",
            reviewed: false
          },
          {
            id: 102,
            hotelName: "Lakeside Inn",
            location: "Chicago",
            bookingDate: "2025-02-15",
            checkInDate: "2025-03-01",
            checkOutDate: "2025-03-05",
            reviewed: false
          },
          {
            id: 103,
            hotelName: "Seaside Resort",
            location: "Miami Beach",
            bookingDate: "2025-01-20",
            checkInDate: "2025-03-10",
            checkOutDate: "2025-03-15",
            reviewed: true
          }
        ];
        
        setReviews(mockReviews);
        setBookedHotels(mockBookedHotels);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Function to render stars based on rating
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<span key={i} className="star filled">★</span>);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<span key={i} className="star half-filled">★</span>);
      } else {
        stars.push(<span key={i} className="star">☆</span>);
      }
    }

    return stars;
  };

  // Interactive stars for rating selection
  const renderSelectableStars = () => {
    return (
      <div className="selectable-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`star ${rating >= star ? 'filled' : ''}`}
            onClick={() => setRating(star)}
            onMouseEnter={() => setRating(star)}
          >
            {rating >= star ? '★' : '☆'}
          </span>
        ))}
      </div>
    );
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setImages([...images, ...filesArray]);
      
      // Create preview URLs
      const newPreviewImages = filesArray.map(file => URL.createObjectURL(file));
      setPreviewImages([...previewImages, ...newPreviewImages]);
    }
  };

  // Remove image from preview
  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
    
    const newPreviewImages = [...previewImages];
    URL.revokeObjectURL(newPreviewImages[index]);
    newPreviewImages.splice(index, 1);
    setPreviewImages(newPreviewImages);
  };

  // Open add review form for a specific hotel
  const openAddReviewForm = (hotel: BookedHotel) => {
    setSelectedHotel(hotel);
    setShowAddReviewForm(true);
    setRating(5);
    setComment("");
    setImages([]);
    setPreviewImages([]);
  };

  // Submit review
  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedHotel) return;
    
    // In a real application, you would upload images to your storage and get URLs back
    // For this demo, we'll create dummy URLs for the preview images
    const imageUrls = previewImages.length > 0 
      ? previewImages 
      : undefined;
    
    const newReview: Review = {
      id: Date.now(), // Simple ID generation for demo
      hotelName: selectedHotel.hotelName,
      location: selectedHotel.location,
      rating: rating,
      date: new Date().toISOString().split('T')[0],
      comment: comment,
      images: imageUrls
    };
    
    // Update reviews list
    setReviews([newReview, ...reviews]);
    
    // Mark hotel as reviewed
    const updatedHotels = bookedHotels.map(hotel => 
      hotel.id === selectedHotel.id ? { ...hotel, reviewed: true } : hotel
    );
    setBookedHotels(updatedHotels);
    
    // Reset form and close it
    setShowAddReviewForm(false);
    setSelectedHotel(null);
    
    // In a real application, you would send this data to your API
    // await fetch('/api/user/reviews', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(newReview)
    // });
  };

  if (loading) {
    return (
      <div className="mt-3 pt-3">
        <div className="list-container loading-container">
          <p>Loading your reviews...</p>
        </div>
      </div>
    );
  }

  return (

      <div>
        {/* Add Review Section */}
        <div className="add-review-section">
          <div className="section-header">
            <h4 className="section-heading">Your Booked Hotels</h4>
            {!showAddReviewForm && (
              <p className="section-subheading">Leave a review for hotels you've stayed at</p>
            )}
          </div>
          
          {showAddReviewForm ? (
            <div className="review-form-container">
              <div className="form-header">
                <h5>Add Review for {selectedHotel?.hotelName}</h5>
                <button 
                  className="close-button"
                  onClick={() => setShowAddReviewForm(false)}
                >
                  &times;
                </button>
              </div>
              
              <form onSubmit={handleSubmitReview} className="review-form">
                <div className="form-group">
                  <label>Rating</label>
                  {renderSelectableStars()}
                </div>
                
                <div className="form-group">
                  <label htmlFor="comment">Your Review</label>
                  <textarea
                    id="comment"
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    rows={5}
                    placeholder="Share your experience at this hotel..."
                    required
                  />
                </div>
                
                <div className="form-group">
                  <label>Add Photos (Optional)</label>
                  <input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    className="file-input"
                  />
                  
                  {previewImages.length > 0 && (
                    <div className="image-previews">
                      {previewImages.map((preview, index) => (
                        <div key={index} className="image-preview-container">
                          <Image
                            src={preview}
                            alt="Preview"
                            className="image-preview"
                            width={300}
                            height={400}
                          />
                          <button
                            type="button"
                            className="remove-image"
                            onClick={() => removeImage(index)}
                          >
                            &times;
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                <div className="form-actions">
                  <button
                    type="button"
                    className="cancel-button"
                    onClick={() => setShowAddReviewForm(false)}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="submit-button"
                  >
                    Submit Review
                  </button>
                </div>
              </form>
            </div>
          ) : (
            <div className="booked-hotels-list">
              {bookedHotels.filter(hotel => !hotel.reviewed).length > 0 ? (
                bookedHotels.filter(hotel => !hotel.reviewed).map((hotel) => (
                  <div key={hotel.id} className="booked-hotel-card">
                    <div className="hotel-info">
                      <h5>{hotel.hotelName}</h5>
                      <p className="location">{hotel.location}</p>
                      <p className="stay-dates">
                        Stay: {formatDate(hotel.checkInDate)} - {formatDate(hotel.checkOutDate)}
                      </p>
                    </div>
                    <button
                      className="add-review-button"
                      onClick={() => openAddReviewForm(hotel)}
                    >
                      Add Review
                    </button>
                  </div>
                ))
              ) : (
                <div className="no-hotels-to-review">
                  <p>You don't have any unreviewed hotel stays.</p>
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Reviews List Section */}
        {reviews.length > 0 ? (
          <div className="reviews-section">
            <h4 className="reviews-heading">Your Hotel Reviews</h4>
            <div className="reviews-list">
              {reviews.map((review) => (
                <div key={review.id} className="review-card">
                  <div className="review-header">
                    <div className="hotel-info">
                      <h5>{review.hotelName}</h5>
                      <p className="location">{review.location}</p>
                    </div>
                    <div className="review-date">
                      {formatDate(review.date)}
                    </div>
                  </div>
                  
                  <div className="rating">
                    {renderStars(review.rating)}
                    <span className="rating-value">{review.rating.toFixed(1)}</span>
                  </div>
                  
                  <p className="review-comment">{review.comment}</p>
                  
                  {review.images && review.images.length > 0 && (
                    <div className="review-images">
                      {review.images.map((image, index) => (
                        <Image
                          key={index} 
                          src={image} 
                          alt={`Review for ${review.hotelName}`} 
                          className="review-image"
                          width={400}    
                          height={300} 
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="no-reviews-found">
            <h5>No reviews yet. Share your experiences!</h5>
            <p>Add reviews for hotels you've stayed at above.</p>
          </div>
        )}
        
        {reviews.length === 0 && bookedHotels.length === 0 && (
          <div className="no-reviews-found">
            <h5>Nothing to review yet. Let's change that!</h5>
            <p>The world awaits. Book a trip now.</p>
            <button
              onClick={() => router.push("/")}
              className="getStartedBtn"
            >
              Get Started
            </button>
          </div>
        )}
      </div>

  );
};

export default Reviews;