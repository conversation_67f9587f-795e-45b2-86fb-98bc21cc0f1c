@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as *;

.hotel-filter-container {
  width: 261px; /* Set exact width */
  position: relative; /* Not sticky by default */
  height: auto; /* Allow content to determine height */
  max-height: calc(100vh - 30px); /* Maximum height based on viewport */
  overflow: visible;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  z-index: z-index(base);
  background-color: #fff;
  display: block;

  &.fixed {
    position: fixed;
    top: 15px;
    width: 261px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: z-index(dropdown-1); /* Ensure it's above other content */
  }

  // @media (max-width: $breakpoint-md) {
  //   height: calc(100% - 50px);
  //   overflow-y: auto;
  // }

  @media (max-width: $isMobile) {
    width: 100%;
    border: none;
  }

  .filter-body {
    max-height: calc(100vh - 128px); /* Adjusted for better scrolling */
    overflow-y: auto;
    /* Ensure smooth scrolling */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    padding-right: 5px;
    /* Prevent content from being cut off */
    padding-bottom: 20px;

    @media (max-width: $isMobile) {
      max-height: calc(100vh - 243px);
    }

    .view-more {
      color: $secondary_color;
      padding: 8px 0;
      font-size: 13px;
      font-weight: 500;
      display: flex;
      flex-direction: row;
      align-items: center;
      cursor: pointer;
      transition: color 0.2s ease;

      .fa-solid {
        margin-right: 3px;
      }

      &:hover {
        color: color.adjust($secondary_color, $lightness: -20%);
      }
    }

    .fixed & {
      /* Adjust max-height when fixed to ensure it fits in viewport */
      max-height: calc(100vh - 50px);
    }

    .type-filter {
      min-width: 232px;
      height: auto;
      padding: 12px 16px 8px 13px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      &:last-child {
        border-bottom: none;
      }

      .head {
        font-size: 14px;
        font-weight: 700;
      }

      .filters {
        display: flex;
        flex-direction: column;
        cursor: pointer;

        .filter-item {
          padding: 5px 0;
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 6px;

          .checkBox {
            input[type="checkbox"] {
              transform: scale(1.3);
            }
          }

          .content {
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .label {
              font-size: 13px;
              color: $filter_black1;
            }

            .count {
              font-size: 12px;
              color: $filter_black2;
            }
          }
        }
      }
    }

    // .budgetSlider {
    //       padding: 12px 16px 8px 13px;

    //   border-radius: 8px;
    //   background-color: #fff;
    //       border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    //   h3 {
    //     font-size: 14px;
    //     font-weight: bold;
    //     color: #000;
    //     margin-bottom: 12px;
    //   }

    //   p {
    //     font-size: 14px;
    //     color: #333;
    //     margin-bottom: 15px;
    //   }

    //   .sliderContainer {
    //     position: relative;
    //     height: 50px;
    //     padding-top: 5px;
    //     top: 14px;

    //    .histogramBackground {
    //   position: absolute;
    //   top: -32px;
    //   left: 0;
    //   right: 0;
    //   height: 100%;
    //   background: linear-gradient(
    //     to right,
    //     rgba(100, 100, 100, 0.25) 0%,
    //     rgba(70, 70, 70, 0.15) 100%
    //   );
    //   pointer-events: none;
    //   mask-image: url("data:image/svg+xml;utf8,\
    // <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 120 40'>\
    // <path d='\
    // M-23 40V36h3V40\
    // M-21 40V34h3V40\
    // M-19 40V34h3V40\
    // M-17 40V36h3V40\
    // M-15 40V34h3V40\
    // M-13 40V34h3V40\
    // M-12 40V34h3V40\
    // M-11 40V34h3V40\
    // M-9 40V34h3V40\
    // M-7 40V30h3V40\
    // M-5 40V32h3V40\
    // M-3 40V30h3V40\
    // M-2 40V28h3V40\
    // M-1 40V28h3V40\
    // M0 40V8h3V40\
    // M3 40V22h3V40\
    // M6 40V35h3V40\
    // M9 40V12h3V40\
    // M12 40V28h3V40\
    // M15 40V5h3V40\
    // M18 40V18h3V40\
    // M21 40V31h3V40\
    // M24 40V14h3V40\
    // M27 40V25h3V40\
    // M30 40V9h3V40\
    // M33 40V37h3V40\
    // M36 40V19h3V40\
    // M39 40V26h3V40\
    // M42 40V13h3V40\
    // M45 40V22h3V40\
    // M48 40V30h3V40\
    // M51 40V18h3V40\
    // M54 40V35h3V40\
    // M57 40V10h3V40\
    // M60 40V28h3V40\
    // M63 40V17h3V40\
    // M66 40V31h3V40\
    // M69 40V8h3V40\
    // M72 40V26h3V40\
    // M75 40V19h3V40\
    // M78 40V12h3V40\
    // M81 40V30h3V40\
    // M84 40V22h3V40\
    // M87 40V5h3V40\
    // M90 40V35h3V40\
    // M93 40V14h3V40\
    // M96 40V25h3V40\
    // M99 40V18h3V40\
    // M102 40V31h3V40\
    // M105 40V13h3V40\
    // M108 40V28h3V40\
    // M111 40V9h3V40\
    // M114 40V21h3V40\
    // M116 40V11h3V40\
    // M119 40V16h3V40\
    // M121 40V26h3V40\
    // M123 40V28h3V40\
    // M126 40V30h3V40\
    // M128 40V30h3V40\
    // M130 40V34h3V40\
    // M132 40V34h3V40\
    // M134 40V34h3V40\
    // M136 40V34h3V40\
    // M138 40V34h3V40\
    // M140 40V34h3V40\
    // M142 40V34h3V40\
    // M1 40V16h3V40' fill='%23000'/>\
    // </svg>");
    //   mask-size: 100% 100%;
    //   mask-repeat: no-repeat;
    // }

    //     .sliderTrack {
    //       position: absolute;
    //       top: 15px;
    //       left: 0;
    //       right: 0;
    //       height: 4px;
    //       background: #e0e0e0;
    //       border-radius: 2px;
    //       z-index: 0;
    //     }

    //     .sliderProgress {
    //       position: absolute;
    //       top: 15px;
    //       height: 4px;
    //       background: #0066ff;
    //       border-radius: 2px;
    //       z-index: 1;
    //     }
    //   }

    //   /* Base slider styles */
    //   .slider {
    //     -webkit-appearance: none;
    //     appearance: none;
    //     position: absolute;
    //     top: 7px;
    //     width: 100%;
    //     height: 4px;
    //     background: transparent;
    //     border-radius: 2px;
    //     outline: none;
    //     margin: 0;
    //     padding: 0;
    //     /* Critical fix for Firefox */
    //     pointer-events: none;
    //   }

    //   /* Fixing pointer events for all browsers */
    //   .slider::-webkit-slider-thumb {
    //     -webkit-appearance: none;
    //     appearance: none;
    //     width: 20px;
    //     height: 20px;
    //     border-radius: 50%;
    //     background: #0066ff;
    //     cursor: grab;
    //     border: 2px solid #fff;
    //     box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    //     pointer-events: auto;
    //     z-index: 5;
    //   }

    //   .slider::-moz-range-thumb {
    //     width: 20px;
    //     height: 20px;
    //     border-radius: 50%;
    //     background: #0066ff;
    //     cursor: grab;
    //     border: 2px solid #fff;
    //     box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    //     pointer-events: auto;
    //     z-index: 5;
    //   }

    //   /* Track appearance */
    //   .slider::-webkit-slider-runnable-track {
    //     width: 100%;
    //     height: 4px;
    //     cursor: pointer;
    //     background: transparent;
    //     border-radius: 2px;
    //   }

    //   .slider::-moz-range-track {
    //     width: 100%;
    //     height: 4px;
    //     cursor: pointer;
    //     background: transparent;
    //     border-radius: 2px;
    //   }

    //   /* Specific slider styles */
    //   .minSlider {
    //     z-index: 5; /* Higher z-index for min slider */
    //     pointer-events: auto; /* Enable pointer events only on thumb */
    //   }

    //   .maxSlider {
    //     z-index: 4;
    //     pointer-events: auto; /* Enable pointer events only on thumb */
    //   }

    //   /* Active state styles */
    //   .slider:active::-webkit-slider-thumb {
    //     cursor: grabbing;
    //     transform: scale(1.1);
    //   }

    //   .slider:active::-moz-range-thumb {
    //     cursor: grabbing;
    //     transform: scale(1.1);
    //   }
    // }

    .washroom-counter {
      padding: 12px 16px 8px 13px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      .counter {
        padding: 5px 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .label {
          font-size: 13px;
          color: $filter_black1;
        }

        .counter-buttons {
          display: flex;
          flex-direction: row;
          align-items: center;
          border: 1px solid rgba(0, 0, 0, 0.4);
          border-radius: 4px;

          .btn {
            height: 40px;
            width: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 22px;
            cursor: pointer;
            color: $primary_color;
            transition: background-color 0.2s ease;

            user-select: none;
            outline: none;
            border: none; // Ensure no border styles override this
            -webkit-tap-highlight-color: transparent; // Removes blue highlight on mobile

            &.inactive {
              color: $black_color4;
              cursor: not-allowed;
            }

            &:not(.inactive):hover {
              background-color: rgba(8, 119, 103, 0.05);
            }
          }
          .count {
            width: 36px;
            display: flex;
            justify-content: center;
            font-size: 14px;
            color: #868686;
            font-weight: 700;
            cursor: text;
          }
        }
      }
    }
  }

  .mobileFilterResetBtnWrapper{
    display: flex;
    justify-content: end;
    padding-right: 15px;

     .mobileFilterResetBtn {
    display: none;

    @media (max-width: $isMobile) {
      display: block;
      font-size: 12px;
      color: rgb(239.4, 233, 227.6);
      font-weight: 600;
      cursor: not-allowed;
      transition: color 0.2s ease-in-out;
    

      &.enabled {
        color: $primary-color;
        cursor: pointer;
      }
    }
  }
  }

 

  .filter-head {
    padding: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    @media (max-width: $isMobile) {
      display: none;
    }

    h3 {
      font-size: 16px;
      font-weight: 700;
    }

    .resetBtn {
      font-size: 12px;
      color: rgb(239.4, 233, 227.6);
      font-weight: 600;
      cursor: not-allowed;
      transition: color 0.2s ease-in-out;

      &.enabled {
        color: $primary-color;
        cursor: pointer;
      }
    }
  }
}

.head {
  font-size: 14px;
  font-weight: 700;
}
