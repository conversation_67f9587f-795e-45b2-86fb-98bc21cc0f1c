import React from "react";
import {
  Wifi,
  Car,
  Snowflake,
  Coffee,
  Waves,
  PawPrint,
  Utensils,
  Dumbbell,
  Bed,
  Bath,
  Tv,
  Phone,
  Shield,
  Clock,
  Users,
  Cigarette,
  Baby,
  Accessibility,
  CreditCard,
  Shirt,
  Wind,
  Thermometer,
  MapPin,
  Building,
  TreePine,
  Mountain,
  Plane,
  Car as CarIcon,
  Bus,
  Train,
  Ship,
  Briefcase,
  Globe,
  Camera,
  Music,
  Play,
  ShoppingBag,
  Scissors,
  Heart,
  Stethoscope,
  CheckCircle,
  Home,
  Hotel as HotelIcon,
  Building2,
  Castle,
  Palmtree,
  Sparkles,
  UtensilsCrossed,
  Zap,
  Activity
} from "lucide-react";

export interface AmenityConfig {
  name: string;
  icon: any;
  category: 'connectivity' | 'transportation' | 'comfort' | 'dining' | 'recreation' | 'business' | 'accessibility' | 'safety' | 'services' | 'location';
  description?: string;
}

export const AMENITIES_CONFIG: Record<string, AmenityConfig> = {
  // Exact matches for common API amenities first
  'free wi-fi': { name: 'Free Wi-Fi', icon: Wifi, category: 'connectivity' },
  'swimming pool': { name: 'Swimming Pool', icon: Waves, category: 'recreation' },
  'free parking': { name: 'Free Parking', icon: Car, category: 'transportation' },
  'air conditioning': { name: 'Air Conditioning', icon: Snowflake, category: 'comfort' },
  'breakfast included': { name: 'Breakfast Included', icon: Coffee, category: 'dining' },
  'bar & lounge': { name: 'Bar & Lounge', icon: Coffee, category: 'dining' },
  'spa services': { name: 'Spa Services', icon: Sparkles, category: 'recreation' },
  'conference room': { name: 'Conference Room', icon: Users, category: 'business' },
  'room service': { name: 'Room Service', icon: Utensils, category: 'dining' },
  'fitness center': { name: 'Fitness Center', icon: Activity, category: 'recreation' },
  'beach access': { name: 'Beach Access', icon: Waves, category: 'location' },
  'live music': { name: 'Live Music', icon: Music, category: 'recreation' },
  'jacuzzi': { name: 'Jacuzzi', icon: Waves, category: 'recreation' },

  // Connectivity & Technology
  'wifi': { name: 'Free WiFi', icon: Wifi, category: 'connectivity', description: 'Complimentary wireless internet access' },
  'free wifi': { name: 'Free WiFi', icon: Wifi, category: 'connectivity' },
  'internet': { name: 'Internet Access', icon: Wifi, category: 'connectivity' },
  'wireless internet': { name: 'Wireless Internet', icon: Wifi, category: 'connectivity' },
  'high speed internet': { name: 'High Speed Internet', icon: Wifi, category: 'connectivity' },
  'television': { name: 'Television', icon: Tv, category: 'comfort' },
  'tv': { name: 'TV', icon: Tv, category: 'comfort' },
  'cable tv': { name: 'Cable TV', icon: Tv, category: 'comfort' },
  'satellite tv': { name: 'Satellite TV', icon: Tv, category: 'comfort' },
  'telephone': { name: 'Telephone', icon: Phone, category: 'connectivity' },
  'phone': { name: 'Phone', icon: Phone, category: 'connectivity' },

  // Transportation & Parking
  'parking': { name: 'Parking', icon: Car, category: 'transportation', description: 'Vehicle parking available' },
  'free parking': { name: 'Free Parking', icon: Car, category: 'transportation' },
  'valet parking': { name: 'Valet Parking', icon: Car, category: 'transportation' },
  'self parking': { name: 'Self Parking', icon: Car, category: 'transportation' },
  'garage': { name: 'Garage', icon: Building, category: 'transportation' },
  'airport shuttle': { name: 'Airport Shuttle', icon: Plane, category: 'transportation' },
  'shuttle service': { name: 'Shuttle Service', icon: Bus, category: 'transportation' },
  'car rental': { name: 'Car Rental', icon: CarIcon, category: 'transportation' },
  'taxi service': { name: 'Taxi Service', icon: CarIcon, category: 'transportation' },
  'metro access': { name: 'Metro Access', icon: Train, category: 'transportation' },

  // Climate Control & Comfort
  'air conditioning': { name: 'Air Conditioning', icon: Snowflake, category: 'comfort', description: 'Climate controlled rooms' },
  'ac': { name: 'Air Conditioning', icon: Snowflake, category: 'comfort' },
  'air conditioner': { name: 'Air Conditioner', icon: Snowflake, category: 'comfort' },
  'heating': { name: 'Heating', icon: Thermometer, category: 'comfort' },
  'climate control': { name: 'Climate Control', icon: Wind, category: 'comfort' },
  'fan': { name: 'Fan', icon: Wind, category: 'comfort' },

  // Recreation & Entertainment
  'swimming pool': { name: 'Swimming Pool', icon: Waves, category: 'recreation', description: 'Pool facilities available' },
  'pool': { name: 'Pool', icon: Waves, category: 'recreation' },
  'outdoor pool': { name: 'Outdoor Pool', icon: Waves, category: 'recreation' },
  'indoor pool': { name: 'Indoor Pool', icon: Waves, category: 'recreation' },
  'fitness center': { name: 'Fitness Center', icon: Activity, category: 'recreation' },
  'gym': { name: 'Gym', icon: Dumbbell, category: 'recreation' },
  'fitness centre': { name: 'Fitness Centre', icon: Activity, category: 'recreation' },
  'spa': { name: 'Spa', icon: Sparkles, category: 'recreation' },
  'sauna': { name: 'Sauna', icon: Thermometer, category: 'recreation' },
  'jacuzzi': { name: 'Jacuzzi', icon: Waves, category: 'recreation' },
  'hot tub': { name: 'Hot Tub', icon: Waves, category: 'recreation' },
  'tennis court': { name: 'Tennis Court', icon: Play, category: 'recreation' },
  'golf course': { name: 'Golf Course', icon: TreePine, category: 'recreation' },
  'game room': { name: 'Game Room', icon: Play, category: 'recreation' },
  'entertainment': { name: 'Entertainment', icon: Music, category: 'recreation' },

  // Dining & Food Services
  'restaurant': { name: 'Restaurant', icon: UtensilsCrossed, category: 'dining', description: 'On-site dining available' },
  'breakfast': { name: 'Breakfast', icon: Coffee, category: 'dining' },
  'free breakfast': { name: 'Free Breakfast', icon: Coffee, category: 'dining' },
  'continental breakfast': { name: 'Continental Breakfast', icon: Coffee, category: 'dining' },
  'buffet': { name: 'Buffet', icon: UtensilsCrossed, category: 'dining' },
  'room service': { name: 'Room Service', icon: Utensils, category: 'dining' },
  'bar': { name: 'Bar', icon: Coffee, category: 'dining' },
  'lounge': { name: 'Lounge', icon: Coffee, category: 'dining' },
  'coffee shop': { name: 'Coffee Shop', icon: Coffee, category: 'dining' },
  'cafe': { name: 'Cafe', icon: Coffee, category: 'dining' },
  'kitchen': { name: 'Kitchen', icon: Utensils, category: 'dining' },
  'kitchenette': { name: 'Kitchenette', icon: Utensils, category: 'dining' },
  'minibar': { name: 'Minibar', icon: Coffee, category: 'dining' },
  'refrigerator': { name: 'Refrigerator', icon: Snowflake, category: 'dining' },

  // Business & Work
  'business center': { name: 'Business Center', icon: Briefcase, category: 'business' },
  'meeting room': { name: 'Meeting Room', icon: Users, category: 'business' },
  'conference room': { name: 'Conference Room', icon: Users, category: 'business' },
  'work desk': { name: 'Work Desk', icon: Briefcase, category: 'business' },
  'desk': { name: 'Desk', icon: Briefcase, category: 'business' },
  'printer': { name: 'Printer', icon: Briefcase, category: 'business' },
  'fax': { name: 'Fax', icon: Phone, category: 'business' },
  'copier': { name: 'Copier', icon: Briefcase, category: 'business' },

  // Safety & Security
  'safe': { name: 'Safe', icon: Shield, category: 'safety' },
  'security': { name: 'Security', icon: Shield, category: 'safety' },
  '24 hour security': { name: '24 Hour Security', icon: Shield, category: 'safety' },
  'cctv': { name: 'CCTV', icon: Camera, category: 'safety' },
  'fire safety': { name: 'Fire Safety', icon: Shield, category: 'safety' },
  'smoke detector': { name: 'Smoke Detector', icon: Shield, category: 'safety' },
  'first aid': { name: 'First Aid', icon: Stethoscope, category: 'safety' },

  // Services
  'concierge': { name: 'Concierge', icon: Users, category: 'services' },
  '24 hour front desk': { name: '24 Hour Front Desk', icon: Clock, category: 'services' },
  'front desk': { name: 'Front Desk', icon: Users, category: 'services' },
  'housekeeping': { name: 'Housekeeping', icon: Shirt, category: 'services' },
  'laundry': { name: 'Laundry', icon: Shirt, category: 'services' },
  'dry cleaning': { name: 'Dry Cleaning', icon: Shirt, category: 'services' },
  'ironing': { name: 'Ironing', icon: Shirt, category: 'services' },
  'wake up service': { name: 'Wake Up Service', icon: Clock, category: 'services' },
  'luggage storage': { name: 'Luggage Storage', icon: Briefcase, category: 'services' },
  'currency exchange': { name: 'Currency Exchange', icon: CreditCard, category: 'services' },
  'tour desk': { name: 'Tour Desk', icon: MapPin, category: 'services' },
  'ticket service': { name: 'Ticket Service', icon: CreditCard, category: 'services' },

  // Accessibility
  'wheelchair accessible': { name: 'Wheelchair Accessible', icon: Accessibility, category: 'accessibility' },
  'accessible': { name: 'Accessible', icon: Accessibility, category: 'accessibility' },
  'elevator': { name: 'Elevator', icon: Building2, category: 'accessibility' },
  'lift': { name: 'Lift', icon: Building2, category: 'accessibility' },
  'ramp': { name: 'Ramp', icon: Accessibility, category: 'accessibility' },

  // Pet & Family
  'pet friendly': { name: 'Pet Friendly', icon: PawPrint, category: 'services', description: 'Pets welcome' },
  'pets allowed': { name: 'Pets Allowed', icon: PawPrint, category: 'services' },
  'pet': { name: 'Pet Friendly', icon: PawPrint, category: 'services' },
  'baby crib': { name: 'Baby Crib', icon: Baby, category: 'services' },
  'crib': { name: 'Crib', icon: Baby, category: 'services' },
  'babysitting': { name: 'Babysitting', icon: Baby, category: 'services' },
  'family room': { name: 'Family Room', icon: Users, category: 'comfort' },

  // Room Features
  'balcony': { name: 'Balcony', icon: Home, category: 'comfort' },
  'terrace': { name: 'Terrace', icon: Home, category: 'comfort' },
  'patio': { name: 'Patio', icon: Home, category: 'comfort' },
  'garden view': { name: 'Garden View', icon: TreePine, category: 'location' },
  'sea view': { name: 'Sea View', icon: Waves, category: 'location' },
  'mountain view': { name: 'Mountain View', icon: Mountain, category: 'location' },
  'city view': { name: 'City View', icon: Building2, category: 'location' },
  'private bathroom': { name: 'Private Bathroom', icon: Bath, category: 'comfort' },
  'bathroom': { name: 'Bathroom', icon: Bath, category: 'comfort' },
  'shower': { name: 'Shower', icon: Bath, category: 'comfort' },
  'bathtub': { name: 'Bathtub', icon: Bath, category: 'comfort' },
  'hair dryer': { name: 'Hair Dryer', icon: Wind, category: 'comfort' },

  // Smoking
  'non smoking': { name: 'Non Smoking', icon: Cigarette, category: 'comfort' },
  'smoking allowed': { name: 'Smoking Allowed', icon: Cigarette, category: 'comfort' },
  'smoking room': { name: 'Smoking Room', icon: Cigarette, category: 'comfort' },

  // Shopping & Retail
  'shopping': { name: 'Shopping', icon: ShoppingBag, category: 'services' },
  'gift shop': { name: 'Gift Shop', icon: ShoppingBag, category: 'services' },
  'souvenir shop': { name: 'Souvenir Shop', icon: ShoppingBag, category: 'services' },

  // Beauty & Wellness
  'salon': { name: 'Salon', icon: Scissors, category: 'services' },
  'beauty salon': { name: 'Beauty Salon', icon: Scissors, category: 'services' },
  'barber': { name: 'Barber', icon: Scissors, category: 'services' },
  'massage': { name: 'Massage', icon: Heart, category: 'recreation' },

  // Default/Fallback
  'default': { name: 'Amenity', icon: CheckCircle, category: 'services' }
};

// Helper function to get amenity configuration
export const getAmenityConfig = (amenityName: string): AmenityConfig => {
  const normalizedName = amenityName.toLowerCase().trim();

  // Direct exact match (highest priority)
  if (AMENITIES_CONFIG[normalizedName]) {
    return AMENITIES_CONFIG[normalizedName];
  }

  // Try exact match with common variations
  const variations = [
    normalizedName.replace(/\s+/g, ' '), // normalize spaces
    normalizedName.replace(/&/g, 'and'), // & to and
    normalizedName.replace(/and/g, '&'), // and to &
  ];

  for (const variation of variations) {
    if (AMENITIES_CONFIG[variation]) {
      return AMENITIES_CONFIG[variation];
    }
  }

  // More conservative partial matching - only for meaningful matches
  const matches: { key: string; config: AmenityConfig; score: number }[] = [];

  for (const [key, config] of Object.entries(AMENITIES_CONFIG)) {
    if (key === 'default') continue; // Skip default in partial matching

    // Only do partial matching if the key is substantial (> 4 chars)
    if (key.length > 4 && normalizedName.includes(key)) {
      // Score based on how much of the amenity name is matched
      const score = key.length / normalizedName.length;
      matches.push({ key, config, score });
    }
  }

  // Return the best match (highest score) only if it's a good match (score > 0.5)
  if (matches.length > 0) {
    matches.sort((a, b) => b.score - a.score);
    if (matches[0].score > 0.5) {
      return matches[0].config;
    }
  }

  // Fallback to default
  return AMENITIES_CONFIG['default'];
};

// Helper function to get amenity icon component
export const getAmenityIcon = (amenityName: string, size: number = 16, className: string = "mr-2 text-gray-500 flex-shrink-0") => {
  const config = getAmenityConfig(amenityName);
  const IconComponent = config.icon;
  return <IconComponent size={size} className={className} />;
};

// Helper function to get amenities by category
export const getAmenitiesByCategory = (category: AmenityConfig['category']): AmenityConfig[] => {
  return Object.values(AMENITIES_CONFIG).filter(config => config.category === category);
};

// Helper function to get all categories
export const getAllCategories = (): AmenityConfig['category'][] => {
  return ['connectivity', 'transportation', 'comfort', 'dining', 'recreation', 'business', 'accessibility', 'safety', 'services', 'location'];
};
