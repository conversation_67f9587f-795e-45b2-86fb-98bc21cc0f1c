'use client';

import React, { useEffect } from 'react';
import { useLanguage } from '../contexts/languageContext';

interface LanguageLayoutWrapperProps {
  children: React.ReactNode;
}

const LanguageLayoutWrapper: React.FC<LanguageLayoutWrapperProps> = ({ children }) => {
  const { isRTL, currentLanguage, fontFamily } = useLanguage();

  useEffect(() => {
    // Set the HTML dir attribute based on the language direction
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;

    // Apply the font family from context
    document.body.style.fontFamily = fontFamily;
  }, [isRTL, currentLanguage, fontFamily]);

  return <>{children}</>;
};

export default LanguageLayoutWrapper;
