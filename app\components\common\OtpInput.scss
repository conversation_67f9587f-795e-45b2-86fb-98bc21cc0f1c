@use "/styles/variable" as *;

.otp-input-container {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;

  .otp-input {
    width: 48px;
    height: 48px;
    text-align: center;
    font-size: 18px;
    border: 1px solid $border-color;
    border-radius: 8px;
    transition: all 0.2s ease;
    caret-color: $primary-color;

    &:focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
      outline: none;
    }

    &.filled {
      border-color: $primary-color;
    }

    &:disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
      opacity: 0.7;
    }
  }

  // Responsive adjustments
  @media (max-width: $breakpoint-sm) {
    gap: 8px;
    
    .otp-input {
      width: 40px;
      height: 40px;
      font-size: 16px;
    }
  }

  // RTL support
  html[dir="rtl"] & {
    .otp-input {
      // No specific RTL adjustments needed for inputs
    }
  }
}
