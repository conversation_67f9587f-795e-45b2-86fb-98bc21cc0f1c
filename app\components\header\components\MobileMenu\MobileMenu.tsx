"use client";
import React from "react";
import { useTranslation } from "@/app/hooks/useTranslation";
import './MobileMenu.scss'

interface MobileMenuModalProps {
  isOpen: boolean;
  handleClose: () => void;
  children: React.ReactNode;
  title?: string;
  component_name?: string;
  isRTL?: boolean;
}

const MobileMenuModal: React.FC<MobileMenuModalProps> = ({ isOpen, handleClose, children, title, component_name, isRTL }) => {
  const { t } = useTranslation("common");
  return (
    <div onClick={handleClose} className={`slide-from-right-modal-mobile ${isOpen ? "show" : ""} ${isRTL ? "rtl" : ""}`}>
      <div className={`modal-content ${component_name === "CancellationPolicy" ? 'cancellation-policy-modal' : ''}`} onClick={(e) => e.stopPropagation()}>
        <div className="header">
          <h2>{t("common.menu", "Menu")}</h2>
          <div className="close-btn" onClick={handleClose}>
            <i className="fa-solid fa-xmark"></i>
          </div>
        </div>

        <div className="content">{children}</div>
      </div>
    </div>
  );
};

export default MobileMenuModal;
