import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next';
// import resourcesToBackend from 'i18next-resources-to-backend';

// Import translation resources
import enCommon from './public/locales/en/common.json';
import arCommon from './public/locales/ar/common.json';
import esCommon from './public/locales/es/common.json';
import frCommon from './public/locales/fr/common.json';
import hiCommon from './public/locales/hi/common.json';

// Define language mapping for display names
export const languageMap: Record<string, string> = {
  en: 'English',
  ar: 'العربية',
  es: 'Español',
  fr: 'Français',
  hi: 'हिन्दी', // Hindi
};

// Define language codes
export const languages = ['en', 'ar', 'es', 'fr', 'hi'];

// Define flag mapping for languages
export const languageFlags: Record<string, string> = {
  en: '/assets/img/country-logo/gb.webp', // UK flag for English
  ar: '/assets/img/country-logo/sa.webp', // Saudi Arabia flag for Arabic
  es: '/assets/img/country-logo/es.webp', // Spain flag for Spanish
  fr: '/assets/img/country-logo/fr.webp', // France flag for French
  hi: '/assets/img/country-logo/in.webp', // India flag for Hindi
};

// Create resources object
const resources = {
  en: {
    common: enCommon
  },
  ar: {
    common: arCommon
  },
  es: {
    common: esCommon
  },
  fr: {
    common: frCommon
  },
  hi: {
    common: hiCommon
  }
};

// Create i18next instance
const i18nInstance = createInstance();

// Initialize i18next
i18nInstance
  .use(initReactI18next)
  .init({
    lng: 'en', // Default language
    fallbackLng: 'en',
    supportedLngs: languages,
    defaultNS: 'common',
    fallbackNS: 'common',
    resources,
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    react: {
      useSuspense: false,
    },
  });

export default i18nInstance;
