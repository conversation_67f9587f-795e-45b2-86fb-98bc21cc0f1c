"use client";
import React, { useCallback, useEffect, useState, useRef } from "react";
import "./BookingPreviewPage.scss";
import FareSummary from "./components/FareSummary/FareSummary";
import OffersSection from "./components/OffersSection/OffersSection";
import Link from "next/link";
import GuestDetailsForm, { GuestDetailsFormRef } from "./components/GuestDetailsForm/GuestDetailsForm";
import PropertyDetails from "./components/PropertyDetails/PropertyDetails";
import CancellationPolicy from "./components/CancellationPolicy/CancellationPolicy";
import SpecialRequest from "./components/SpecialRequest/SpecialRequest";
import MobileBookingHeader from "./components/MobileBookingHeader/MobileBookingHeader";
import FareSummaryShimmer from "./components/FareSummaryShimmer/FareSummaryShimmer";
import Image from "next/image";
import OffersSectonShimmr from "./components/OffersSectionShimmer/OffersSectonShimmr";
import GuestDetailsFormShimmer from "./components/GuestDetailsFormShimmer/GuestDetailsFormShimmer";
import PropertyDetailsShimmer from "./components/ProperyDetailsShimmer/PropertyDetailsShimmer";
import axios from "axios";
import useScrollLock from "../components/utilities/ScrollLock/useScrollLock";
import { useRouter } from "next/navigation";
import { BookingDetail } from "./hotel-booking-details.model";
import { useCommonContext } from "../contexts/commonContext";
import { ShieldCheck } from "lucide-react";
import visa from 'public/assets/img/payment-methods/visa.webp'
import master from 'public/assets/img/payment-methods/mastercard.webp'
import paypal from 'public/assets/img/payment-methods/paypal.webp'


const API_URL = "/data/hotelbookingdetails.json";

function Page() {
  const navigation = useRouter();
  //const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  // const [cancellationBenefits, setCancellationBenefits] =
  //   useState<CancellationBenefits>();
  const [isMobileBenfitsActive, setIsMobileBenefitsActive] = useState<boolean>(false);
  const [hotelBookingDetails, setHotelBookingDetails] = useState<BookingDetail>();
  const {  setIsLoading, isLoading } = useCommonContext();

  // Ref for guest details form validation
  const guestDetailsFormRef = useRef<GuestDetailsFormRef>(null);



  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 780);
    };
    handleResize(); // initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }, []);


  const getHotelBookingDetails = useCallback(async () => {
    try{

      const response = await axios.get(API_URL);
      console.log("booking details response", response);
      setHotelBookingDetails(response.data.data.bookingDetail);

    }catch (err) {
      console.error(err);


    }finally {
      setIsLoading(false);

    }

  }, [setHotelBookingDetails, setIsLoading])

  useEffect(() => {
    getHotelBookingDetails();

  }, [getHotelBookingDetails])


  useScrollLock(isMobileBenfitsActive);

  // Handle Pay Now button click with validation
  const handlePayNowClick = () => {
    if (guestDetailsFormRef.current) {
      const isValid = guestDetailsFormRef.current.validateForm();

      if (isValid) {
        // Get form data if needed
        const formData = guestDetailsFormRef.current.getFormData();
        console.log("Form is valid, proceeding to payment:", formData);

        // Navigate to booking confirmation
        navigation.push('/BookingConfirmation');
      } else {
        console.log("Form validation failed");
        // Scroll to the first error (optional)
        const firstErrorElement = document.querySelector('.error-message');
        if (firstErrorElement) {
          firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    }
  };

  if (isMobile) {
    return (
      <div className="booking-preview-page">
        {/* Mobile Header - Outside container for proper sticky behavior */}
        {!isLoading && (
          <MobileBookingHeader
            name={hotelBookingDetails?.name}
            travelDetails={hotelBookingDetails?.travelDetails}
          />
        )}

        <div className="booking-preview-container common-container mobile-order">
          {isLoading ? (
            <>
              <PropertyDetailsShimmer />
              <OffersSectonShimmr />
              <FareSummaryShimmer />
              <GuestDetailsFormShimmer />
            </>
          ) : (
            <>
              <PropertyDetails setIsMobileBenefitsActive={setIsMobileBenefitsActive} thumbnailImageURL={hotelBookingDetails?.thumbnailImageURL} name={hotelBookingDetails?.name} address={hotelBookingDetails?.address} starRating={hotelBookingDetails?.starRating} travelDetails={hotelBookingDetails?.travelDetails} roomDetail={hotelBookingDetails?.roomDetail} roomBenefits={hotelBookingDetails?.roomBenefits} />

              <CancellationPolicy cancellationBenefits={hotelBookingDetails?.cancellationBenefits} />

              <OffersSection />
              <FareSummary fareDetail={hotelBookingDetails?.fareDetail} travelDetails={hotelBookingDetails
                ?.travelDetails
              } />
              <CancellationPolicy cancellationBenefits={hotelBookingDetails?.cancellationBenefits} />
              <GuestDetailsForm ref={guestDetailsFormRef} />
              <SpecialRequest />
              <div className="payment-disclaimer-section">
                <p>
                  By clicking on Pay Now/Book Now, I confirm that I have read,
                  understood, and agree with the{" "}
                  <Link href={"#"}>Cancellation Policy</Link>,{" "}
                  <Link href={"#"}>Privacy Policy</Link> and{" "}
                  <Link href={"#"}>User Agreement</Link>.
                </p>
                <p>
                  Please note that KindAli will not provide a tax invoice. You
                  will be given a commercial receipt to serve as proof of
                  transaction.
                </p>
                <div className="bg-gray-100 p-4 rounded-lg shadow-sm">
                  <p className="text-gray-700 text-center font-medium mb-3 flex items-center justify-center gap-2">
                    <ShieldCheck className="h-5 w-5 text-green-600" />
                    100% Safe Payment Process
                  </p>
                  <div className="flex justify-center items-center gap-4">
                    {/* Payment method logos */}
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={visa.src} alt="Visa" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={master.src} alt="Mastercard" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={paypal.src} alt="PayPal" className="h-8 w-auto" />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 text-center mt-2">
                    All payment methods are secure and encrypted
                  </p>
                </div>
              </div>
              <div className="booking-preview-footer-bottom-0">
                <div className="totalAmtLabelValue">
                  <p className="value">₹1,32,868.<span className="xs-text">
                  70
                  </span></p>

                  <span className="label">Total amount</span>
                </div>
                <button className="footer-btn2" onClick={handlePayNowClick}>Pay Now</button>
              </div>
            </>
          )}
        </div>


        <div className={`mobile-bottom-to-top-modal-container ${isMobileBenfitsActive ? 'active' : ''}`}>

               <div className="mobile-bottom-to-top-modal-header">
          <div
            className="mobile-bottom-to-top-modal-header__close-bttn"
            onClick={() => {
              setIsMobileBenefitsActive(false);
            }}
          >
            <i className="fa-solid fa-xmark"></i>
          </div>
              <h3 className="mobile-bottom-to-top-modal-heading">Room Benefits:</h3>
        </div>



     <div className="mobile-bottom-to-top-content">

     <div className="room-benefits-mobile-container " style={{ borderTop: '1px solid rgba(0, 0, 0, 0.1)'}}>
       <div className="mobile-room-benefits">
         <div className="mobile-room-benefits__list">
           {hotelBookingDetails?.roomBenefits ? (
             <>
               {/* Render meal benefits if they exist */}
               {hotelBookingDetails.roomBenefits.mealBenefits && hotelBookingDetails.roomBenefits.mealBenefits.map((benefit: string, index: number) => (
                 <div key={`meal-${index}`} className="list-item">
                   <i className="fa-solid fa-check"></i>
                   <span className="label">{benefit}</span>
                 </div>
               ))}

               {/* Render other benefits */}
               {hotelBookingDetails.roomBenefits.otherBenefits && hotelBookingDetails.roomBenefits.otherBenefits.map((benefit: string, index: number) => (
                 <div key={`other-${index}`} className="list-item">
                   <i className="fa-solid fa-check"></i>
                   <span className="label">{benefit}</span>
                 </div>
               ))}
             </>
           ) : (
             // Fallback content if no dynamic data
             <>
               <div className="list-item">
                 <i className="fa-solid fa-check"></i>
                 <span className="label">Food & Beverage Discount 10%</span>
               </div>
               <div className="list-item">
                 <i className="fa-solid fa-check"></i>
                 <span className="label">Parking</span>
               </div>
               <div className="list-item">
                 <i className="fa-solid fa-check"></i>
                 <span className="label">Welcome drink</span>
               </div>
               <div className="list-item">
                 <i className="fa-solid fa-check"></i>
                 <span className="label">Express check-in</span>
               </div>
               <div className="list-item">
                 <i className="fa-solid fa-check"></i>
                 <span className="label">Free WiFi</span>
               </div>
               <div className="list-item">
                 <i className="fa-solid fa-check"></i>
                 <span className="label">Drinking water</span>
               </div>
             </>
           )}
         </div>
       </div>


                 </div>

     </div>






 </div>

      </div>
    );
  }

  // Default layout for desktop/tablet
  return (
    <div className="booking-preview-page">
      <div className="booking-preview-container common-container">
        <div className="left-section">
          {isLoading ? (
            <>
              <GuestDetailsFormShimmer />
               <PropertyDetailsShimmer />
            </>
          ) : (
            <>
              <GuestDetailsForm ref={guestDetailsFormRef} />
              <PropertyDetails thumbnailImageURL={hotelBookingDetails?.thumbnailImageURL} name={hotelBookingDetails?.name} address={hotelBookingDetails?.address} starRating={hotelBookingDetails?.starRating} travelDetails={hotelBookingDetails?.travelDetails} roomDetail={hotelBookingDetails?.roomDetail} roomBenefits={hotelBookingDetails?.roomBenefits} />
              <CancellationPolicy cancellationBenefits={hotelBookingDetails?.cancellationBenefits} />
              <SpecialRequest />
              <div className="booking-preview-footer">
                <button className="footer-btn" onClick={handlePayNowClick}>Pay Now</button>
              </div>
            </>
          )}
        </div>

        <div className="right-section">
          {isLoading ? (
            <>
              <FareSummaryShimmer /> <OffersSectonShimmr />
            </>
          ) : (
            <>
              <FareSummary fareDetail={hotelBookingDetails?.fareDetail} travelDetails={hotelBookingDetails
                ?.travelDetails} />
              <OffersSection />
              <div className="payment-disclaimer-section">
                <p>
                  By clicking on Pay Now/Book Now, I confirm that I have read,
                  understood, and agree with the{" "}
                  <Link href={"#"}>Cancellation Policy</Link>,{" "}
                  <Link href={"#"}>Privacy Policy</Link> and{" "}
                  <Link href={"#"}>User Agreement</Link>.
                </p>
                <p>
                  Please note that KindAli will not provide a tax invoice. You
                  will be given a commercial receipt to serve as proof of
                  transaction.
                </p>
                <div className="bg-gray-100 p-4 rounded-lg shadow-sm">
                  <p className="text-gray-700 text-center font-medium mb-3 flex items-center justify-center gap-2">
                    <ShieldCheck className="h-5 w-5 text-green-600" />
                    100% Safe Payment Process
                  </p>
                  <div className="flex justify-center items-center gap-4">
                    {/* Payment method logos */}
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={visa.src} alt="Visa" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={master.src} alt="Mastercard" className="h-8 w-auto" />
                    </div>
                    <div className="bg-white p-2 rounded-md shadow-sm">
                      <Image width={60} height={40} src={paypal.src} alt="PayPal" className="h-8 w-auto" />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 text-center mt-2">
                    All payment methods are secure and encrypted
                  </p>
                </div>
              </div>

               <div className="booking-preview-footer-bottom-0">
                <div className="totalAmtLabelValue">
                  <p className="value">₹1,32,868.<span className="xs-text">
                  70
                  </span></p>

                  <span className="label">Total amount</span>
                </div>
                <button className="footer-btn2" onClick={handlePayNowClick}>Pay Now</button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default Page;
