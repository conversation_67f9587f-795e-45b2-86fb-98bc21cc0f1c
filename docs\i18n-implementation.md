# Multi-Language Implementation Technical Documentation

This document provides detailed technical information about the internationalization (i18n) implementation in the KindAli Travel & Tourism web application.

## Technology Stack

- **i18next**: Core internationalization framework
- **react-i18next**: React bindings for i18next
- **next-i18next**: Next.js integration for i18next
- **i18next-resources-to-backend**: Backend for loading translation resources

## Configuration Files

### 1. Next.js Configuration (`next.config.ts`)

```typescript
// next.config.ts
const nextConfig: NextConfig = {
  i18n: {
    defaultLocale: 'en',
    locales: ['en', 'ar', 'es', 'fr'],
    localeDetection: false,
  },
  // other config options...
};
```

### 2. i18next Configuration (`i18n.ts`)

```typescript
// i18n.ts
import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next';
import resourcesToBackend from 'i18next-resources-to-backend';

// Import translation resources
import enCommon from './public/locales/en/common.json';
import arCommon from './public/locales/ar/common.json';
import esCommon from './public/locales/es/common.json';
import frCommon from './public/locales/fr/common.json';

// Define language mapping for display names
export const languageMap: Record<string, string> = {
  en: 'English',
  ar: 'العربية',
  es: 'Español',
  fr: 'Français',
};

// Define language codes
export const languages = ['en', 'ar', 'es', 'fr'];

// Create resources object
const resources = {
  en: { common: enCommon },
  ar: { common: arCommon },
  es: { common: esCommon },
  fr: { common: frCommon }
};

// Create i18next instance
const i18nInstance = createInstance();

// Initialize i18next
i18nInstance
  .use(initReactI18next)
  .init({
    lng: 'en',
    fallbackLng: 'en',
    supportedLngs: languages,
    defaultNS: 'common',
    fallbackNS: 'common',
    resources,
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

export default i18nInstance;
```

## Core Components

### 1. Language Context (`app/contexts/languageContext.tsx`)

The Language Context provides state management for the current language and RTL direction.

```typescript
// languageContext.tsx
'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import i18n from '@/i18n';
import { languages, languageMap } from '@/i18n';

interface LanguageContextType {
  currentLanguage: string;
  changeLanguage: (lang: string) => void;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const router = useRouter();
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');
  const [isRTL, setIsRTL] = useState<boolean>(false);

  useEffect(() => {
    // Get language from localStorage or use default
    const savedLanguage = typeof window !== 'undefined' 
      ? localStorage.getItem('language') || 'en'
      : 'en';
    
    if (languages.includes(savedLanguage)) {
      setCurrentLanguage(savedLanguage);
      i18n.changeLanguage(savedLanguage);
      setIsRTL(savedLanguage === 'ar');
    }
  }, []);

  const changeLanguage = (lang: string) => {
    if (languages.includes(lang)) {
      setCurrentLanguage(lang);
      i18n.changeLanguage(lang);
      localStorage.setItem('language', lang);
      setIsRTL(lang === 'ar');
      
      // Refresh the page to apply the language change
      router.refresh();
    }
  };

  return (
    <LanguageContext.Provider value={{ currentLanguage, changeLanguage, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
```

### 2. Translation Hook (`app/hooks/useTranslation.ts`)

A custom hook that wraps react-i18next's useTranslation hook for easier usage.

```typescript
// useTranslation.ts
'use client';

import { useTranslation as useI18nTranslation } from 'react-i18next';
import i18n from '@/i18n';

export function useTranslation(namespace = 'common') {
  const { t, i18n: i18nInstance, ready } = useI18nTranslation(namespace);

  return {
    t,
    i18n: i18nInstance,
    ready,
    changeLanguage: i18n.changeLanguage,
  };
}
```

### 3. Language Layout Wrapper (`app/components/LanguageLayoutWrapper.tsx`)

A component that handles setting the HTML dir attribute based on the language direction.

```typescript
// LanguageLayoutWrapper.tsx
'use client';

import React, { useEffect } from 'react';
import { useLanguage } from '../contexts/languageContext';

interface LanguageLayoutWrapperProps {
  children: React.ReactNode;
}

const LanguageLayoutWrapper: React.FC<LanguageLayoutWrapperProps> = ({ children }) => {
  const { isRTL, currentLanguage } = useLanguage();
  
  useEffect(() => {
    // Set the HTML dir attribute based on the language direction
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;
  }, [isRTL, currentLanguage]);
  
  return <>{children}</>;
};

export default LanguageLayoutWrapper;
```

## RTL Styling

RTL-specific styles are defined in `styles/rtl.scss`:

```scss
// rtl.scss
html[dir="rtl"] {
  // Text alignment
  text-align: right;
  
  // Directional properties
  .header .top-bar .right-section {
    flex-direction: row-reverse;
  }
  
  // Margins and paddings
  .header .top-bar .dropdown .dropdown-button i {
    margin-left: 0;
    margin-right: 4px;
  }
  
  // Floats
  .float-left {
    float: right !important;
  }
  
  .float-right {
    float: left !important;
  }
  
  // Borders
  .border-left {
    border-left: none;
    border-right: 1px solid;
  }
  
  .border-right {
    border-right: none;
    border-left: 1px solid;
  }
  
  // Icons and arrows
  .fa-angle-right:before {
    content: "\f104"; // Left arrow
  }
  
  .fa-angle-left:before {
    content: "\f105"; // Right arrow
  }
  
  // Form elements
  input, textarea {
    text-align: right;
  }
  
  // Navigation
  .navigation .nav-container {
    flex-direction: row-reverse;
  }
}
```

## Integration in Root Layout

The language provider and layout wrapper are integrated in the root layout:

```tsx
// app/layout.tsx
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* head content */}
      </head>
      <body id="__next">
        <CommonProvider>
          <LanguageProvider>
            <LanguageLayoutWrapper>
              <Header />
              <Loader />
              <AlertProvider>
                {children}
              </AlertProvider>
            </LanguageLayoutWrapper>
          </LanguageProvider>
        </CommonProvider>
      </body>
    </html>
  );
}
```

## Translation Files Structure

Translation files follow a nested JSON structure:

```json
{
  "header": {
    "helpline": "Helpline",
    "contact": {
      "phone": "+971 800 KINDALI",
      "email": "<EMAIL>"
    },
    "nav": {
      "hotels": "Hotels",
      "holidays": "Holidays"
    }
  },
  "search": {
    "placeholder": "Search for hotels, destinations...",
    "button": "Search"
  },
  // other sections...
}
```

## Best Practices

1. **Key Organization**: Organize translation keys by feature or component
2. **Namespace Usage**: Use namespaces for large sections of the application
3. **Variable Interpolation**: Use i18next's interpolation for dynamic content
   ```tsx
   // Example: t('greeting', { name: 'John' })
   // In translation file: "greeting": "Hello, {{name}}!"
   ```
4. **Pluralization**: Use i18next's pluralization features for count-based text
   ```tsx
   // Example: t('items', { count: 5 })
   // In translation file: "items": "{{count}} item", "items_plural": "{{count}} items"
   ```
5. **RTL Considerations**: Always test RTL languages to ensure proper layout

## Troubleshooting

### Common Issues

1. **Missing Translations**: Check if the key exists in all language files
2. **RTL Layout Issues**: Ensure RTL-specific styles are applied correctly
3. **Language Not Changing**: Verify the language code is correct and included in the supported languages

### Debugging

1. Use browser developer tools to inspect the HTML lang and dir attributes
2. Check localStorage for the saved language preference
3. Verify that translation files are being loaded correctly

## Future Improvements

1. **Language Detection**: Implement automatic language detection based on browser settings
2. **Lazy Loading**: Implement lazy loading of translation files for better performance
3. **Translation Management**: Integrate with a translation management system for easier updates
