"use client";
import React from 'react'
import ItineraryCard from '../components/ItineraryCard/ItineraryCard';

function Page() {
    const bookingData: any = {
        bookingId: "BK123456789",
        hotelName: "The Luxury Collection Hotel",
        hotelAddress: "123 Palm Jumeirah, Dubai, UAE",
        hotelRating: 5,
        checkInDate: "April 28, 2025",
        checkOutDate: "May 1, 2025",
        checkInTime: "After 2:00 PM",
        checkOutTime: "Before 12:00 PM",
        roomType: "Deluxe Ocean View Suite",
        guestCount: {
          rooms: 1,
          adults: 2
        },
        primaryGuest: {
          title: "Mr.",
          name: "<PERSON>",
          email: "<EMAIL>",
          phone: "+****************"
        },
        priceDetails: {
          roomRate: 750,
          nights: 3,
          taxesAndFees: 90,
          resortFee: 45
        },
        paymentInfo: {
          isPaid: true,
          cardType: "Visa",
          cardLastDigits: "4567"
        },
        cancellationDeadline: "April 26, 2025",
        hotelImage: "/assets/img/searchBg.webp"
      };
  return (
    <div className="py-8 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <ItineraryCard data={bookingData} />
      </div>
    </div>
  )
}

export default Page