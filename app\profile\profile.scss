@use "/styles/variable" as *;
@use "sass:color";

.profile-page-main-container {
  @media (max-width: $breakpoint-md) {
    display: flex;
    justify-content: center;
  }
}

.profile-page {
  display: flex;
  flex-direction: row;

  &__leftSection {
    width: 22vw;
    height: calc(100vh - 142px);
    padding: 0 15px;

    @media (max-width: $breakpoint-md) {
      display: none;
    }
  }

  &__rightSection {
    width: calc(100vw - 22vw);
    padding: 0 15px;

    @media (max-width: $breakpoint-md) {
      width: 100vw;
    }

    .content {
      display: flex;
      flex-direction: column;
      row-gap: 14px;

      height: calc(100vh - 182px);
      overflow-y: auto;
      padding: 0 20px;

      @media (max-width: $breakpoint-xs) {
        padding: 0;
      }

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
      }

      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

      $accent-color: #e6f3f0;
      $light-gray: #e9ecef;
      $medium-gray: #adb5bd;
      $dark-gray: #495057;
      $white: #ffffff;
      $shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      $border-radius: 8px;
      $border-radius-sm: 4px;

      .list-container {
        background-color: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        padding: 20px;

        &.no-bg {
          background: none;
          border: none;
          border-radius: 0;
        }

        .bookings-container {
          width: 100%;

          .bookings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;

            @media (max-width: $breakpoint-xs) {
              flex-direction: column;
              row-gap: 10px;
              align-items: start;
            }

            h1 {
              font-size: 1rem;
              color: $dark-gray;
              font-weight: 700;
              margin: 0;
            }

            .bookings-actions {
              display: flex;
              gap: 0.75rem;
              align-items: center;

              .search-box {
                position: relative;

                i {
                  position: absolute;
                  left: 10px;
                  top: 50%;
                  transform: translateY(-50%);
                  color: $medium-gray;
                  font-size: 0.7rem;
                }

                input {
                  padding: 0.4rem 0.75rem 0.4rem 2rem;
                  border-radius: $border-radius-sm;
                  border: 1px solid $light-gray;
                  font-size: 0.7rem;
                  width: 180px;
                  transition: all 0.2s ease;

                  &:focus {
                    outline: none;
                    border-color: $primary-color;
                    box-shadow: 0 0 0 2px rgba($primary-color, 0.15);
                  }
                }
              }

              .new-booking-btn {
                background-color: $primary-color;
                color: $white;
                border: none;
                padding: 0.4rem 0.75rem;
                border-radius: $border-radius-sm;
                font-size: 0.7rem;
                font-weight: 600;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.3rem;
                transition: all 0.2s ease;

                &:hover {
                  background-color: darken($primary-color, 7%);
                }
              }
            }
          }

          .bookings-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;

            .filter-tabs {
              display: flex;
              gap: 0.4rem;

              button {
                background-color: $secondary-color;
                border: 1px solid $light-gray;
                border-radius: $border-radius-sm;
                padding: 0.15rem 0.5rem;
                font-size: 0.65rem;
                font-weight: 500;
                color: $dark-gray;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                gap: 0.3rem;

                i {
                  font-size: 0.7rem;
                }

                &:hover {
                  background-color: $accent-color;
                }

                &.active {
                  background-color: $primary-color;
                  color: $white;
                  border-color: $primary-color;
                }
              }
            }

            /* Sort options CSS */
            .sort-options {
              display: flex;
              align-items: center;
              gap: 0.5rem;
            }

            .sort-options span {
              font-size: 0.7rem;
              color: var(--dark-gray);
            }

            .sort-options select {
              padding: 0.3rem 0.6rem;
              border-radius: var(--border-radius-sm);
              border: 1px solid var(--light-gray);
              font-size: 0.7rem;
              color: var(--dark-gray);
              cursor: pointer;
              background-color: var(--white);
            }

            .sort-options select:focus {
              outline: none;
              border-color: var(--primary-color);
            }
          }

          .bookings-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;

            .booking-card {
              display: flex;
              background-color: $white;
              border-radius: $border-radius;
              overflow: hidden;
              box-shadow: $shadow;
              transition: transform 0.2s ease;

              &:hover {
                transform: translateY(-2px);
              }

              // Common styles for both cards
              .booking-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 60px;

                @media (max-width: $breakpoint-md) {
                  padding: 10px 0;
                }

                i {
                  font-size: 1.5rem;
                }
              }

              &.flight-card {
                .booking-icon {
                  background-color: rgba($primary-color, 0.1);

                  i {
                    color: $primary-color;
                  }
                }

                .booking-details.flight-details {
                  flex: 1;
                  padding: 1rem;
                  display: flex;
                  flex-direction: column;

                  .flight-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 0.75rem;

                    h3 {
                      margin: 0;
                      font-size: 0.9rem;
                      font-weight: 600;
                      color: $dark-gray;
                    }
                  }

                  .flight-info {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 1rem;

                    .flight-route,
                    .flight-timing {
                      display: flex;
                      flex-direction: column;
                      gap: 0.4rem;
                    }
                  }

                  .flight-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: auto;

                    .booking-reference {
                      margin: 0;
                    }

                    .flight-actions {
                      display: flex;
                      align-items: center;
                      gap: 1rem;

                      .booking-price {
                        font-size: 1.1rem;
                        font-weight: 700;
                        color: $dark-gray;
                      }
                    }
                  }
                }
              }

              &.hotel-card {
                //   .booking-icon {
                //     background-color: rgba(#8e24aa, 0.1);

                //     i {
                //       color: #8e24aa;
                //     }
                //   }

                .booking-icon {
                  background-color: rgba($primary-color, 0.1);

                  i {
                    color: $primary-color;
                  }
                }

                .booking-details.hotel-details {
                  flex: 1;
                  padding: 1rem;
                  display: flex;
                  flex-direction: column;

                  .hotel-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 0.75rem;

                    h3 {
                      margin: 0;
                      font-size: 0.9rem;
                      font-weight: 600;
                      color: $dark-gray;

                      @media (max-width: $breakpoint-xs) {
                        font-size: 13px;
                      }
                    }
                  }

                  .hotel-info {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 1rem;

                    @media (max-width: $breakpoint-xs) {
                      flex-direction: column;
                      row-gap: 10px;
                    }

                    .hotel-location {
                      display: flex;
                      flex-direction: column;
                      gap: 0.4rem;
                    }

                    .hotel-amenities {
                      display: flex;
                      flex-direction: column;
                      gap: 0.4rem;

                      .amenities-title {
                        font-weight: 500;
                        margin: 0;
                        font-size: 0.75rem;
                      }

                      .amenities-list {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 0.3rem;

                        @media (max-width: $breakpoint-xs) {
                          flex-direction: column;
                        }

                        .amenity-tag {
                          background-color: $accent-color;
                          color: $primary-color;
                          font-size: 0.65rem;
                          padding: 0.15rem 0.4rem;
                          border-radius: 20px;
                          font-weight: 500;
                          white-space: nowrap;

                          @media (max-width: $breakpoint-xs) {
                            width: fit-content;
                          }
                        }
                      }
                    }
                  }

                  .hotel-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: auto;

                    .booking-reference {
                      margin: 0;
                    }

                    .hotel-actions {
                      display: flex;
                      align-items: center;
                      gap: 1rem;

                      .booking-price {
                        font-size: 1.1rem;
                        font-weight: 700;
                        color: $dark-gray;
                      }
                    }
                  }
                }
              }

              p {
                margin: 0;
                font-size: 0.75rem;
                color: $dark-gray;
                display: flex;
                align-items: center;
                gap: 0.4rem;

                i {
                  color: $primary-color;
                  width: 14px;
                  font-size: 0.7rem;
                }
              }

              .booking-status {
                display: flex;
                align-items: center;
                gap: 0.3rem;
                font-size: 0.7rem;
                font-weight: 600;
                padding: 0.25rem 0.6rem;
                border-radius: 16px;

                @media (max-width: $breakpoint-xs) {
                  .fa-solid {
                    display: none;
                  }
                }

                &.confirmed {
                  background-color: #e8f5e9;
                  color: #2e7d32;
                }

                &.pending {
                  background-color: #fff8e1;
                  color: #ff8f00;
                }

                &.cancelled {
                  background-color: #ffebee;
                  color: #c62828;
                }
              }

              .view-details-btn {
                background-color: $white;
                color: $primary-color;
                border: 1px solid $primary-color;
                padding: 0.4rem 0.7rem;
                border-radius: $border-radius-sm;
                font-weight: 600;
                font-size: 0.7rem;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.3rem;
                transition: all 0.2s ease;

                &:hover {
                  background-color: $primary-color;
                  color: $white;
                }
              }
            }

            .no-bookings {
              text-align: center;
              padding: 2rem 0;

              i {
                font-size: 2rem;
                color: $medium-gray;
                margin-bottom: 0.75rem;
              }

              p {
                color: $dark-gray;
                font-size: 0.9rem;
                margin-bottom: 1rem;
              }

              button {
                background-color: $primary-color;
                color: $white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: $border-radius-sm;
                font-weight: 600;
                font-size: 0.7rem;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background-color: darken($primary-color, 7%);
                }
              }
            }
          }

          .bookings-summary {
            display: flex;
            gap: 1rem;

            @media (max-width: $breakpoint-xs) {
              flex-direction: column;
            }

            .summary-box {
              flex: 1;
              background-color: $white;
              border-radius: $border-radius;
              padding: 1rem;
              box-shadow: $shadow;
              text-align: center;

              h4 {
                color: $primary-color;
                font-size: 0.8rem;
                font-weight: 600;
                margin: 0 0 0.5rem 0;
              }

              p {
                font-size: 1.3rem;
                font-weight: 700;
                color: $dark-gray;
                margin: 0;
              }
            }
          }
        }

        .profile {
          &__header {
            color: #495057;
            font-weight: 600;
            font-size: 22px;
            padding-bottom: 20px;
          }

          &__userForm {
            display: flex;
            flex-direction: column;
            row-gap: 10px;
            width: 100%;
          }

          &__form-field {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            width: 100%;
            height: fit-content;
            padding: 24px 20px;
            background-color: #f5eee7;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            //border: 1px solid rgba(0, 0, 0, 0.2);
            transition: background-color 0.3s ease;

            &.bg-color-white {
              background-color: #fff;
            }

            &.edit-mode {
              flex-direction: column;
              background-color: #fff;
            }

            // Shared animation classes
            .form-field__details-group,
            .form-field__input-field-group {
              width: 100%;
              opacity: 1;
              max-height: 500px;
              transition: opacity 0.3s ease, max-height 0.3s ease,
                visibility 0.3s ease;
              visibility: visible;

              &.hidden {
                opacity: 0;
                max-height: 0;
                visibility: hidden;
                overflow: hidden;
                margin: 0;
                padding: 0;
              }
            }

            .form-field__details-group {
              display: flex;
              justify-content: space-between;
              align-items: center;
              width: 100%;
            }

            .form-field__userName {
              display: flex;
              flex-direction: row;
              align-items: center;

              .form-field__icon {
                width: 50px;
                height: 50px;
                border-radius: 100%;
                background-color: $primary-color;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #fff;
                font-size: 28px;
                font-weight: 700;

                @media (max-width: $breakpoint-xs) {
                  margin-right: 21px;
                }
              }

              .form-field__label-fullname {
                display: flex;
                flex-direction: column;
                padding-left: 21px;

                @media (max-width: $breakpoint-xs) {
                  padding-left: 0;
                }

                .label,
                .fullname {
                  font-size: 16px;
                  color: #5a5b5b;
                  margin: 0;

                  @media (max-width: $breakpoint-xs) {
                    font-size: 14px;
                  }
                }

                .label {
                  font-weight: 700;
                }

                .fullname {
                  font-weight: 500;
                }

                .password-dots {
                  display: flex;
                  flex-direction: row;
                  padding: 5px 0;
                  margin-top: 3px;
                  .dots {
                    height: 7px;
                    width: 7px;
                    background-color: rgba(0, 0, 0, 0.3);
                    border-radius: 100%;
                    margin-right: 5px;
                  }
                }
              }
            }

            .form-field__action {
              font-size: 14px;
              font-weight: 600;
              color: #5a5b5b;
              cursor: pointer;
              transition: color 0.2s ease;
              margin-left: auto; /* Pushes the button to the right */
              align-self: center;

              @media (max-width: $breakpoint-xs) {
                font-size: 13px;
              }

              &:hover {
                color: darken(#5a5b5b, 10%);
              }
            }

            .form-field__input-field {
              margin-bottom: 16px;

              h3 {
                font-size: 16px;
                color: #5a5b5b;
                font-weight: 700;
                margin: 0 0 5px 0;
              }

              input {
                margin: 5px 0;
                padding: 8px 12px;
                border: 1px solid #ccc;
                width: 100%;
                max-width: 443px;
                border-radius: 3px;
                outline: 0;
                transition: border-color 0.2s ease, box-shadow 0.2s ease;

                &:focus {
                  border-color: $primary-color;
                  box-shadow: 0 0 0 2px rgba(8, 119, 103, 0.2);
                }
              }
            }

            .form-field__input-buttons {
              padding: 10px 0;
              display: flex;
              gap: 10px;

              .btn {
                padding: 0.625rem 1.25rem; // 10px 20px -> rem units
                font-size: 1rem; // 16px -> rem
                font-weight: 600;
                border-radius: 3px;
                cursor: pointer;
                border: 1px solid $primary-color;
                white-space: nowrap;
                transition: background-color 0.3s ease, color 0.3s ease;

                &.saveBtn {
                  background-color: $primary-color;
                  color: #fff;

                  &:hover {
                    background-color: lighten($primary-color, 5%);
                  }
                }

                &.cancelBtn {
                  background-color: #fff;
                  color: $primary-color;

                  &:hover {
                    background-color: $primary-color;
                    color: #fff;
                  }
                }
              }

              // Optional media queries for responsive tweaks
              @media (max-width: $breakpoint-md) {
                .btn {
                  padding: 0.5rem 1rem;
                  font-size: 0.95rem;
                }
              }

              @media (max-width: $breakpoint-xs) {
                .btn {
                  width: 100%;
                  text-align: center;
                  padding: 0.75rem;
                  font-size: 0.95rem;
                }
              }
            }
          }

          &__form-field2 {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            width: 100%;
            padding: 24px 20px;
            background-color: #fff;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            //border: 1px solid rgba(0, 0, 0, 0.2);
            transition: background-color 0.3s ease;

            .form-field__details-group {
              display: flex;
              justify-content: space-between;
              align-items: center;
              width: 100%;
            }

            .subscription-label {
              font-size: 16px;
              color: #5a5b5b;
              margin: 0;
              font-weight: 700;

              @media (max-width: $breakpoint-xs) {
                font-size: 12px;
                max-width: 90%;
              }
            }

            .form-field__userName {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              width: 100%;
              position: relative;

              //   @media (max-width: $breakpoint-sm) {
              //   flex-direction: column;
              //   align-items: start;

              // }

              .form-field__action {
                font-size: 14px;
                font-weight: 600;
                color: #5a5b5b;
                cursor: pointer;
                transition: color 0.2s ease;
                margin-left: auto; /* Pushes the button to the right */
                align-self: center;

                @media (max-width: $breakpoint-xs) {
                  font-size: 13px;
                }

                &:hover {
                  color: darken(#5a5b5b, 10%);
                }
              }

              .verify-textbox-btn-group {
                display: flex;
                align-items: center;
                gap: 8px;
                width: 100%;
                max-width: 400px;
                @media (max-width: $breakpoint-sm) {
                  flex-direction: column;
                  align-items: start;
                }
                &__textbox {
                  flex: 1;
                  padding: 6px 12px;
                  border: 1px solid $primary-color;
                  border-radius: 4px;
                  font-size: 14px;
                  transition: border-color 0.2s ease;
                  &:focus {
                    outline: none;
                    border-color: $secondary-color;
                    box-shadow: 0 0 0 2px rgba($secondary-color, 0.2);
                  }
                }

                &__buttons-wrapper {
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  flex-wrap: nowrap;
                  column-gap: 5px;
                }
                &__verifyBtn,
                &__cancelBtn {
                  border-radius: 4px;
                  padding: 0.5rem 1rem; // Responsive padding
                  font-size: 1rem; // Responsive font-size
                  font-weight: 500;
                  cursor: pointer;
                  white-space: nowrap;
                  transition: all 0.3s ease;
                }

                // Verify Button
                &__verifyBtn {
                  background-color: $secondary-color;
                  color: white;
                  border: none;

                  &:hover {
                    background-color: darken($secondary-color, 5%);
                  }

                  &:active {
                    background-color: darken($secondary-color, 10%);
                  }
                }

                // Cancel Button
                &__cancelBtn {
                  background-color: #fff;
                  color: $primary-color;
                  border: 1px solid $secondary-color;

                  &:hover,
                  &:active {
                    background-color: $secondary-color;
                    color: #fff;
                  }
                }

                // Optional: Responsive tweaks with media queries
                @media (max-width: $breakpoint-md) {
                  &__verifyBtn,
                  &__cancelBtn {
                    padding: 0.4rem 0.8rem;
                    font-size: 0.9rem;
                  }
                }

                @media (max-width: $breakpoint-xs) {
                  &__verifyBtn,
                  &__cancelBtn {
                    width: 100%;
                    text-align: center;
                    padding: 0.6rem;
                    font-size: 0.95rem;
                  }
                }
              }

              .form-field__icon {
                width: 50px;
                height: 50px;
                border-radius: 100%;
                background-color: $primary-color;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #fff;
                font-size: 28px;
                font-weight: 700;
              }

              .form-field__label-fullname {
                display: flex;
                flex-direction: column;
                padding-left: 21px;

                @media (max-width: $breakpoint-xs) {
                  padding-left: 0;
                }

                .label,
                .fullname {
                  font-size: 16px;
                  color: #5a5b5b;
                  margin: 0;

                  @media (max-width: $breakpoint-xs) {
                    font-size: 14px;
                  }
                }

                .label {
                  font-weight: 700;
                }

                .fullname {
                  font-weight: 500;
                }
              }
            }

            .delete-account {
              font-size: 16px;
              font-weight: 300;
              color: $primary-color;
              text-decoration: none;
              transition: text-decoration 0.2 ease color;

              @media (max-width: $breakpoint-xs) {
                font-size: 13px;
              }

              &:hover {
                text-decoration: underline $primary-color;
              }
            }
          }

          &__form-field,
          &__form-field2 {
            transition: box-shadow 0.2s ease;
            &:hover {
              box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.3);
            }
          }
        }
      }

      .no-bookings-found {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 180px;

        h3 {
          font-size: 18px;
          font-weight: 700;
          text-align: center;
          margin-bottom: 5px;
        }

        a {
          font-size: 13px;
          font-weight: 600;
          color: $primary-color;
          text-decoration: underline $primary-color;
          text-underline-offset: 2px;
          transition: color 0.2s ease;

          &:hover {
            color: color.adjust($primary-color, $lightness: -5%);
          }
        }
      }

      // .no-reviews-found {
      //   display: flex;
      //   flex-direction: column;
      //   justify-content: center;
      //   align-items: center;
      //   min-height: 380px;

      //   h5 {
      //     font-size: 15px;
      //     font-weight: 600;
      //     margin-bottom: 3px;
      //   }

      //   p {
      //     color: #555;
      //     font-size: 13px;
      //     font-weight: 500;
      //     margin-bottom: 10px;
      //   }

      //   .getStartedBtn {
      //     background-color: $primary-color;
      //     color: #fff;
      //     font-size: 14px;
      //     font-weight: 600;
      //     padding: 13px 27px;
      //     border-radius: 9999px;
      //     transition: background-color 0.2s ease;

      //     &:hover {
      //       background-color: color.adjust($primary-color, $lightness: -3%);
      //     }
      //   }
      // }
    }
  }
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: 0.625rem; // 10px
  font-family: sans-serif;
  flex-wrap: wrap;

  .label {
    font-size: 0.9375rem; // 15px
    font-weight: 400;
    color: #5a5b5b;
    transition: opacity 0.3s ease;
    white-space: nowrap;

    @media (max-width: $breakpoint-xs) {
      display: none;
    }
  }

  .toggle {
    position: relative;
    width: 3.125rem; // 50px
    height: 1.625rem; // 26px
    border-radius: 9999px;
    background-color: #e5e5e5;
    border: none;
    cursor: pointer;
    padding: 0;
    transition: background-color 0.3s ease;
    flex-shrink: 0;

    &.active {
      background-color: $secondary-color;

      .indicator {
        transform: translateX(1.5rem); // 24px
      }
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px #5e9f9f;
    }

    .indicator {
      position: absolute;
      top: 0.1875rem; // 3px
      left: 0.1875rem;
      width: 1.25rem; // 20px
      height: 1.25rem;
      background-color: white;
      border-radius: 50%;
      transition: transform 0.3s ease;
    }
  }

  // Optional media queries for scaling on smaller screens
  @media (max-width: 480px) {
    gap: 0.5rem;

    .label {
      font-size: 0.875rem; // 14px
    }

    .toggle {
      width: 2.5rem; // 40px
      height: 1.375rem; // 22px

      &.active {
        .indicator {
          transform: translateX(1.125rem); // 18px
        }
      }

      .indicator {
        width: 1rem; // 16px
        height: 1rem;
      }
    }
  }
}

.custom-checkbox-container {
  padding: 5px 0;
  display: flex;
  column-gap: 30px;

  @media (max-width: $breakpoint-xs) {
    flex-direction: column;
  }

  .checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-family: system-ui, -apple-system, sans-serif;
    gap: 8px;
    user-select: none;

    input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;
    }

    .checkmark {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 20px;
      width: 20px;
      border-radius: 50%;
      //background-color: #0088ff;
      background-color: $secondary-color;
      transition: all 0.2s ease;

      svg {
        position: absolute;
        width: 12px;
        height: 12px;
      }
    }

    input:not(:checked) ~ .checkmark {
      background-color: #e0e0e0;
    }

    input:focus ~ .checkmark {
      //box-shadow: 0 0 0 2px rgba(0, 136, 255, 0.3);
      box-shadow: 0 0 0 2px #5e9f9f;

      cursor: not-allowed;
    }

    .checkbox-label {
      font-size: 16px;
      font-weight: 400;
      color: #5a5b5b;
    }
  }
}
