"use client";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import leh1 from "../../../public/assets/img/destinations/leh-ladakh.jpg";
import udaipur1 from "../../../public/assets/img/destinations/udaipur.jpg";
import hyderabad1 from "../../../public/assets/img/destinations/hyderabad.jpg";
import leh2 from "../../../public/assets/img/destinations/leh-ladakh.jpg";
import udaipur2 from "../../../public/assets/img/destinations/udaipur.jpg";
import hyderabad2 from "../../../public/assets/img/destinations/hyderabad.jpg";

const destinations = [
  {
    image: leh1,
    location: "Goa",
    description: "Beach paradise",
    rating: 4.8,
    price: "₹3,999",
  },
  {
    image: udaipur1,
    location: "Kerala",
    description: "God's own country",
    rating: 4.9,
    price: "₹4,500",
  },
  {
    image: hyderabad1,
    location: "Rajasthan",
    description: "Royal heritage",
    rating: 4.7,
    price: "₹3,200",
  },
  {
    image: hyderabad2,
    location: "Kashmir",
    description: "Paradise on earth",
    rating: 4.6,
    price: "₹5,800",
  },
  {
    image: udaipur2,
    location: "Himachal",
    description: "Mountain retreat",
    rating: 4.5,
    price: "₹2,900",
  },
  {
    image: leh2,
    location: "Andaman",
    description: "Tropical islands",
    rating: 4.8,
    price: "₹6,200",
  },
];

function ImageSlider() {
  const [isMobileView, setIsMobileView] = useState(false);

  // Determine mobile view based on screen size
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth <= 950);
    };

    // Set initial value
    handleResize();

    // Add resize listener
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="w-full mt-[30px]  md:mt-10">
      {/* Header with title and View more */}
      <div className="flex justify-between items-center mb-6 md:mb-8 sm:flex-col sm:items-start sm:gap-2">
        <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 md:text-2xl">Recommended Hotels</h3>
        {/* <button className="bg-transparent border border-gray-300 text-gray-600 text-[13px] font-medium px-3 py-[6px] rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:text-gray-700 sm:self-end sm:text-xs sm:px-[10px] sm:py-[5px] md:text-sm md:px-4 md:py-2">View more</button> */}
      </div>

      {isMobileView ? (
        // Mobile Layout - Horizontal Scroll
        <div className="min-[951px]:hidden">
          <div className="flex gap-4 overflow-x-auto pb-4 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
            {destinations.map((item, index) => (
              <div className="flex-shrink-0 w-[160px] bg-white rounded-xl overflow-hidden shadow-[0_2px_8px_rgba(0,0,0,0.1)] transition-all duration-200 hover:-translate-y-0.5 hover:shadow-[0_4px_16px_rgba(0,0,0,0.15)]" key={index}>
                <div className="relative w-full h-[120px] bg-gradient-to-br from-[#667eea] to-[#764ba2] overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.location}
                    fill
                    className="object-cover transition-transform duration-300 hover:scale-105"
                  />
                  <div className="absolute top-2 right-2 bg-[rgba(255,255,255,0.95)] rounded-xl px-[6px] py-[2px] flex items-center gap-[2px] text-[11px] font-semibold shadow-[0_1px_3px_rgba(0,0,0,0.1)]">
                    <span className="text-[#ffa500] text-[9px]">★</span>
                    <span className="text-[#333]">{item.rating}</span>
                  </div>
                </div>
                <div className="p-3">
                  <h4 className="text-base font-semibold text-[#333] my-0 mb-1 leading-[1.2]">{item.location}</h4>
                  <p className="text-xs text-[#666] my-0 mb-2 leading-[1.3]">{item.description}</p>
                  <div className="flex flex-col gap-[2px]">
                    <span className="text-[11px] text-[#888] uppercase tracking-[0.5px]">Starting</span>
                    <span className="text-sm font-bold text-[#333]">{item.price}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        // Desktop Layout - Matching Mobile Style
        <div className="max-[950px]:hidden">
          <div className="flex gap-5 overflow-x-auto pb-4 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
            {destinations.map((item, index) => (
              <div className="flex-[0_0_auto] w-[220px] bg-white rounded-xl overflow-hidden shadow-[0_4px_12px_rgba(0,0,0,0.1)] transition-all duration-300 cursor-pointer hover:-translate-y-1 hover:shadow-[0_8px_24px_rgba(0,0,0,0.15)]" key={index}>
                <div className="relative w-full h-40 bg-gradient-to-br from-[#667eea] to-[#764ba2] overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.location}
                    fill
                    className="object-cover transition-transform duration-300 hover:scale-105"
                  />
                  <div className="absolute top-3 right-3 bg-[rgba(255,255,255,0.95)] rounded-2xl px-2 py-1 flex items-center gap-[2px] text-xs font-semibold shadow-[0_2px_4px_rgba(0,0,0,0.1)]">
                    <span className="text-[#ffa500] text-[10px]">★</span>
                    <span className="text-[#333]">{item.rating}</span>
                  </div>
                </div>
                <div className="p-4">
                  <h4 className="text-lg font-semibold text-[#333] my-0 mb-[6px] leading-[1.2]">{item.location}</h4>
                  <p className="text-sm text-[#666] my-0 mb-3 leading-[1.3]">{item.description}</p>
                  <div className="flex flex-col gap-[2px]">
                    <span className="text-xs text-[#888] uppercase tracking-[0.5px]">Starting</span>
                    <span className="text-base font-bold text-[#333]">{item.price}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default ImageSlider;
