@use "/styles/variable" as *;

.wishlist-title {
  color: #495057;
  font-size: 1rem;
  margin-bottom: 20px;
  font-weight: 700;
}

// Empty state styles
.no-wishlist-found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 380px;
  
  h5 {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 3px;
    color: $primary-color;
  }
  
  p {
    color: lighten($primary-color, 20%);
    margin-top: 0;
  }
  
  i.fa-solid.fa-heart {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: $secondary-color;
  }
}

// Hotel card styles
.wishlist-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.hotel-card {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.hotel-image {
  width: 200px;
  height: 180px;
  flex-shrink: 0;
  position: relative;
  
  @media (max-width: 768px) {
    width: 100%;
    height: 200px;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .discount-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #e53e3e;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
  }
}

.hotel-details {
  padding: 15px;
  flex-grow: 1;
  
  h3 {
    margin: 0 0 10px;
    font-size: 18px;
    color: $primary-color;
  }
  
  .hotel-location, .hotel-rating {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #666;

    @media (max-width: $breakpoint-xs) {
      align-items: start;
      
    }
    
    i {
      margin-right: 8px;
      color: $secondary-color;
    }
    
    .review-count {
      margin-left: 8px;
      font-size: 14px;
      color: #999;
    }
  }
  
  .hotel-facilities {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
    
    .facility-badge {
      background-color: #f9f9f9;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }
  }
  
  .cancellation-policy {
    font-size: 12px;
    color: $secondary-color;
    margin-bottom: 10px;
  }
  
  .hotel-price {
    margin-top: 10px;
    
    span {
      font-weight: 600;
      font-size: 18px;
      color: $primary-color;
    }
  }
}

.hotel-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 15px;
  gap: 10px;
  background-color: #f9f9f9;
  
  @media (max-width: 768px) {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .hotel-price {
    margin-bottom: 15px;
    
    span {
      font-weight: 600;
      font-size: 18px;
      color: $primary-color;
    }
  }
  
  button {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .btn-view {
    background-color: $secondary-color;
    color: white;
    
    &:hover {
      background-color: darken($secondary-color, 10%);
    }
  }
  
  .btn-remove {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    background-color: transparent;
    color: $primary-color;
    border: 1px solid $primary-color;
    
    i {
      color: $primary-color;
    }
    
    &:hover {
      background-color: rgba($primary-color, 0.1);
    }
  }
}