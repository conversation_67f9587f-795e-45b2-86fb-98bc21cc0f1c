@use "sass:color";
@use "/styles/variable" as *;

.currency-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden; /* Important to prevent scrolling issues */

  .currency-section {
    margin-bottom: 0;
    display: flex;
    flex-direction: column;

    &:last-child {
      flex: 1;
      overflow: hidden; /* Important for flex child with overflow */
    }

    .section-title {
      padding: 12px 16px;
      background-color: #f5f5f5;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      flex-shrink: 0; /* Prevent title from shrinking */
    }

    .section-title-with-search {
      background-color: #f5f5f5;
      padding: 12px 16px;
      flex-shrink: 0; /* Prevent from shrinking */

      .section-title {
        background-color: transparent;
        padding: 0 0 8px 0;
      }

      .search-container {
        position: relative;

        .search-icon {
          position: absolute;
          left: 10px;
          top: 50%;
          transform: translateY(-50%);
          color: #999;
          font-size: 14px;
        }

        .search-input {
          width: 100%;
          padding: 8px 12px 8px 32px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;

          &:focus {
            outline: none;
            border-color: $primary-color;
          }
        }
      }
    }

    .price-view-list {
      flex-shrink: 0; /* Prevent from shrinking */

      .price-view-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 59, 149, 0.05);
        }

        &:last-child {
          border-bottom: none;
        }

        .price-view-info {
          display: flex;
          align-items: flex-start;
          gap: 12px;

          .icon-container {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;

            i {
              color: $primary-color;
              font-size: 16px;
            }
          }

          .price-view-details {
            display: flex;
            flex-direction: column;

            .price-view-name {
              font-size: 15px;
              color: #333;
              margin-bottom: 4px;
            }

            .price-view-description {
              font-size: 13px;
              color: #666;
            }
          }
        }
      }
    }

    .currency-list {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      /* Scrollbar styling */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #aaa;
      }

      .currency-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 59, 149, 0.05);
        }

        &:last-child {
          border-bottom: none;
        }

        .currency-info {
          .currency-name {
            font-size: 15px;
            color: #333;
          }
        }

        .currency-code-container {
          display: flex;
          align-items: center;
          gap: 8px;

          .currency-symbol {
            color: #666;
            font-size: 14px;
            padding-right: 8px;
            border-right: 1px solid #ddd;
          }

          .currency-code {
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }

          .selection-indicator {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;

            i {
              color: $primary-color;
              font-size: 16px;
            }
          }
        }
      }

      .no-results {
        padding: 20px;
        text-align: center;
        color: #666;
        font-size: 14px;
      }
    }
  }
}

// RTL support
html[dir="rtl"] {
  .currency-selector {
    .currency-section {
      .section-title {
        text-align: right;
      }

      .section-title-with-search {
        .search-container {
          .search-icon {
            left: auto;
            right: 10px;
          }

          .search-input {
            padding: 8px 32px 8px 12px;
            text-align: right;
          }
        }
      }

      .price-view-list {
        .price-view-item {
          .price-view-info {
            flex-direction: row-reverse;

            .price-view-details {
              text-align: right;
            }
          }
        }
      }

      .currency-list {
        .currency-item {
          .currency-info {
            text-align: right;
          }

          .currency-code-container {
            flex-direction: row-reverse;

            .currency-symbol {
              padding-right: 0;
              padding-left: 8px;
              border-right: none;
              border-left: 1px solid #ddd;
            }

            .selection-indicator {
              margin-left: 0;
              margin-right: 8px;
            }
          }
        }

        .no-results {
          text-align: right;
        }
      }
    }
  }
}
