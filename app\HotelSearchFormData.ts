export interface HotelSearchFormData {
  searchQuery: string; // Display name (e.g., "Dubai")
  fullLocationData?: string; // Full location data (e.g., "Dubai, United Arab Emirates")
  locationId?: string; // Location ID from auto-suggest API
  geoCode?: { lat: string; long: string }; // Coordinates from auto-suggest API
  checkInDate: string | null;
  checkOutDate: string | null;
  travelers: {
    adults: number;
    rooms: number;
    children: number;
  };
  // Add rooms array to store detailed room information
  roomsData: Array<{
    id: number;
    adults: number;
    children: number;
    childrenAges: Array<{
      id: number;
      age: number;
    }>;
  }>;
}