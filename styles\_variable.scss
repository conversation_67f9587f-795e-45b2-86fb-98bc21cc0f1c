// ===============================
// COLORS
// ===============================
$primary-color: #003b95;
$light-primary: #d5dfff;
$primary-text-color: #ffffff;
$primary-text-hover: #E0E0E0;
$secondary-color: #006ce4;
$bg-light: #e7e7e7;
$bg-dark: #EFEFF0;
$border-color: #e5e7eb;
$border-color2: #ccc;
$button-color: #046A38;
$button-txt-color: #fff;
$white-color: white;
$white-color1: #F2F2F2;
$rating-color: #FFA500;
$red-color4: #C71515;
$input-element-blue: #1e90ff;
$black-color: #000000 ;
$gray_color:#cccccc;
$light_gray_color:#f2f2f2;
$yellow_color:#FFD700;

// Derived colors
$primary-light: #e7dfd5; // Lighter version of primary
$secondary-light: #d5e7e7; // Lighter version of secondary

// Dark Mode Colors
$dark-bg: #111827;
$dark-text: #f9fafb;
$dark-primary: #3b82f6;
$black-color: #1E1E1E;
$black-color2: #616161;
$black-color3: #9a9a9a;
$black-color4: #d6d6d6;
$filter-black1: #1A1A1A;
$filter-black2: #595959;
$booking-black: #17181C;

// ===============================
// TYPOGRAPHY
// ===============================
$font-family-sans: "Geist", sans-serif;
$font-family-mono: "Geist Mono", monospace;

$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-sm: 14px;

// ===============================
// BREAKPOINTS
// ===============================
$isMobile: 950px;
$breakpoint-xs: 480px; // Mobile
$breakpoint-sm: 640px; // Small devices
$breakpoint-md: 768px; // Tablets
$breakpoint-lg: 1024px; // Laptops
$breakpoint-xl: 1280px; // Desktops

// ===============================
// SPACING (Margin/Padding)
// ===============================
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// ===============================
// SHADOWS & BORDERS
// ===============================
$box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
$border-radius: 8px;

// CSS custom properties are now defined in variable.css

// Additional utility classes for Tailwind CSS integration
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.bg-primary {
  background-color: var(--primary-color);
}

.bg-secondary {
  background-color: var(--secondary-color);
}

.bg-primary-light {
  background-color: var(--primary-light);
}

.bg-secondary-light {
  background-color: var(--secondary-light);
}

.border-primary-light {
  border-color: var(--primary-light);
}

.border-secondary-light {
  border-color: var(--secondary-light);
}