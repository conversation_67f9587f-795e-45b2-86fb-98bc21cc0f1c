<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 705 591">
  <!-- Generator: Adobe Illustrator 29.4.0, SVG Export Plug-In . SVG Version: 2.1.0 Build 152)  -->
  <defs>
    <style>
      .st0 {
        font-family: AdobeArabic-Regular, 'Adobe Arabic';
      }

      .st0, .st1, .st2, .st3 {
        fill: #fff;
      }

      .st0, .st3 {
        font-size: 33.73px;
      }

      .st4 {
        clip-path: url(#clippath-6);
      }

      .st5 {
        clip-path: url(#clippath-7);
      }

      .st6 {
        clip-path: url(#clippath-4);
      }

      .st7 {
        clip-path: url(#clippath-9);
      }

      .st8 {
        clip-path: url(#clippath-10);
      }

      .st2 {
        font-family: TimesNRMTPro, TimesNRMTPro;
        font-size: 37.97px;
      }

      .st9 {
        fill: none;
      }

      .st10 {
        clip-path: url(#clippath-1);
      }

      .st11 {
        clip-path: url(#clippath-5);
      }

      .st12 {
        clip-path: url(#clippath-8);
      }

      .st13 {
        clip-path: url(#clippath-3);
      }

      .st3 {
        font-family: DroidArabicKufi-Bold, 'DroidArabicKufi Bold';
        font-weight: 700;
      }

      .st14 {
        clip-path: url(#clippath-2);
      }

      .st15 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="st9" y="-8.04" width="705" height="591"/>
    </clipPath>
    <clipPath id="clippath-1">
      <rect class="st9" y="-8.04" width="705" height="591"/>
    </clipPath>
    <clipPath id="clippath-2">
      <rect class="st9" y="-8.04" width="705" height="591"/>
    </clipPath>
    <clipPath id="clippath-3">
      <rect class="st9" y="-8.04" width="705" height="591"/>
    </clipPath>
    <clipPath id="clippath-4">
      <rect class="st9" x="121.33" y="206.75" width="23.73" height="161.35"/>
    </clipPath>
    <clipPath id="clippath-5">
      <path class="st9" d="M132.97,237.74s5.82,23.23,5.82,31.16v41.95h-11.09v-83.04l-6.16-20.96v161.22h6.16v-51.06h11.09v51.06h6.16v-99.17l-11.99-31.16Z"/>
    </clipPath>
    <clipPath id="clippath-6">
      <rect class="st9" y="-8.04" width="705" height="591"/>
    </clipPath>
    <clipPath id="clippath-7">
      <rect class="st9" y="-8.04" width="705" height="591"/>
    </clipPath>
    <clipPath id="clippath-8">
      <rect class="st9" x="95.54" y="206.75" width="23.73" height="161.35"/>
    </clipPath>
    <clipPath id="clippath-9">
      <path class="st9" d="M112.91,308.43c0,1.34-1.08,2.42-2.42,2.42h-8.67v-41.95h-6.16v99.17h6.16v-51.06h8.67c1.34,0,2.42,1.08,2.42,2.42v48.64h6.16v-48.55c0-1.61-.6-3.18-1.68-4.38l-1.09-1.22,1.09-1.22c1.08-1.2,1.68-2.76,1.68-4.38v-101.48l-6.16,20.96v80.62Z"/>
    </clipPath>
    <clipPath id="clippath-10">
      <rect class="st9" y="-8.04" width="705" height="591"/>
    </clipPath>
  </defs>
  <g class="st15">
    <rect class="st1" y="-8.04" width="705" height="591"/>
  </g>
  <g class="st10">
    <rect class="st1" y="-8.04" width="705" height="591"/>
  </g>
  <g class="st14">
    <g class="st13">
      <g class="st6">
        <g class="st11">
          <rect class="st1" x="93.89" y="205.1" width="52.82" height="164.65"/>
        </g>
      </g>
    </g>
  </g>
  <g class="st4">
    <g class="st5">
      <g class="st12">
        <g class="st7">
          <rect class="st1" x="93.89" y="205.1" width="52.82" height="164.65"/>
        </g>
      </g>
    </g>
  </g>
  <g class="st8">
    <text class="st2" transform="translate(182.77 285.57)"><tspan x="0" y="0">K</tspan></text>
    <text class="st2" transform="translate(214.02 285.57)"><tspan x="0" y="0">i</tspan></text>
    <text class="st2" transform="translate(224.31 285.57)"><tspan x="0" y="0">n</tspan></text>
    <text class="st2" transform="translate(245.26 285.57)"><tspan x="0" y="0">d</tspan></text>
    <text class="st2" transform="translate(266.22 285.57)"><tspan x="0" y="0">A</tspan></text>
    <text class="st2" transform="translate(295.5 285.57)"><tspan x="0" y="0">l</tspan></text>
    <text class="st2" transform="translate(305.79 285.57)"><tspan x="0" y="0">i</tspan></text>
    <text class="st2" transform="translate(316.08 285.57)"><tspan x="0" y="0"> </tspan></text>
    <text class="st2" transform="translate(325.57 285.57)"><tspan x="0" y="0">T</tspan></text>
    <text class="st2" transform="translate(347.59 285.57)"><tspan x="0" y="0">r</tspan></text>
    <text class="st2" transform="translate(361.94 285.57)"><tspan x="0" y="0">a</tspan></text>
    <text class="st2" transform="translate(378.08 285.57)"><tspan x="0" y="0">v</tspan></text>
    <text class="st2" transform="translate(396.53 285.57)"><tspan x="0" y="0">e</tspan></text>
    <text class="st2" transform="translate(412.59 285.57)"><tspan x="0" y="0">l</tspan></text>
    <text class="st2" transform="translate(422.88 285.57)"><tspan x="0" y="0"> </tspan></text>
    <text class="st2" transform="translate(432.38 285.57)"><tspan x="0" y="0">&amp;</tspan></text>
    <text class="st2" transform="translate(461.65 285.57)"><tspan x="0" y="0"> </tspan></text>
    <text class="st2" transform="translate(471.14 285.57)"><tspan x="0" y="0">T</tspan></text>
    <text class="st2" transform="translate(492.63 285.57)"><tspan x="0" y="0">o</tspan></text>
    <text class="st2" transform="translate(513.59 285.57)"><tspan x="0" y="0">u</tspan></text>
    <text class="st2" transform="translate(534.55 285.57)"><tspan x="0" y="0">r</tspan></text>
    <text class="st2" transform="translate(549.17 285.57)"><tspan x="0" y="0">i</tspan></text>
    <text class="st2" transform="translate(559.46 285.57)"><tspan x="0" y="0">s</tspan></text>
    <text class="st2" transform="translate(574.08 285.57)"><tspan x="0" y="0">m</tspan></text>
    <text class="st2" transform="translate(605.33 285.57)"><tspan x="0" y="0"> </tspan></text>
    <text class="st3" transform="translate(172.13 333.46)"><tspan x="0" y="0"> </tspan></text>
    <text class="st0" transform="translate(183.6 333.46)"><tspan x="0" y="0">ﺔ</tspan></text>
    <text class="st0" transform="translate(205.68 333.46)"><tspan x="0" y="0">ﺣ</tspan></text>
    <text class="st0" transform="translate(226.35 333.46)"><tspan x="0" y="0">ﺎ</tspan></text>
    <text class="st0" transform="translate(238.8 333.46)"><tspan x="0" y="0">ﻴ</tspan></text>
    <text class="st0" transform="translate(253.77 333.46)"><tspan x="0" y="0">ﺴ</tspan></text>
    <text class="st0" transform="translate(288.99 333.46)"><tspan x="0" y="0">ﻟ</tspan></text>
    <text class="st0" transform="translate(301.44 333.46)"><tspan x="0" y="0">ا</tspan></text>
    <text class="st0" transform="translate(313.21 333.46)"><tspan x="0" y="0">و</tspan></text>
    <text class="st3" transform="translate(334.36 333.46)"><tspan x="0" y="0"> </tspan></text>
    <text class="st0" transform="translate(345.83 333.46)"><tspan x="0" y="0">ﺮ</tspan></text>
    <text class="st0" transform="translate(361.95 333.46)"><tspan x="0" y="0">ﻔ</tspan></text>
    <text class="st0" transform="translate(384.89 333.46)"><tspan x="0" y="0">ﺴ</tspan></text>
    <text class="st0" transform="translate(420.11 333.46)"><tspan x="0" y="0">ﻠ</tspan></text>
    <text class="st0" transform="translate(433.4 333.46)"><tspan x="0" y="0">ﻟ</tspan></text>
    <text class="st3" transform="translate(445.85 333.46)"><tspan x="0" y="0"> </tspan></text>
    <text class="st0" transform="translate(457.31 333.46)"><tspan x="0" y="0">ﻲ</tspan></text>
    <text class="st0" transform="translate(491.21 333.46)"><tspan x="0" y="0">ﻠ</tspan></text>
    <text class="st0" transform="translate(504.5 333.46)"><tspan x="0" y="0">ﻋ</tspan></text>
    <text class="st0" transform="translate(523.62 333.46)"><tspan x="0" y="0">ﺪ</tspan></text>
    <text class="st0" transform="translate(545.71 333.46)"><tspan x="0" y="0">ﻨ</tspan></text>
    <text class="st0" transform="translate(559 333.46)"><tspan x="0" y="0">ﻳ</tspan></text>
    <text class="st0" transform="translate(572.62 333.46)"><tspan x="0" y="0">ﺎ</tspan></text>
    <text class="st0" transform="translate(585.07 333.46)"><tspan x="0" y="0">ﻛ</tspan></text>
  </g>
</svg>