"use client";
import React, { useEffect, useState } from "react";
import "./PropertyDetailsShimmer.scss";

function PropertyDetailsShimmer() {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    handleResize(); // check once on mount
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div className="property-details-shimmer-container">
      <div className="propery-header-shimmer">
        <div className="property-info-shimmer">
          <div className="property-info-shimmer__image"></div>

          <div className="property-info-shimmer__info">
            <div className="property-info-shimmer__title-star-rating">
              <h6 className="title"></h6>
            </div>
            <p className="property-info-shimmer__location"></p>

            <div className="property-info-shimmer__room-info">
              <div className="info"></div>
              {!isMobile && <div className="info"></div>}
            </div>
          </div>
        </div>

        <div className="checkin-checkout-container">
          <div className="checkin-out-section">
            <p className="date-label"></p>
            <h6 className="date"></h6>
            <p className="time"></p>
          </div>

          <div className="day-count">
            <span></span>
          </div>

          {isMobile && (
            <>
              <div className="day-count2">
                <span></span>
              </div>
              <div className="day-count2">
                <span></span>
              </div>
            </>
          )}

          <div className="checkin-out-section">
            <p className="date-label"></p>
            <h6 className="date"></h6>
            <p className="time"></p>
          </div>
        </div>
      </div>

      {!isMobile && <hr />}

      <div className="room-details-shimmer-container">
        <h6 className="room-header"></h6>
        <div className="room-info">
          <div className="room-info__image"></div>
          <div className="room-info__details">
            <h6 className="room-name"></h6>
            <div className="room-details">
              <div className="detail"></div>
              <div className="detail"></div>
              <div className="detail"></div>
              <div className="detail"></div>
            </div>
          </div>
        </div>

        <div className="room-benefits">
          <h6 className="room-benefits__header"></h6>
          <div className="room-benefits__list">
            <div className="list-item"></div>
            <div className="list-item"></div>
            <div className="list-item"></div>
            <div className="list-item"></div>
            <div className="list-item"></div>
            <div className="list-item"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PropertyDetailsShimmer;
