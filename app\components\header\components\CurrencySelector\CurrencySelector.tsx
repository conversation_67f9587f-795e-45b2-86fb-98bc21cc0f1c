"use client";
import React, { useState } from "react";
import { useTranslation } from "@/app/hooks/useTranslation";
import './CurrencySelector.scss';

interface CurrencyItem {
  name: string;
  symbol?: string;
  code: string;
}

interface CurrencySelectorProps {
  currencies: CurrencyItem[];
  currentSelectedCurrency: string;
  handleCurrencySelection: (currencyCode: string) => void;
  priceViewOptions: { label: string; icon: string; description: string; }[];
  currentSelectedPriceView: string;
  handlePriceViewSelection: (priceView: string) => void;
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  currencies,
  currentSelectedCurrency,
  handleCurrencySelection,
  priceViewOptions,
  currentSelectedPriceView,
  handlePriceViewSelection
}) => {
  const { t } = useTranslation("common");
  const [searchQuery, setSearchQuery] = useState("");

  // Filter currencies based on search query
  const filteredCurrencies = currencies.filter(
    (currency) =>
      currency.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      currency.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="currency-selector">
      {/* Price view options section */}
      <div className="currency-section">
        <h3 className="section-title">{t("settings.priceView", "Price view")}</h3>

        <div className="price-view-list">
          {priceViewOptions.map((priceView, index) => (
            <div
              className="price-view-item"
              key={index}
              onClick={() => handlePriceViewSelection(priceView.label)}
            >
              <div className="price-view-info">
                <div className="icon-container">
                  <i className={`fa-solid ${priceView.icon}`}></i>
                </div>
                <div className="price-view-details">
                  <span className="price-view-name">{priceView.label}</span>
                  <span className="price-view-description">{priceView.description}</span>
                </div>
              </div>

              <div className="selection-indicator">
                {currentSelectedPriceView === priceView.label && (
                  <i className="fa-solid fa-check"></i>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Currency selection section */}
      <div className="currency-section">
        <div className="section-title-with-search">
          <h3 className="section-title">{t("settings.chooseCurrency", "Choose a currency")}</h3>
          <div className="search-container">
            <i className="fa-solid fa-magnifying-glass search-icon"></i>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t("common.search", "Search")}
              className="search-input"
            />
          </div>
        </div>

        <div className="currency-list">
          {filteredCurrencies.length > 0 ? (
            filteredCurrencies.map((currency, index) => (
              <div
                className="currency-item"
                key={index}
                onClick={() => handleCurrencySelection(currency.code)}
              >
                <div className="currency-info">
                  <span className="currency-name">{currency.name}</span>
                </div>

                <div className="currency-code-container">
                  <span className="currency-symbol">{currency.symbol}</span>
                  <span className="currency-code">{currency.code}</span>

                  <div className="selection-indicator">
                    {currentSelectedCurrency === currency.code && (
                      <i className="fa-solid fa-check"></i>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="no-results">{t("common.noDataFound", "No data found...")}</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CurrencySelector;
