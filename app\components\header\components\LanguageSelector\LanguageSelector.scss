@use "sass:color";
@use "/styles/variable" as *;

.language-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden; /* Important to prevent scrolling issues */

  .language-section {
    margin-bottom: 0;
    display: flex;
    flex-direction: column;

    &:last-child {
      flex: 1;
      overflow: hidden; /* Important for flex child with overflow */
    }

    .section-title {
      padding: 12px 16px;
      background-color: #f5f5f5;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      flex-shrink: 0; /* Prevent title from shrinking */
    }

    .language-list {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      /* Scrollbar styling */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #aaa;
      }
    }

    .language-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(0, 59, 149, 0.05);
      }

      &:last-child {
        border-bottom: none;
      }

      .language-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .flag-container {
          width: 24px;
          height: 18px;
          overflow: hidden;
          border-radius: 2px;

          .flag-image {
            object-fit: cover;
            width: 100%;
            height: 100%;
          }
        }

        .language-name {
          font-size: 15px;
          color: #333;
        }
      }

      .selection-indicator {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: $primary-color;
          font-size: 16px;
        }
      }
    }
  }
}

// RTL support
html[dir="rtl"] {
  .language-selector {
    .language-section {
      .section-title {
        text-align: right;
      }

      .language-item {
        .language-info {
          flex-direction: row-reverse;
        }
      }
    }
  }
}
