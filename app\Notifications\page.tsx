"use client";
import React, { useEffect, useState } from "react";
import <PERSON> from "next/link";
import './Notification.scss'

type Notification = {
  id: string;
  message: string;
  timestamp: string;
  isRead: boolean;
};

const NotificationsPage: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // In a real app, fetch from your API
    // Simulating API call with setTimeout
    setTimeout(() => {
      const mockNotifications: Notification[] = [
        {
          id: "1",
          message: "Your hotel booking in Dubai is confirmed!",
          timestamp: "2 hours ago",
          isRead: false,
        },
        {
          id: "2",
          message: "New holiday package for Bali is available now.",
          timestamp: "1 day ago",
          isRead: false,
        },
        {
          id: "3",
          message: "Your flight to Paris departs tomorrow.",
          timestamp: "2 days ago",
          isRead: true,
        },
        {
          id: "4",
          message: "Special discount on hotels in Istanbul this week!",
          timestamp: "3 days ago",
          isRead: true,
        },
        {
          id: "5",
          message: "Complete your profile to get personalized recommendations.",
          timestamp: "1 week ago",
          isRead: true,
        },
      ];
      
      setNotifications(mockNotifications);
      setIsLoading(false);
    }, 500);
  }, []);

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map(notification => ({
      ...notification,
      isRead: true
    }));
    setNotifications(updatedNotifications);
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => 
      prev.filter(notification => notification.id !== id)
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    // Here you could also navigate to a detailed view or perform other actions
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="notifications-page">
      <div className="notifications-container">
        <div className="notifications-header">
          <div className="header-left">
            <h1>Notifications</h1>
            {unreadCount > 0 && (
              <span className="unread-count">({unreadCount} unread)</span>
            )}
          </div>
          <div className="header-actions">
            {notifications.some(n => !n.isRead) && (
              <button className="mark-read-button" onClick={markAllAsRead}>
                Mark all as read
              </button>
            )}
            {notifications.length > 0 && (
              <button className="clear-all-button" onClick={clearAllNotifications}>
                Clear all
              </button>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="loading">Loading notifications...</div>
        ) : (
          <>
            {notifications.length > 0 ? (
              <div className="notifications-list">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`notification-item ${!notification.isRead ? 'unread' : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="notification-content">
                      <p className="notification-message">{notification.message}</p>
                      <span className="notification-time">{notification.timestamp}</span>
                    </div>
                    <div className="notification-actions">
                      {!notification.isRead && (
                        <button 
                          className="mark-read-btn"
                          onClick={(e) => {
                            e.stopPropagation();
                            markAsRead(notification.id);
                          }}
                          title="Mark as read"
                        >
                          ✓
                        </button>
                      )}
                      <button 
                        className="delete-btn"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteNotification(notification.id);
                        }}
                        title="Delete notification"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <p>You don't have any notifications yet.</p>
                <Link href="/">Return to homepage</Link>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default NotificationsPage;