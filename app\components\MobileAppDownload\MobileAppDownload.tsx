'use client';

import React from 'react';
import Image from 'next/image';

const MobileAppDownload: React.FC = () => {
  return (
    <div className="w-full  py-12 lg:py-16" style={{ background: 'linear-gradient(135deg, #003b95, #002a6b, #001a4a)' }}>
      <div className="common-container w-full mt-[30px]  md:mt-10">

        {/* Header Section */}
        <div className="mb-6 md:mb-8">
          <h3 className="text-lg font-semibold text-white m-0 md:text-2xl">Download Our App</h3>
        </div>

        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12">

          {/* Content Section */}
          <div className="flex-1 text-center lg:text-left text-white">
            <div className="mb-6">
              <p className="text-lg lg:text-xl text-blue-100 mb-8 leading-relaxed max-w-lg mx-auto lg:mx-0 text-left lg:text-left">
                Book hotels faster, get exclusive deals, and manage your trips on the go.
                Available for iOS and Android.
              </p>
            </div>

            {/* Features List */}
            <div className="mb-8 space-y-3">
              <div className="flex items-center justify-start gap-1 lg:gap-3">
                <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <i className="fa-solid fa-check text-white text-sm"></i>
                </div>
                <span className="text-blue-100">Exclusive mobile-only deals</span>
              </div>
              <div className="flex items-center justify-start gap-1 lg:gap-3">
                <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <i className="fa-solid fa-check text-white text-sm"></i>
                </div>
                <span className="text-blue-100">Instant booking confirmations</span>
              </div>
              <div className="flex items-center justify-start gap-1 lg:gap-3">
                <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <i className="fa-solid fa-check text-white text-sm"></i>
                </div>
                <span className="text-blue-100">Offline access to bookings</span>
              </div>
              <div className="flex items-center justify-start gap-1 lg:gap-3">
                <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <i className="fa-solid fa-check text-white text-sm"></i>
                </div>
                <span className="text-blue-100">24/7 customer support</span>
              </div>
            </div>

            {/* Download Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              {/* App Store Button */}
              <a
                href="#"
                className="inline-flex items-center justify-center bg-black hover:bg-gray-800 text-white px-6 py-3 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg group"
              >
                <div className="flex items-center gap-3">
                  <i className="fa-brands fa-apple text-2xl"></i>
                  <div className="text-left">
                    <div className="text-xs text-gray-300">Download on the</div>
                    <div className="text-lg font-semibold">App Store</div>
                  </div>
                </div>
              </a>

              {/* Google Play Button */}
              <a
                href="#"
                className="inline-flex items-center justify-center bg-black hover:bg-gray-800 text-white px-6 py-3 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg group"
              >
                <div className="flex items-center gap-3">
                  <i className="fa-brands fa-google-play text-2xl"></i>
                  <div className="text-left">
                    <div className="text-xs text-gray-300">Get it on</div>
                    <div className="text-lg font-semibold">Google Play</div>
                  </div>
                </div>
              </a>
            </div>
          </div>

          {/* Phone Mockup Section */}
          <div className="flex-shrink-0 relative">
            <div className="relative w-56 h-[420px] lg:w-64 lg:h-[480px] mx-auto">
              {/* Phone Frame */}
              <div className="absolute inset-0 bg-gray-900 rounded-[3rem] p-2 shadow-2xl">
                <div className="w-full h-full bg-white rounded-[2.5rem] overflow-hidden relative">
                  
                  {/* Status Bar */}
                  <div className="absolute top-0 left-0 right-0 h-10 flex items-center justify-between px-4 text-white text-xs z-10" style={{ backgroundColor: '#003b95' }}>
                    <span className="font-medium">9:41</span>
                    <div className="flex items-center gap-1">
                      <i className="fa-solid fa-signal text-xs"></i>
                      <i className="fa-solid fa-wifi text-xs"></i>
                      <i className="fa-solid fa-battery-three-quarters text-xs"></i>
                    </div>
                  </div>

                  {/* App Content */}
                  <div className="pt-10 h-full bg-gradient-to-b from-blue-50 to-white">
                    {/* Header */}
                    <div className="px-3 py-3 text-white" style={{ backgroundColor: '#003b95' }}>
                      <h3 className="text-base font-bold">EcoGO Hotels</h3>
                      <p className="text-blue-100 text-xs">Find your perfect stay</p>
                    </div>

                    {/* Search Section */}
                    <div className="p-3">
                      <div className="bg-white rounded-lg shadow-md p-3 mb-3">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-gray-600 text-xs">
                            <i className="fa-solid fa-location-dot" style={{ color: '#003b95' }}></i>
                            <span>New York, NY</span>
                          </div>
                          <div className="flex items-center gap-2 text-gray-600 text-xs">
                            <i className="fa-solid fa-calendar" style={{ color: '#003b95' }}></i>
                            <span>Dec 25 - Dec 28</span>
                          </div>
                          <div className="flex items-center gap-2 text-gray-600 text-xs">
                            <i className="fa-solid fa-users" style={{ color: '#003b95' }}></i>
                            <span>2 guests, 1 room</span>
                          </div>
                        </div>
                        <button className="w-full text-white py-2 rounded-lg mt-2 text-xs font-medium" style={{ backgroundColor: '#003b95' }}>
                          Search Hotels
                        </button>
                      </div>

                      {/* Hotel Cards */}
                      <div className="space-y-2">
                        <div className="bg-white rounded-lg shadow-sm p-2">
                          <div className="flex gap-2">
                            <div className="w-12 h-10 bg-gray-200 rounded"></div>
                            <div className="flex-1">
                              <h4 className="font-medium text-xs text-gray-800">Grand Hotel</h4>
                              <p className="text-[10px] text-gray-500">★★★★☆ 4.5</p>
                              <p className="text-xs font-bold" style={{ color: '#003b95' }}>$120/night</p>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white rounded-lg shadow-sm p-2">
                          <div className="flex gap-2">
                            <div className="w-12 h-10 bg-gray-200 rounded"></div>
                            <div className="flex-1">
                              <h4 className="font-medium text-xs text-gray-800">City Plaza</h4>
                              <p className="text-[10px] text-gray-500">★★★★★ 4.8</p>
                              <p className="text-xs font-bold" style={{ color: '#003b95' }}>$95/night</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                <i className="fa-solid fa-star text-white"></i>
              </div>
              <div className="absolute -bottom-4 -left-4 w-10 h-10 bg-green-400 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                <i className="fa-solid fa-check text-white text-sm"></i>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default MobileAppDownload;
