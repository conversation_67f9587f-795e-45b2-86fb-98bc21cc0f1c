"use client";

import React from 'react';
import MapContainer from '@/components/map/MapContainer';
import { useTranslation } from '@/app/hooks/useTranslation';
import './NearbyAttractionsMap.scss';

interface Attraction {
  id: string;
  name: string;
  type: string;
  location: {
    latitude: number;
    longitude: number;
  };
  distance?: string;
}

interface NearbyAttractionsMapProps {
  hotelLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  hotelName: string;
  attractions: Attraction[];
}

const NearbyAttractionsMap: React.FC<NearbyAttractionsMapProps> = ({
  hotelLocation,
  hotelName,
  attractions = []
}) => {
  const { t } = useTranslation();
  
  // Convert attractions to points of interest format
  const pointsOfInterest = attractions.map(attraction => ({
    name: `${attraction.name} ${attraction.distance ? `(${attraction.distance})` : ''}`,
    location: attraction.location,
    type: attraction.type
  }));
  
  return (
    <div className="nearby-attractions-map">
      <MapContainer
        center={hotelLocation}
        zoom={13}
        height="500px"
        width="100%"
        hotels={[{
          id: 'hotel',
          name: hotelName,
          location: hotelLocation
        }]}
        pointsOfInterest={pointsOfInterest}
        showHeader={true}
        title={t('Nearby Attractions')}
        showGoogleMapsLink={true}
      />
      
      {attractions.length > 0 && (
        <div className="attractions-list">
          <h3>{t('Attractions List')}</h3>
          <ul>
            {attractions.map(attraction => (
              <li key={attraction.id} className="attraction-item">
                <div className="attraction-name">{attraction.name}</div>
                <div className="attraction-type">{attraction.type}</div>
                {attraction.distance && (
                  <div className="attraction-distance">{attraction.distance}</div>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default NearbyAttractionsMap;
