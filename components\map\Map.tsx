// 'use client';

// import { useEffect, useRef, useMemo } from 'react';
// import { createIcon } from './createIcon';
// import './Map.scss';

// // Define types for Leaflet to avoid TypeScript errors
// interface LeafletMap {
//   setView: (center: [number, number], zoom: number) => LeafletMap;
//   remove: () => void;
//   fitBounds: (bounds: LeafletLatLngBounds, options?: { padding?: [number, number] }) => LeafletMap;
// }

// interface LeafletLatLngBounds {
//   extend: (latLng: [number, number]) => LeafletLatLngBounds;
// }

// interface PopupOptions {
//   maxWidth: number;
//   className: string;
// }

// interface LeafletMarker {
//   addTo: (map: LeafletMap) => LeafletMarker;
//   bindPopup: (content: string, options?: PopupOptions) => LeafletMarker;
// }

// interface LeafletTileLayer {
//   addTo: (map: LeafletMap) => LeafletTileLayer;
// }

// interface TileLayerOptions {
//   attribution: string;
// }

// interface MarkerOptions {
//   icon: unknown;
// }

// interface LeafletIcon {
//   new (options: ReturnType<typeof createIcon>): unknown;
// }

// interface MapOptions {
//   zoomControl?: boolean;
//   dragging?: boolean;
//   touchZoom?: boolean;
//   scrollWheelZoom?: boolean;
//   doubleClickZoom?: boolean;
//   boxZoom?: boolean;
//   keyboard?: boolean;
// }

// interface TooltipOptions {
//   permanent?: boolean;
//   direction?: string;
//   className?: string;
//   offset?: [number, number];
// }

// interface LeafletMarker {
//   addTo: (map: LeafletMap) => LeafletMarker;
//   bindPopup: (content: string, options?: PopupOptions) => LeafletMarker;
//   bindTooltip: (content: string, options?: TooltipOptions) => LeafletMarker;
//   on: (event: string, callback: () => void) => LeafletMarker;
// }

// interface LeafletStatic {
//   map: (element: HTMLElement, options?: MapOptions) => LeafletMap;
//   tileLayer: (url: string, options: TileLayerOptions) => LeafletTileLayer;
//   marker: (position: [number, number], options: MarkerOptions) => LeafletMarker;
//   Icon: LeafletIcon;
//   latLngBounds: () => LeafletLatLngBounds;
//   divIcon: (options: { className: string, html: string, iconSize?: [number, number], iconAnchor?: [number, number] }) => unknown;
// }

// export interface LocationDetails {
//   id?: string;
//   name: string;
//   rating?: number;
//   price?: string;
//   image?: string;
//   description?: string;
//   showViewButton?: boolean;
// }

// export interface MapMarker {
//   id?: string;
//   position: [number, number];
//   popup?: string;
//   iconType?: 'hotel' | 'poi' | 'restaurant' | 'attraction' | 'default';
//   details?: LocationDetails;
// }

// export interface MapProps {
//   center?: [number, number];
//   zoom?: number;
//   markers?: Array<MapMarker>;
//   style?: React.CSSProperties;
//   autoCenter?: boolean; // Auto-center map based on markers
//   fitBounds?: boolean; // Fit all markers in view
//   interactive?: boolean; // Allow user interaction with map
//   onMarkerClick?: (markerId: string) => void; // Callback when marker is clicked
// }

// const Map = ({
//   center,
//   zoom = 13,
//   markers = [],
//   style = { height: '500px', width: '100%' },
//   autoCenter = true,
//   fitBounds = true,
//   interactive = true,
//   onMarkerClick
// }: MapProps) => {
//   const mapRef = useRef<HTMLDivElement>(null);
//   const mapInstanceRef = useRef<LeafletMap | null>(null);
//   const markersRef = useRef<LeafletMarker[]>([]);

//   // Use useMemo for computedCenter to avoid dependency issues
//   const computedCenter = useMemo(() => {
//     return center || (markers.length > 0 ? markers[0].position : [51.505, -0.09] as [number, number]);
//   }, [center, markers]);

//   useEffect(() => {
//     // Import Leaflet CSS
//     import('leaflet/dist/leaflet.css');

//     // Dynamically import Leaflet to avoid SSR issues
//     const initializeMap = async () => {
//       if (!mapRef.current) return;

//       // Clean up previous map instance if it exists
//       if (mapInstanceRef.current) {
//         mapInstanceRef.current.remove();
//         mapInstanceRef.current = null;
//         markersRef.current = [];
//       }

//       // Dynamically import Leaflet
//       const L = (await import('leaflet')).default as unknown as LeafletStatic;

//       // Create map instance with options
//       const mapOptions: MapOptions = {
//         zoomControl: interactive,
//         dragging: interactive,
//         touchZoom: interactive,
//         scrollWheelZoom: interactive,
//         doubleClickZoom: interactive,
//         boxZoom: interactive,
//         keyboard: interactive,
//       };

//       const map = L.map(mapRef.current, mapOptions).setView(computedCenter, zoom);
//       mapInstanceRef.current = map;

//       // Add tile layer
//       L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
//         attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
//       }).addTo(map);

//       // Create bounds object to fit all markers if we have multiple
//       const bounds = L.latLngBounds();

//       // Always show labels for hotel markers for better visibility
//       const hasMultipleHotels = true;

//       // Add markers
//       markers.forEach(marker => {
//         // Get icon configuration based on marker type
//         const iconType = marker.iconType || 'default';
//         const iconConfig = createIcon(iconType);

//         // Create the icon using Leaflet's Icon constructor
//         const icon = new L.Icon(iconConfig);

//         // Create the marker with the icon
//         const leafletMarker = L.marker(marker.position, { icon }).addTo(map);
//         markersRef.current.push(leafletMarker);

//         // Add to bounds for fitting
//         bounds.extend(marker.position);

//         // Handle popup and tooltip content
//         if (marker.details) {
//           // Create rich popup with details
//           const { id, name, rating, price, showViewButton } = marker.details;

//           // Generate stars for rating
//           const stars = rating
//             ? '★'.repeat(Math.floor(rating)) + (rating % 1 >= 0.5 ? '½' : '')
//             : '';

//           // Create HTML content for popup with optional View button
//           const popupContent = `
//             <div class="custom-popup">
//               <div class="popup-content">
//                 <h3>${name}</h3>
//                 ${stars ? `<div class="popup-rating">${stars}</div>` : ''}
//                 ${price ? `<div class="popup-price">${price}</div>` : ''}
//                 ${showViewButton ? `<div class="popup-view-btn" data-marker-id="${id || ''}">View Details</div>` : ''}
//               </div>
//             </div>
//           `;

//           // Bind popup for all markers
//           leafletMarker.bindPopup(popupContent, {
//             maxWidth: 250,
//             className: 'custom-popup-container'
//           });

//           // Add click event listener to the View button if marker has an ID
//           if (showViewButton && id && onMarkerClick) {
//             leafletMarker.on('popupopen', () => {
//               setTimeout(() => {
//                 const viewBtn = document.querySelector(`.popup-view-btn[data-marker-id="${id}"]`);
//                 if (viewBtn) {
//                   viewBtn.addEventListener('click', (e) => {
//                     e.stopPropagation();
//                     onMarkerClick(id);
//                   });
//                 }
//               }, 10);
//             });
//           }

//           // For hotel markers, if we have multiple hotels, add permanent tooltips
//           if (hasMultipleHotels && iconType === 'hotel') {
//             // Create tooltip content - make it more compact and visible
//             const tooltipContent = `
//               <div class="hotel-marker-tooltip">
//                 <div class="hotel-marker-tooltip__price">${price || ''}</div>
//                 <div class="hotel-marker-tooltip__name">${name}</div>
//                 <div class="hotel-marker-tooltip__details">
//                   ${stars ? `<span class="hotel-marker-tooltip__rating">${stars}</span>` : ''}
//                 </div>
//               </div>
//             `;

//             // Add permanent tooltip above marker
//             leafletMarker.bindTooltip(tooltipContent, {
//               permanent: true,
//               direction: 'top',
//               className: 'hotel-marker-tooltip-container',
//               offset: [0, -30] // Reduced offset to position closer to the marker
//             });
//           }
//         } else if (marker.popup) {
//           // Simple text popup
//           leafletMarker.bindPopup(marker.popup);
//         }
//       });

//       // Handle map centering and bounds
//       if (markers.length > 1 && fitBounds) {
//         // Fit all markers in view with padding
//         map.fitBounds(bounds, { padding: [50, 50] });
//       } else if (markers.length === 1) {
//         // Always center on single marker with a slightly higher zoom level for better visibility
//         map.setView(markers[0].position, zoom + 1);
//       }
//     };

//     initializeMap();

//     // Cleanup function
//     return () => {
//       if (mapInstanceRef.current) {
//         mapInstanceRef.current.remove();
//         mapInstanceRef.current = null;
//         markersRef.current = [];
//       }
//     };
//   }, [computedCenter, zoom, markers, autoCenter, fitBounds, interactive]);

//   return <div ref={mapRef} style={style} className="map-container" />;
// };

// export default Map;


'use client';

import { useEffect, useRef, useMemo } from 'react';
import { createIcon } from './createIcon';
import './Map.scss';

// Define types for Leaflet to avoid TypeScript errors
interface LeafletMap {
  setView: (center: [number, number], zoom: number) => LeafletMap;
  remove: () => void;
  fitBounds: (bounds: LeafletLatLngBounds, options?: { padding?: [number, number] }) => LeafletMap;
}

interface LeafletLatLngBounds {
  extend: (latLng: [number, number]) => LeafletLatLngBounds;
}

// Type for DOM elements that have been initialized with Leaflet
interface LeafletElement extends HTMLDivElement {
  _leaflet_id?: number;
}

interface PopupOptions {
  maxWidth: number;
  className: string;
}

interface LeafletMarker {
  addTo: (map: LeafletMap) => LeafletMarker;
  bindPopup: (content: string, options?: PopupOptions) => LeafletMarker;
  bindTooltip: (content: string, options?: TooltipOptions) => LeafletMarker;
  on: (event: string, callback: () => void) => LeafletMarker;
}

interface LeafletTileLayer {
  addTo: (map: LeafletMap) => LeafletTileLayer;
}

interface TileLayerOptions {
  attribution: string;
}

interface MarkerOptions {
  icon: unknown;
}

interface LeafletIcon {
  new (options: ReturnType<typeof createIcon>): unknown;
}

interface MapOptions {
  zoomControl?: boolean;
  dragging?: boolean;
  touchZoom?: boolean;
  scrollWheelZoom?: boolean;
  doubleClickZoom?: boolean;
  boxZoom?: boolean;
  keyboard?: boolean;
}

interface TooltipOptions {
  permanent?: boolean;
  direction?: string;
  className?: string;
  offset?: [number, number];
}

interface LeafletStatic {
  map: (element: HTMLElement, options?: MapOptions) => LeafletMap;
  tileLayer: (url: string, options: TileLayerOptions) => LeafletTileLayer;
  marker: (position: [number, number], options: MarkerOptions) => LeafletMarker;
  Icon: LeafletIcon;
  latLngBounds: () => LeafletLatLngBounds;
  divIcon: (options: { className: string, html: string, iconSize?: [number, number], iconAnchor?: [number, number] }) => unknown;
}

export interface LocationDetails {
  id?: string;
  name: string;
  rating?: number;
  price?: string;
  image?: string;
  description?: string;
  showViewButton?: boolean;
}

export interface MapMarker {
  id?: string;
  position: [number, number];
  popup?: string;
  iconType?: 'hotel' | 'poi' | 'restaurant' | 'attraction' | 'default';
  details?: LocationDetails;
}

export interface MapProps {
  center?: [number, number];
  zoom?: number;
  markers?: Array<MapMarker>;
  style?: React.CSSProperties;
  autoCenter?: boolean;
  fitBounds?: boolean;
  interactive?: boolean;
  onMarkerClick?: (markerId: string) => void;
}

const Map = ({
  center,
  zoom = 13,
  markers = [],
  style = { height: '500px', width: '100%' },
  autoCenter = true,
  fitBounds = true,
  interactive = true,
  onMarkerClick
}: MapProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<LeafletMap | null>(null);
  const markersRef = useRef<LeafletMarker[]>([]);
  const isInitializingRef = useRef(false);

  // Use useMemo for computedCenter to avoid dependency issues
  const computedCenter = useMemo(() => {
    return center || (markers.length > 0 ? markers[0].position : [51.505, -0.09] as [number, number]);
  }, [center, markers]);

  useEffect(() => {
    // Import Leaflet CSS
    import('leaflet/dist/leaflet.css');

    // Capture the current ref value for stable cleanup
    const currentMapElement = mapRef.current;

    const initializeMap = async () => {
      // Prevent multiple simultaneous initializations
      if (isInitializingRef.current || !currentMapElement) return;

      // Check if the element already has a Leaflet map
      if ((currentMapElement as LeafletElement)._leaflet_id) {
        console.warn('Map container already initialized, skipping');
        return;
      }

      isInitializingRef.current = true;

      // Clean up any existing map instance FIRST
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        } catch (error) {
          console.warn('Error removing map:', error);
        }
      }
      markersRef.current = [];

      try {
        // Small delay to ensure DOM cleanup is complete
        await new Promise(resolve => setTimeout(resolve, 50));

        // Check if component is still mounted and element exists
        if (!currentMapElement) {
          isInitializingRef.current = false;
          return;
        }

        // Clear any existing Leaflet state on the DOM element
        if ((currentMapElement as LeafletElement)._leaflet_id) {
          delete (currentMapElement as LeafletElement)._leaflet_id;
        }

        // Clear the innerHTML to ensure clean state
        currentMapElement.innerHTML = '';

        // Dynamically import Leaflet
        const L = (await import('leaflet')).default as unknown as LeafletStatic;

        // Double-check that the element doesn't already have a map
        if ((currentMapElement as LeafletElement)._leaflet_id) {
          console.warn('Map container still has Leaflet ID, forcing cleanup');
          delete (currentMapElement as LeafletElement)._leaflet_id;
        }

        // Create map instance with options
        const mapOptions: MapOptions = {
          zoomControl: interactive,
          dragging: interactive,
          touchZoom: interactive,
          scrollWheelZoom: interactive,
          doubleClickZoom: interactive,
          boxZoom: interactive,
          keyboard: interactive,
        };

        const map = L.map(currentMapElement, mapOptions).setView(computedCenter, zoom);
        mapInstanceRef.current = map;

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Create bounds object to fit all markers if we have multiple
        const bounds = L.latLngBounds();

        // Always show labels for hotel markers for better visibility
        const hasMultipleHotels = true;

        // Add markers
        markers.forEach(marker => {
          // Get icon configuration based on marker type
          const iconType = marker.iconType || 'default';
          const iconConfig = createIcon(iconType);

          // Create the icon using Leaflet's Icon constructor
          const icon = new L.Icon(iconConfig);

          // Create the marker with the icon
          const leafletMarker = L.marker(marker.position, { icon }).addTo(map);
          markersRef.current.push(leafletMarker);

          // Add to bounds for fitting
          bounds.extend(marker.position);

          // Handle popup and tooltip content
          if (marker.details) {
            // Create rich popup with details
            const { id, name, rating, price, showViewButton } = marker.details;

            // Generate stars for rating
            const stars = rating
              ? '★'.repeat(Math.floor(rating)) + (rating % 1 >= 0.5 ? '½' : '')
              : '';

            // Create HTML content for popup with optional View button
            const popupContent = `
              <div class="custom-popup">
                <div class="popup-content">
                  <h3>${name}</h3>
                  ${stars ? `<div class="popup-rating">${stars}</div>` : ''}
                  ${price ? `<div class="popup-price">${price}</div>` : ''}
                  ${showViewButton ? `<div class="popup-view-btn" data-marker-id="${id || ''}">View Details</div>` : ''}
                </div>
              </div>
            `;

            // Bind popup for all markers
            leafletMarker.bindPopup(popupContent, {
              maxWidth: 250,
              className: 'custom-popup-container'
            });

            // Add click event listener to the View button if marker has an ID
            if (showViewButton && id && onMarkerClick) {
              leafletMarker.on('popupopen', () => {
                setTimeout(() => {
                  const viewBtn = document.querySelector(`.popup-view-btn[data-marker-id="${id}"]`);
                  if (viewBtn) {
                    viewBtn.addEventListener('click', (e) => {
                      e.stopPropagation();
                      onMarkerClick(id);
                    });
                  }
                }, 10);
              });
            }

            // For hotel markers, if we have multiple hotels, add permanent tooltips
            if (hasMultipleHotels && iconType === 'hotel') {
              // Create tooltip content - make it more compact and visible
              const tooltipContent = `
                <div class="hotel-marker-tooltip">
                  <div class="hotel-marker-tooltip__price">${price || ''}</div>
                  <div class="hotel-marker-tooltip__name">${name}</div>
                  <div class="hotel-marker-tooltip__details">
                    ${stars ? `<span class="hotel-marker-tooltip__rating">${stars}</span>` : ''}
                  </div>
                </div>
              `;

              // Add permanent tooltip above marker
              leafletMarker.bindTooltip(tooltipContent, {
                permanent: true,
                direction: 'top',
                className: 'hotel-marker-tooltip-container',
                offset: [0, -30]
              });
            }
          } else if (marker.popup) {
            // Simple text popup
            leafletMarker.bindPopup(marker.popup);
          }
        });

        // Handle map centering and bounds
        if (markers.length > 1 && fitBounds) {
          // Fit all markers in view with padding
          map.fitBounds(bounds, { padding: [50, 50] });
        } else if (markers.length === 1) {
          // Always center on single marker with a slightly higher zoom level for better visibility
          map.setView(markers[0].position, zoom + 1);
        }

        isInitializingRef.current = false;
      } catch (error) {
        console.error('Error initializing map:', error);
        isInitializingRef.current = false;
      }
    };

    initializeMap();

    // Cleanup function
    return () => {
      isInitializingRef.current = false;
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        } catch (error) {
          console.warn('Error removing map:', error);
        }
      }
      markersRef.current = [];

      // Clear any Leaflet state on DOM element using captured reference
      if (currentMapElement) {
        if ((currentMapElement as LeafletElement)._leaflet_id) {
          delete (currentMapElement as LeafletElement)._leaflet_id;
        }
        // Clear the innerHTML to ensure clean state
        currentMapElement.innerHTML = '';
      }
    };
  }, [computedCenter, zoom, markers, autoCenter, fitBounds, interactive, onMarkerClick]);

  return <div ref={mapRef} style={style} className="map-container" />;
};

export default Map;