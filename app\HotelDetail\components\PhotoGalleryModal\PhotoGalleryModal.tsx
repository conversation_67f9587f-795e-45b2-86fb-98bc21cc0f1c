import React, { useState } from "react";
import Image from "next/image";
import "./PhotoGalleryModal.scss";
import ImageShowCase from "../ImageShowcase/ImageShowcase";
import { CarouselImage } from "../../hotel-detail-result.model";
import { useRouter } from "next/navigation";

interface PhotoGalleryModalProps {
  isOpen: boolean;
  onClose: () => void;
  hotelName: string;
}

// Define a type for the tabs
type TabName = "Overview" | "Deluxe Room" | "Superior Double" | "King Room";

// Define a type for the images object
type ImagesType = {
  [key in TabName]: string[];
};

const PhotoGalleryModal: React.FC<PhotoGalleryModalProps> = ({
  isOpen,
  onClose,
  hotelName = "Le Celestium",
}) => {
  const [activeTab, setActiveTab] = useState<TabName>("Overview");
  const [isImageLightBoxActive, setIsImageLightBoxActive] = useState<boolean>(false);

  const router = useRouter();

  // Sample data for the tabs and images
  const tabs: TabName[] = [
    "Overview",
    "Deluxe Room",
    "Superior Double",
    "King Room",
  ];

  const images2: CarouselImage[] = [
          {
            "pictureId": "carousel_101",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Hotel Overview",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 1,
            "detailRank": 1,
            "categoryRank": 1,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Exterior",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Public Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Bathroom",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/cfc1cc12d06b9bb675e7d26ccee0b7ec.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Dining Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Food & Beverages",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Facilities",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
            {
            "pictureId": "carousel_101",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Hotel Overview",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 1,
            "detailRank": 1,
            "categoryRank": 1,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Exterior",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Public Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Bathroom",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/cfc1cc12d06b9bb675e7d26ccee0b7ec.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Dining Areas",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Food & Beverages",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Facilities",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          },
          {
            "pictureId": "carousel_102",
            "url": "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
            "urlHigh": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlMedium": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "urlLow": "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
            "caption": "Luxury Suite",
            "imageCategory": "Room",
            "rank": null,
            "srpRank": 2,
            "detailRank": 2,
            "categoryRank": 2,
            "withinCategoryRank": 1
          }
        ]

  const images: ImagesType = {
    Overview: [
      "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
      "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
      "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
      "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
      "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
      "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
      "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
      "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
    ],
    "Deluxe Room": [
      "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
      "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
      "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
    ],
    "Superior Double": [
      "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
      "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
    ],
    "King Room": [
      "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
      "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
    ],
  };

  // Rating data
  const ratings = {
    overall: 7.2,
    reviews: 392,
    categories: {
     
      Facilities: 7.2,
       Staff: 5.9,
      Cleanliness: 7.7,
      Comfort: 7.8,
      "Value for money": 7.7,
      Location: 7.7,
      "Free Wifi": 10,
    },
  };

  if (!isOpen) return null;

  const handleReserveBtn = () => {
    router.push('/BookingPreviewPage');

  }

  return (
    <div className="photo-gallery-modal">
      <div className="photo-gallery-modal__container">
        <div className="photo-gallery-modal__header">
         {
          isImageLightBoxActive ? ( <button onClick={() =>  setIsImageLightBoxActive(false)} className="photo-gallery-modal__go-to-prevpage-btn"><i className="fa-solid fa-arrow-left "></i> Gallery</button>) : (<div></div>)
         }
       <div className="flex items-center">
           <div className="photo-gallery-modal__title">{hotelName}</div>
          <button className="photo-gallery-modal__reserve-btn" onClick={handleReserveBtn}>
            Reserve now
          </button>
       </div>
          <button className="photo-gallery-modal__close-btn" onClick={onClose}>
            Close <i className="fa-solid fa-xmark"></i>
          </button>
        </div>

        <div className="photo-gallery-modal__content">
          {
            isImageLightBoxActive ? 
            
            (  
              <div className="image-showcase-main-container">
               <ImageShowCase images={images2} />
              </div>
          
          
          
        ) : (  
        
        <div className="photo-gallery-modal__gallery">
            <div className="photo-gallery-modal__tabs">
              {tabs.map((tab) => {
                // Get the correct thumbnail image for each tab (first image in that category)
                const thumbnailImage =
                  images[tab] && images[tab].length > 0
                    ? images[tab][0]
                    : "/api/placeholder/100/60";

                return (
                  <div
                    key={tab}
                    className={`photo-gallery-modal__tab ${
                      activeTab === tab ? "active" : ""
                    }`}
                    onClick={() => setActiveTab(tab)}
                  >
                    <div
                      className={`photo-gallery-modal__tab-image ${
                        activeTab === tab ? "active" : ""
                      }`}
                    >
                      <Image
                        src={thumbnailImage}
                        alt={tab}
                        width={100}
                        height={60}
                        objectFit="cover"
                      />
                    </div>
                    <div
                      className={`photo-gallery-modal__tab-name ${
                        activeTab === tab ? "active" : ""
                      }`}
                    >
                      {tab}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="photo-gallery-modal__gallery-row">
              {images[activeTab] &&
                images[activeTab].map((imageUrl, index) => (
                  <div
                    key={index}
                    className="photo-gallery-modal__gallery-item"
                  >
                    <div className="image-container" onClick={() => setIsImageLightBoxActive(true)}>
                      <Image
                        src={imageUrl}
                        alt={`${activeTab} image ${index + 1}`}
                        layout="fill"
                        objectFit="cover"
                      />
                    </div>
                  </div>
                ))}
            </div>
          </div>
          
        )
          }
        

          <div className="photo-gallery-modal__sidebar">
            <div className="photo-gallery-modal__rating">
              <div className="rating-score">{ratings.overall}</div>
              <div className="rating-text">
                <div className="rating-label">Good</div>
                <div className="rating-reviews">{ratings.reviews} reviews</div>
              </div>
            </div>

            <div className="photo-gallery-modal__categories">
              <h3>Categories:</h3>
              {Object.entries(ratings.categories).map(([category, score]) => (
                <div key={category} className="category-item">
                  <div className="category-name-score-wrapper">
                    <div className="category-name">
                      {category}
                      {score === 10 && (
                        <i className="fa-solid fa-arrow-up category-icon"></i>
                      )}
                      
                    </div>
                    <div className="category-score">{score}</div>
                  </div>

                  <div className="category-score-container">
                    <div
                      className={`category-score-bar ${
                        (Number(score) / 10) * 100 === 100 ? "highscore" : Number(score) <= 6 ? "lowscore" : ""
                      }`}
                      style={{ width: `${(Number(score) / 10) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
             <div className="score-note-container">
     
                <div className="score-note">
                <i className="fa-solid fa-arrow-up highscore"></i> High score for
                Goa
              </div>

                <div className="score-note">
                <i className="fa-solid fa-arrow-down lowscore"></i> Low score for
                Goa
              </div>
    
             </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhotoGalleryModal;
