/* CSS custom properties (variables) for use in React components */
:root {
  /* Colors */
  --primary-color: #003b95;
  --primary-color-dark: #002a6b;
  --secondary-color: #006ce4;
  --primary-light: #e7dfd5;
  --secondary-light: #d5e7e7;
  --bg-light: #e7e7e7;
  --bg-dark: #EFEFF0;
  --border-color: #e5e7eb;
  --border-color2: #ccc;
  --button-color: #046A38;
  --button-txt-color: #fff;
  --white-color: white;
  --white-color1: #F2F2F2;
  --rating-color: #FFA500;
  --red-color4: #C71515;
  --input-element-blue: #1e90ff;
  
  /* Dark mode */
  --dark-bg: #111827;
  --dark-text: #f9fafb;
  --dark-primary: #3b82f6;
  --black-color: #1E1E1E;
  --black-color2: #616161;
  --black-color3: #9a9a9a;
  --black-color4: #d6d6d6;
  --filter-black1: #1A1A1A;
  --filter-black2: #595959;
  --booking-black: #17181C;
  
  /* Typography */
  --font-family-sans: "Geist", sans-serif;
  --font-family-mono: "Geist Mono", monospace;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-sm: 14px;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Shadows & Borders */
  --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
}
