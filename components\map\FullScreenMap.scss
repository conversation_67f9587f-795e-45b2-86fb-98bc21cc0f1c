@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.fullscreen-map {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: z-index(popover); /* High enough to be above most elements but below tooltips */
  display: flex;
  flex-direction: column;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    h2 {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }

    .header-buttons {
      display: flex;
      gap: 8px;
    }

    .close-btn, .minimize-btn {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 18px;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .minimize-btn {
      color: $primary_color;
    }
  }

  &__content {
    flex: 1;
    width: 100%;
    height: calc(100% - 66px); // Subtract header height

    .map-container {
      width: 100%;
      height: 100%;
      border-radius: 0;
    }

    // Override map container styles for fullscreen mode
    :global(.map-container) {
      box-shadow: none;
      border-radius: 0;
    }

    :global(.leaflet-container) {
      height: 100% !important;
      width: 100% !important;
      z-index: z-index(base);
    }

    // Ensure tooltip styles are applied in fullscreen mode
    :global(.hotel-marker-tooltip-container) {
      z-index: z-index(tooltip) !important;

      :global(.hotel-marker-tooltip) {
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
        padding: 6px 10px;
        min-width: 120px;
        max-width: 200px;
        text-align: center;
        border: 1px solid rgba(0, 0, 0, 0.1);
        font-family: 'Open Sans', sans-serif;
        z-index: z-index(popover)-1;

        :global(.hotel-marker-tooltip__name) {
          font-weight: 600;
          font-size: 12px;
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        :global(.hotel-marker-tooltip__details) {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 8px;
        }

        :global(.hotel-marker-tooltip__rating) {
          color: #FFB800;
          font-size: 11px;
        }

        :global(.hotel-marker-tooltip__price) {
          font-weight: 600;
          color: $primary_color;
          font-size: 11px;
        }
      }
    }

    // Ensure leaflet tooltips are visible
    :global(.leaflet-tooltip) {
      z-index: z-index(popover)-1 !important;
    }

    :global(.leaflet-tooltip-pane) {
      z-index: z-index(popover)-1 !important;
    }
  }
}

// RTL support
html[dir="rtl"] {
  .fullscreen-map {
    &__header {
      h2 {
        text-align: right;
      }
    }

    &__content {
      :global(.hotel-marker-tooltip-container) {
        :global(.hotel-marker-tooltip) {
          text-align: right;

          :global(.hotel-marker-tooltip__details) {
            justify-content: flex-start;
            flex-direction: row-reverse;
          }
        }
      }
    }
  }
}
