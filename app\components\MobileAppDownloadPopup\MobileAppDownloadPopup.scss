@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.app-download-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: z-index(modal) + 2;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;
  }

  // Only show on mobile devices
  @media (min-width: 769px) {
    display: none;
  }
}

.app-download-popup {
  background: white;
  width: 100%;
  max-width: 100%;
  border-radius: 20px 20px 0 0;
  padding: 24px 20px 32px;
  position: relative;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.15);

  &.visible {
    transform: translateY(0);
  }

  .popup-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #666;

    &:hover {
      background: rgba(0, 0, 0, 0.15);
      color: #333;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .app-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;

    .icon-container {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #003b95 0%, #006ce4 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 16px rgba(0, 59, 149, 0.3);

      i {
        font-size: 28px;
        color: white;
      }
    }
  }

  .popup-content {
    text-align: center;

    .popup-title {
      font-size: 22px;
      font-weight: 700;
      color: #1a365d;
      margin: 0 0 8px 0;
      line-height: 1.3;
    }

    .popup-description {
      font-size: 15px;
      color: #64748b;
      margin: 0 0 20px 0;
      line-height: 1.5;
      max-width: 280px;
      margin-left: auto;
      margin-right: auto;
    }

    .features-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 24px;
      max-width: 260px;
      margin-left: auto;
      margin-right: auto;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        color: #475569;

        i {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 59, 149, 0.1);
          border-radius: 50%;
          color: #003b95;
          font-size: 10px;
          flex-shrink: 0;
        }
      }
    }

    .download-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 20px;

      .download-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        background: #000;
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        text-decoration: none;
        transition: all 0.2s ease;
        min-height: 56px;

        &:hover {
          background: #333;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        i {
          font-size: 24px;
          flex-shrink: 0;
        }

        .btn-text {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          line-height: 1.2;

          .small-text {
            font-size: 11px;
            opacity: 0.8;
          }

          .main-text {
            font-size: 16px;
            font-weight: 600;
          }
        }
      }
    }

    .dont-show-again {
      background: none;
      border: none;
      color: #64748b;
      font-size: 14px;
      cursor: pointer;
      padding: 8px;
      transition: color 0.2s ease;

      &:hover {
        color: #475569;
      }
    }
  }

  // Landscape mobile adjustments
  @media (max-height: 600px) and (orientation: landscape) {
    .popup-content {
      .features-list {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;

        .feature-item {
          font-size: 12px;
          gap: 8px;

          i {
            width: 16px;
            height: 16px;
            font-size: 8px;
          }
        }
      }

      .download-buttons {
        flex-direction: row;
        gap: 8px;

        .download-btn {
          flex: 1;
          padding: 10px 16px;
          min-height: 48px;

          i {
            font-size: 20px;
          }

          .btn-text {
            .small-text {
              font-size: 10px;
            }

            .main-text {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
