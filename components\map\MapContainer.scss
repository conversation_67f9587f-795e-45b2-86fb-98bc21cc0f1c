@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as *;

.map-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  height: 100%;

  &__header {
    display: flex;
    flex-direction: column;
    gap: 15px;

    h6 {
      font-size: 22px;
      font-weight: 700;
      margin: 0;

      @media (max-width: $breakpoint-md) {
        font-size: 18px;
      }

      @media (max-width: $breakpoint-sm) {
        font-size: 16px;
      }
    }

    .location-details {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      @media (max-width: $isMobile) {
        align-items: start;
        flex-direction: column;
        row-gap: 10px;
      }

      .location {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 5px;

        .fa-location-dot {
          font-size: 16px;
          color: $primary_color;
        }

        p {
          font-size: 16px;
          font-weight: 500;
          margin: 0;

          .fa-location-dot {
            font-size: 16px;
            color: $primary_color;
            margin-right: 5px;
          }

          @media (max-width: $breakpoint-sm) {
            font-size: 14px;
          }
        }
      }

      .view-map-btn {
        padding: 5px 10px;
        background-color: rgba($primary_color, 0.1);
        font-size: 15px;
        font-weight: 600;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba($primary_color, 0.2);
        }
      }
    }
  }

  &__map {
    height: 100%;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;

    .maximize-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: z-index(base);
      background-color: white;
      border: none;
      border-radius: 4px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }

      i {
        font-size: 16px;
        color: $primary_color;
      }
    }
  }
}

// RTL support for map controls
html[dir="rtl"] {
  .map-container {
    &__map {
      .maximize-btn {
        right: auto;
        left: 10px;
      }
    }
  }
}
