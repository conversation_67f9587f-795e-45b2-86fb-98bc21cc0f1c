# Product Context

## Purpose
EcoGO is a hotel booking platform that allows users to search for and book hotels. The application provides a user-friendly interface for searching hotels, viewing hotel details, and making bookings.

## Problems Solved
- Simplifies the hotel search and booking process
- Provides detailed information about hotels including amenities, ratings, and prices
- Offers map-based hotel search to help users find accommodations based on location
- Supports multiple languages and RTL layouts for international users

## How It Works
1. Users search for hotels by location, dates, and number of travelers
2. The application displays a list of available hotels with filtering and sorting options
3. Users can view hotel details including rooms, amenities, and location on a map
4. Users can select rooms and complete the booking process
5. The application provides booking confirmation and itinerary details

## Key Features
- Hotel search with filters (price, rating, amenities)
- Map-based hotel search and visualization
- Detailed hotel information pages
- Room selection and booking
- Multi-language support with RTL capabilities
- Responsive design for desktop and mobile devices
