'use client';

import { Toaster, toast } from 'sonner';

type ToastType = 'success' | 'error' | 'info' | 'warning' | 'loading' | 'default';
type ToastPosition =
  | 'top-right'
  | 'bottom-right'
  | 'top-left'
  | 'bottom-left'
  | 'top-center'
  | 'bottom-center';

export const showToast = (
  message: string,
  type: ToastType = 'success',
  position: ToastPosition = 'top-right'
) => {
  const commonStyle: React.CSSProperties = {
    borderRadius: 'var(--border-radius)',
    fontFamily: 'var(--font-family-sans)',
    fontSize: 'var(--font-size-sm)',
  };

  const styleMap: Record<ToastType, React.CSSProperties> = {
    success: {
      background: 'var(--button-color)',
      color: 'var(--white-color)',
    },
    error: {
      background: 'var(--red-color4)',
      color: 'var(--white-color)',
    },
    info: {
      background: 'var(--secondary-light)',
      color: 'var(--black-color)',
    },
    warning: {
      background: '#facc15',
      color: 'var(--black-color)',
    },
    loading: {
      background: '#1e40af',
      color: 'white',
    },
    default: {
      background: 'var(--primary-color)',
      color: 'white',
    },
  };

  const style = { ...commonStyle, ...styleMap[type] };

  // Use specific toast function based on type
  switch (type) {
    case 'success':
      toast.success(message, { position, style });
      break;
    case 'error':
      toast.error(message, { position, style });
      break;
    case 'info':
      toast.info(message, { position, style });
      break;
    case 'warning':
      toast.warning(message, { position, style });
      break;
    case 'loading':
      toast.loading(message, { position, style });
      break;
    default:
      toast(message, { position, style });
  }
};

// Component that renders all active toaster containers
export const ToastContainers = () => {
  return (
    <>
      <Toaster
        position="top-right"
        toastOptions={{
          style: {
            background: 'var(--primary-light)',
            color: 'var(--black-color)',
            border: '1px solid white',
            fontFamily: 'var(--font-family-sans)',
            borderRadius: 'var(--border-radius)',
          },
        }}
      />
    </>
  );
};
