"use client";

import HotelSearchBar from '@/app/components/hotel/HotelSearchBar/HotelSearchBar'
import HotelSearchMap from '@/app/components/hotel/HotelSearchMap/HotelSearchMap';
import { useCommonContext } from '@/app/contexts/commonContext';
import { HotelFilterData } from '@/app/components/utilities/helpers/hotel/filterHotels';
import React, { useCallback, useEffect, useState } from 'react'
import { Hotel } from '@/models/hotel/list-page.model';
import FilterMapShimmer from './components/shimmers/FilterMapShimmer';
import HotelFilter from '@/app/HotelSearchResult/components/HotelFilter/HotelFilter';
import FilterShimmer from './components/shimmers/FilterShimmer/FilterShimmer';
import SortBarShimmer from './components/shimmers/SortBarShimmer/SortBarShimmer';
import HotelSort from '@/app/components/hotel/HotelSort/HotelSort';

function Page() {
    const [masterHotelList, setMasterHotelList] = useState<Hotel[]>([]);
    const [filteredHotelList, setFilteredHotelList] = useState<Hotel[]>([]);
    const [displayList, setDisplayList] = useState<Hotel[]>([]);
    const [mapDisplayList, setMapDisplayList] = useState<Hotel[]>([]);
    const [priceDistribution, setPriceDistribution] = useState<number[]>([]);
    const [selectedHotels, setSelectedHotels] = useState<Hotel[]>([]);
    const [isMobile, setIsMobile] = useState<boolean>(false);
    const [showFilter, setShowFilter] = useState<boolean>(false);
    const [showMap, setShowMap] = useState<boolean>(false);
    const [showSort, setShowSort] = useState<boolean>(false);
    const [showCardShimmer , setShowCardShimmer] = useState<boolean>(true);
    const [showFilterShimmer, setShowFilterShimmer] = useState<boolean>(true);
    const [showSortShimmer,setshowSortShimmer] = useState<boolean>(false);
    const [isAllowClicks, setIsAllowClicks] = useState<boolean>(false);
    const [filterData, setFilterData] = useState<HotelFilterData | undefined>(undefined);
    const [viewMode, setViewMode] = useState<"list" | "grid">("list");
    // Sort-related state
    const [selectedSort, setSelectedSort] = useState<string>("Top picks for long stays");

    const { setHotelSearchFormData, hotelSearchFormData, hotelSearchData, setSearchKey } = useCommonContext();

    // Sort options
    const sortOptions: string[] = [
        "Top picks for long stays",
        "Homes & Apartments first",
        "Price (lowest first)",
        "Price (highest first)",
        "Best reviewed and lowest price",
        "Property rating (high to low)",
        "Property rating (low to high)",
        "Property rating and price",
        "Distance from city centre",
        "Top reviewed",
    ];

    const defaultSort = "Top picks for long stays";

    // Handle filter changes
    const handleFilterChange = useCallback((newFilterData: HotelFilterData | undefined) => {
        setFilterData(newFilterData);
    }, []);

    // Handle sort changes
    const handleSortChange = useCallback((sortOption: string) => {
        setSelectedSort(sortOption);
        // Here you can add logic to sort the hotels based on the selected option
        // For now, we'll just update the state
        console.log('Sort changed to:', sortOption);
    }, []);

    const getHotelSearchFormData = useCallback(() => {
        if (typeof window !== "undefined") {
        const data = localStorage.getItem("hotelSearchFormData");
        if (data !== null) {
            setHotelSearchFormData(JSON.parse(data));
        }
        }
    }, [setHotelSearchFormData]);

    const generatePriceDistribution = ( hotels: Hotel[],min: number,max: number,bins: number): number[] => {
        const range = max - min;
        const binSize = range / bins;
        const distribution = Array(bins).fill(0);
    
        hotels.forEach((hotel) => {
          const price = hotel.fareDetail?.totalPrice || 0;
          if (price >= min && price <= max) {
            const binIndex = Math.floor((price - min) / binSize);
            const adjustedBinIndex = Math.min(binIndex, bins - 1);
            distribution[adjustedBinIndex]++;
          }
        });
    
        return distribution;
    };




    useEffect(() => {
        const checkIsMobile = () => {
        setIsMobile(window.innerWidth < 950);
        };

        checkIsMobile(); // Initial check

        window.addEventListener("resize", checkIsMobile); // Optional: update on resize

        return () => {
        window.removeEventListener("resize", checkIsMobile); // Cleanup
        };
    }, []);

    useEffect(()=>{
        getHotelSearchFormData();
    },[getHotelSearchFormData])

    useEffect(() => {
        if (filterData && filteredHotelList.length > 0) {
          const distribution = generatePriceDistribution(
            filteredHotelList,
            filterData.priceRange.min,
            filterData.priceRange.max,
            40 // Number of histogram bins
          );
          setPriceDistribution(distribution);
        }
    }, [filteredHotelList, filterData]);

  return (
    <div className="hotel-search-result-container common-container">
        <div className="hotel-search-result">
            <div className="searchbar-container-list">
                <HotelSearchBar
                    isModify={true}
                    shareButtonFlag={true}
                    selectedHotels={selectedHotels}
                />
            </div>
            <div className="route-path">
                <span>Home</span> <i className="fa-solid fa-greater-than"></i>
                <span>{hotelSearchFormData?.searchQuery || "..."}</span>{" "}
                <i className="fa-solid fa-greater-than"></i> Search results
            </div>
            <div className="filter-card-container">
                {!isMobile && (
                    <div className="filter-map-container">
                       {!showFilterShimmer ? (
                            <>
                                <div className="hotel-list-map-container">
                                    <HotelSearchMap
                                        actualHotels={displayList}
                                        filterData={filterData}
                                        onFilterChange={handleFilterChange}
                                    />
                                </div>
                                <div className={`filter ${showFilter ? "active" : ""}`}>
                                    <div className="mobilefilter_close-bttn-container">
                                        <div className="close-bttn" onClick={() => setShowFilter(false)} >
                                            <i className="fa-solid fa-xmark"></i>
                                        </div>
                                    </div> 
                                    <HotelFilter
                                        isSearchList={true}
                                        handleChange={handleFilterChange}
                                        initialFilterData={filterData}
                                        priceDistribution={priceDistribution}
                                    />
                                </div>
                            </>

                       ) : (
                            <>
                                <div className="hotel-list-map-container">
                                    <FilterMapShimmer />
                                </div>
                                <FilterShimmer />
                            </>
                            
                       )}
                    </div>
                )}

                {/* card section */}
                <div className="card">
                    {showSortShimmer ? (
                        <SortBarShimmer />
                    ):(
                        <HotelSort
                            sortOptions={sortOptions}
                            selectedSort={selectedSort}
                            onSortChange={handleSortChange}
                            isMobile={isMobile}
                            defaultSort={defaultSort}
                        />
                    )}

                    {viewMode === "list" ? (
                        showCardShimmer ? (
                                             
                        ) : (

                        )
                    ) : (

                    )}
                </div>
            </div>
        </div>
    </div>
  )
}

export default Page
