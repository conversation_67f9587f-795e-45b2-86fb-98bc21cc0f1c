"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import "./hotel.scss";
import searchBgImage from "../public/assets/img/searchBg.webp";
import ImageSlider from "./components/ImageSlider/ImageSlider";
import DestinationGrid from "./components/DestinationGrid/DestinationGrid";
import FeatureHighlights from "./components/FeatureHighlights/FeatureHighlights";
import Footer from "@/app/components/footer/Footer";
import LandingPageContainer from "./components/LandingPageContainer/LandingPageContainer";
import RecentSearches from "./components/RecentSearches/RecentSearches";
import SpecialOffers from "./components/SpecialOffers/SpecialOffers";
import MobileAppDownload from "./components/MobileAppDownload/MobileAppDownload";
import MobileAppDownloadPopup from "./components/MobileAppDownloadPopup/MobileAppDownloadPopup";
import FAQ from "./components/FAQ/FAQ";

const features = [
  {
    icon: "fa-bed",
    description: "Choose from more than 1000 top-rated hotels worldwide.",
    priceDetails: "From affordable stays to luxury resorts.",
  },
  {
    icon: "fa-star",
    description: "Find hotels with verified guest reviews and high ratings.",
    priceDetails: "Book with confidence every time.",
  },
  {
    icon: "fa-location-dot",
    description: "Stay at prime locations close to attractions and transit.",
    priceDetails: "Convenient hotel options in every city.",
  },
  {
    icon: "fa-tags",
    description: "Enjoy exclusive discounts on hotel bookings.",
    priceDetails: "Best deals for every budget.",
  },
  {
    icon: "fa-shield-halved",
    description: "Safe and secure hotel booking experience.",
    priceDetails: "Verified listings with secure payments.",
  },
  {
    icon: "fa-calendar-check",
    description: "Flexible bookings with free cancellation options.",
    priceDetails: "Book now, cancel later hassle-free.",
  },
];

function Page() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [showAppPopup, setShowAppPopup] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const screenWidth = window.innerWidth;

      // Navigate based on screen width
      if (screenWidth > 768) {
        // Desktop/Tablet - navigate to Hotel page
        router.push('/Hotel');
      } else {
        // Mobile - stay on current page (/)
        setIsMobile(true);
        setIsLoading(false);
      }
    };

    // Check screen size on mount
    handleResize();

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Cleanup event listener
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [router]);

  // Mobile app download popup logic (industry standard)
  useEffect(() => {
    if (isMobile && !isLoading) {
      // Check if user has dismissed popup recently (30 days)
      const hidePopupUntil = localStorage.getItem('hideAppDownloadPopup');
      const shouldShowPopup = !hidePopupUntil || new Date() > new Date(hidePopupUntil);

      if (shouldShowPopup) {
        // Show popup after 3 seconds (industry standard timing)
        const timer = setTimeout(() => {
          setShowAppPopup(true);
        }, 3000);

        return () => clearTimeout(timer);
      }
    }
  }, [isMobile, isLoading]);

  const handleCloseAppPopup = () => {
    setShowAppPopup(false);
  };

  // Show loading state while checking screen size for desktop users
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Render mobile homepage content
  return (
    <div className="hotel-page-container">
      <LandingPageContainer searchBgImageSrc={searchBgImage.src} />

      <div className="common-container">
          <SpecialOffers />
          <ImageSlider />
      </div>
      <MobileAppDownload />
      <FAQ />

      <div className="common-container">
        <FeatureHighlights features={features} />
      </div>

      <Footer />
      
      {/* Mobile App Download Popup */}
      <MobileAppDownloadPopup
        isOpen={showAppPopup}
        onClose={handleCloseAppPopup}
      />
    </div>
  );
}

export default Page;
