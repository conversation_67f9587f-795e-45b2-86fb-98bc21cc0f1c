@import '../../../styles/variable.scss';

.login-signup-container {
  color: $primary-color;
  font-weight: 900;
  
  // Make the popup container responsive
  .max-w-4xl {
    width: 95%;
    max-width: 900px;
  }
  
  // Add responsive padding
  @media (max-width: 640px) {
    padding: 0.75rem;
    
    // Make form elements more compact on small screens
    input, button[type="submit"] {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
  }
  
  // Adjust logo size on small screens
  @media (max-width: 768px) {
    .w-32 {
      width: 8rem;
    }
    
    .h-12 {
      height: 3rem;
    }
  }
  
  // Ensure text is readable on all devices
  label, input, button, a {
    @media (max-width: 640px) {
      font-size: 0.875rem;
    }
    
    @media (max-width: 480px) {
      font-size: 0.75rem;
    }
  }
  
  // Add a bit of extra vertical spacing for content on very small screens
  @media (max-width: 380px) {
    .space-y-3 > * + * {
      margin-top: 0.5rem;
    }
  }
  
  // Enhance focus states for better accessibility
  input:focus, button:focus {
    outline: 2px solid lighten($primary-color, 20%);
    outline-offset: 1px;
  }
}