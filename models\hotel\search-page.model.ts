export interface AutoSuggestionRes {
  locationSuggestions?: LocationSuggestion[] 
  status?: string
  searchKey?: string
  search_key?: string
  provider?: string
  data?: {
    locationSuggestions?: LocationSuggestion[]
    status?: string
    searchKey?: string
    search_key?: string
  }
}

export interface LocationSuggestion {
  id: string
  name: string
  fullName: string
  type: string
  state?: string
  country: string
  coordinates: Coordinates
  referenceScore: number
  code?: string
  city?: string
  referenceId?: string
}

export interface Coordinates {
  lat: number
  long: number
}

// Hotel Search Data Models
export interface HotelSearchData {
  geoCode: GeoCode
  locationId: string
  currency: string
  culture: string
  checkIn: string
  checkOut: string
  rooms: Room[]
}

export interface GeoCode {
  lat: string
  long: string
}

export interface Room {
  adults: string
  children: string
  childAges: number[]
}

// Search API Models
export interface SearchApiRequest {
  search_key: string
}

// Import the SearchInitApiResponse since Search API returns the same structure
import { SearchInitApiResponse } from "@/app/HotelSearchResult/hotel-search-result.model";

export interface SearchApiResponse extends SearchInitApiResponse {
  // Search API returns the same structure as Search Init API
}
