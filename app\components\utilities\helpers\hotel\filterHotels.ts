import {
  Hotel,
  Amenity,
  FomoTag,
  RoomInfo,
  HotelListResponse,
} from "@/app/HotelSearchResult/hotel-search-result.model";

export interface HotelFilterData {
  priceRange: { min: number; max: number; values: { minimum: number; maximum: number; } };
  starRatings: { key: number; count: number; isSelected: boolean }[]; // Array of objects instead of Map
  userRatingCategories: { key: string; isSelected: boolean }[]; // Array of objects instead of Map
  accommodationTypes: { key: string; count: number; isSelected: boolean }[]; // Array of objects instead of Map
  amenities: {
    key: string;
    count: number;
    isSelected: boolean;
    icon?: string;
  }[]; // Array of objects instead of Map
  roomTypes: {
    key: string;
    count: number;
    isSelected: boolean;
    room: RoomInfo;
  }[]; // Array of objects instead of Map
  bedroom: { selected: number; max: number };
  bathroom: { selected: number; max: number };
  distanceFromLandmark: { min: number; max: number };
  specialOffers: { key: string; isSelected: boolean; count: number }[]; // Array of objects instead of Map
  availabilityStatus: {
    limitedRooms: { isSelected: boolean };
    lastMinuteDeals: { isSelected: boolean };
  };
  fomoFilters: { key: string; tags: FomoTag[] }[]; // Array of objects instead of Map
}

const parseDistance = (distanceStr: string): number => {
  const match = distanceStr.match(/(\d+)(m|km)/i);
  if (!match) return 0;
  return match[2].toLowerCase() === "km"
    ? parseInt(match[1]) * 1000
    : parseInt(match[1]);
};

export const createHotelFilters = (
  response: HotelListResponse
): HotelFilterData => {
  // Null check to ensure the response is well-formed
  if (
    !response ||
    !response.data ||
    !response.data.result ||
    !Array.isArray(response.data.result.inventoryInfoList)
  ) {
    throw new Error(
      "Invalid response structure: inventoryInfoList is missing or malformed."
    );
  }

  const hotels: Hotel[] = response.data.result.inventoryInfoList;
  const filterData: HotelFilterData = {
    priceRange: { min: Infinity, max: -Infinity, values: { minimum: 0, maximum: 0}, },
    starRatings: [], // Use an array instead of a Map
    userRatingCategories: [], // Use an array instead of a Map
    accommodationTypes: [], // Use an array instead of a Map
    amenities: [], // Use an array instead of a Map
    roomTypes: [], // Use an array instead of a Map
    bedroom: { selected: 0, max: 0 },
    bathroom: { selected: 0, max: 0 },
    distanceFromLandmark: { min: Infinity, max: -Infinity },
    specialOffers: [], // Use an array instead of a Map
    availabilityStatus: {
      limitedRooms: { isSelected: false },
      lastMinuteDeals: { isSelected: false },
    },
    fomoFilters: [], // Use an array instead of a Map
  };

  const distances: number[] = [];

  hotels.forEach((hotel) => {
    // Price Range
    const price = hotel.fareDetail?.totalPrice ?? 0;
    filterData.priceRange.min = Math.min(filterData.priceRange.min, price);
    filterData.priceRange.max = Math.max(filterData.priceRange.max, price);

    // Initially, set the user-selected range to be the full available range
    filterData.priceRange.values.minimum = filterData?.priceRange?.min;
    filterData.priceRange.values.maximum = filterData?.priceRange?.max;
  

    // Star Ratings
    const existingRating = filterData.starRatings.find(
      (rating) => rating.key === hotel.starRating
    );

    if (existingRating) {
      // If the star rating already exists, increment its count
      existingRating.count += 1;
    } else {
      // If the star rating doesn't exist, add it to the array
      filterData.starRatings.push({
        key: hotel.starRating,
        count: 1,
        isSelected: false,
      });
    }

    // User Rating Categories
    const ratingCategory = hotel.userRatingCategory?.toLowerCase() || "";
    filterData.userRatingCategories.push({
      key: ratingCategory,
      isSelected: false,
    });

    // Accommodation Types
    const accType = hotel.accommodationType?.toLowerCase() || "";
    const accTypeObj = filterData.accommodationTypes.find(
      (acc) => acc.key === accType
    );
    if (accTypeObj) {
      accTypeObj.count += 1;
    } else {
      filterData.accommodationTypes.push({
        key: accType,
        count: 1,
        isSelected: false,
      });
    }

    // Amenities with counts and other values
    hotel.amenities?.forEach(({ name }: Amenity) => {
      const amenity = name?.toLowerCase();
      if (amenity) {
        const amenityObj = filterData.amenities.find((a) => a.key === amenity);
        if (amenityObj) {
          amenityObj.count += 1;
        } else {
          filterData.amenities.push({
            key: amenity,
            count: 1,
            isSelected: false,
            icon: hotel.amenities.find((a) => a.name === name)?.amenityUrl,
          });
        }
      }
    });

    // Room Types
    hotel.roomDetails?.forEach((room) => {
      const roomObj = filterData.roomTypes.find((r) => r.key === room.type);
      if (roomObj) {
        roomObj.count += 1;
      } else {
        filterData.roomTypes.push({
          key: room.type,
          count: 1,
          isSelected: false,
          room: room, // Adding the room object
        });
      }

      // Add bedroom and bathroom count for filtering purposes
      // Update bedroom and bathroom counts separately in filterData
      if (room?.bedroom) {
        // Ensure bedroom exists in filterData and update selected and max values
        if (!filterData.bedroom) {
          filterData.bedroom = {
            selected: 0, // Default to 0 selected
            max: Math.max(...hotel.roomDetails.map((r) => r.bedroom)), // Max bedroom value from all rooms
          };
        }
        filterData.bedroom.max = Math.max(filterData.bedroom.max, room.bedroom); // Update max value
      }

      if (room.bathroom) {
        // Ensure bathroom exists in filterData and update selected and max values
        if (!filterData.bathroom) {
          filterData.bathroom = {
            selected: 0, // Default to 0 selected
            max: Math.max(...hotel.roomDetails.map((r) => r.bathroom)), // Max bathroom value from all rooms
          };
        }
        filterData.bathroom.max = Math.max(
          filterData.bathroom.max,
          room.bathroom
        ); // Update max value
      }
    });

    // Distance from Landmark
    const distance = parseDistance(hotel.distanceFromSearchedEntity ?? "");
    distances.push(distance);

    // Special Offers
    hotel.topOfferings?.forEach((offer) => {
      const offerKey = offer.toLowerCase();
      // Find if the offer already exists in the specialOffers array
      const offerObj = filterData.specialOffers.find((o) => o.key === offerKey);

      if (offerObj) {
        // If the offer exists, increment its count
        offerObj.count += 1;
      } else {
        // If the offer doesn't exist, add a new entry with a count of 1
        filterData.specialOffers.push({
          key: offerKey,
          count: 1,
          isSelected: false,
        });
      }
    });
    // FOMO Tags
    hotel.fomoTags?.forEach((tag) => {
      const existing = filterData.fomoFilters.find(
        (f) => f.key === tag.fomoType
      );
      if (existing) {
        existing.tags.push(tag);
      } else {
        filterData.fomoFilters.push({
          key: tag.fomoType,
          tags: [tag],
        });
      }
    });
  });

  // Set distance range
  if (distances.length > 0) {
    filterData.distanceFromLandmark = {
      min: Math.min(...distances),
      max: Math.max(...distances),
    };
  }

  return filterData;
};

export const resetHotelFilters = (filterData: HotelFilterData): HotelFilterData => {
  return {
    ...filterData,
    priceRange: {
      ...filterData.priceRange,
      values: {
        minimum: filterData.priceRange.min,
        maximum: filterData.priceRange.max,
      },
    },
    starRatings: filterData.starRatings.map((rating) => ({
      ...rating,
      isSelected: false,
    })),
    userRatingCategories: filterData.userRatingCategories.map((category) => ({
      ...category,
      isSelected: false,
    })),
    accommodationTypes: filterData.accommodationTypes.map((type) => ({
      ...type,
      isSelected: false,
    })),
    amenities: filterData.amenities.map((amenity) => ({
      ...amenity,
      isSelected: false,
    })),
    roomTypes: filterData.roomTypes.map((room) => ({
      ...room,
      isSelected: false,
    })),
    bedroom: {
      ...filterData.bedroom,
      selected: 0,
    },
    bathroom: {
      ...filterData.bathroom,
      selected: 0,
    },
    distanceFromLandmark: {
      min: filterData.distanceFromLandmark.min,
      max: filterData.distanceFromLandmark.max,
    },
    specialOffers: filterData.specialOffers.map((offer) => ({
      ...offer,
      isSelected: false,
    })),
    availabilityStatus: {
      limitedRooms: { isSelected: false },
      lastMinuteDeals: { isSelected: false },
    },
    fomoFilters: filterData.fomoFilters.map((fomo) => ({
      ...fomo,
      tags: [],
    })),
  };
};

