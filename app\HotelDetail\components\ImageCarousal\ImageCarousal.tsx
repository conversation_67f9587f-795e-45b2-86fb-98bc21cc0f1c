"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import "./ImageCarousal.scss";
import ImageLightBox from "../ImageLightBox/ImageLightBox";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { ImageList } from "../../hotel-level-details.model";


interface ImageCarousalProps {
  images: ImageList[];

}

const ImageCarousel: React.FC<ImageCarousalProps> = ({ images = [] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hovered, setHovered] = useState(false);
  const [isImgLightBoxOpen, setIsImgLightBoxOpen] = useState<boolean>(false);
  useEffect(() => {
    if (images.length === 0) return;

    
    
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [images.length]);

  const nextSlide = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering image click event
    setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
  };

  const prevSlide = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering image click event
    setCurrentIndex((prevIndex) => (prevIndex - 1 + images.length) % images.length);
  };

  const handleOpenLightbox = () => {
    setIsImgLightBoxOpen(true);
  };

  useScrollLock(isImgLightBoxOpen);

  return (
    <div 
      className="carousel" 
      onMouseEnter={() => setHovered(true)} 
      onMouseLeave={() => setHovered(false)}
    >
      {images.length > 0 ? (
        <>
          <div className="image-wrapper">
            <Image
              onClick={handleOpenLightbox}
              src={images[currentIndex]?.url}
              alt={images[currentIndex]?.caption || "Hotel Image"}
              fill
              style={{ objectFit: "cover" }}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="carousel-image"
            />
          </div>

          {hovered && (
            <>
              {currentIndex > 0 && (
                <button className="arrow left" onClick={prevSlide}>
                  <i className="fa-solid fa-chevron-left"></i>
                </button>
              )}
              {currentIndex < images.length - 1 && (
                <button className="arrow right" onClick={nextSlide}>
                  <i className="fa-solid fa-chevron-right"></i>
                </button>
              )}
            </>
          )}

          <div className="dots">
            {images.map((_, index) => (
              <span
                key={index}
                className={index === currentIndex ? "dot active" : "dot"}
                onClick={() => setCurrentIndex(index)}
              ></span>
            ))}
          </div>

          <div className="image-count"onClick={handleOpenLightbox}>{images.length}+</div>
        </>
      ) : (
        <div className="no-images">No images available</div>
      )}

      {isImgLightBoxOpen && (
        <ImageLightBox 
          images={images}
          onClose={() => setIsImgLightBoxOpen(false)} 
        />
      )}
    </div>
  );
};

export default ImageCarousel;