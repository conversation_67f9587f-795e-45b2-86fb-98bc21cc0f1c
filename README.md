# KindAli Travel & Tourism Web Application

This is a [Next.js](https://nextjs.org) project for KindAli Travel & Tourism, featuring hotel booking, holiday packages, and more.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Multi-Language Support

This application supports multiple languages with full internationalization (i18n) capabilities, including Right-to-Left (RTL) language support.

### Supported Languages

- English (en)
- Arabic (ar) - with RTL support
- Spanish (es)
- French (fr)

### Implementation Details

The multi-language system is built using:

- **i18next**: Core internationalization framework
- **react-i18next**: React bindings for i18next
- **next-i18next**: Next.js integration for i18next
- **Custom context**: For managing language state and direction

### Project Structure

```
├── app/
│   ├── components/
│   │   ├── LanguageDemo/           # Example component showing i18n usage
│   │   └── LanguageLayoutWrapper.tsx  # Handles RTL/LTR direction
│   ├── contexts/
│   │   └── languageContext.tsx     # Context for language management
│   └── hooks/
│       └── useTranslation.ts       # Custom hook for easy translation access
├── i18n.ts                         # i18next configuration
├── next.config.ts                  # Next.js i18n configuration
├── public/
│   └── locales/                    # Translation files
│       ├── en/
│       │   └── common.json         # English translations
│       ├── ar/
│       │   └── common.json         # Arabic translations
│       ├── es/
│       │   └── common.json         # Spanish translations
│       └── fr/
│           └── common.json         # French translations
└── styles/
    └── rtl.scss                    # RTL-specific styles
```

### How to Use Translations

#### 1. In React Components

Use the `useTranslation` hook to access translations:

```tsx
import { useTranslation } from '@/app/hooks/useTranslation';

function MyComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('header.title')}</h1>
      <p>{t('common.description')}</p>
      <button>{t('common.save')}</button>
    </div>
  );
}
```

#### 2. Changing Language

Use the `useLanguage` hook to change the current language:

```tsx
import { useLanguage } from '@/app/contexts/languageContext';

function LanguageSwitcher() {
  const { currentLanguage, changeLanguage } = useLanguage();

  return (
    <div>
      <button onClick={() => changeLanguage('en')}>English</button>
      <button onClick={() => changeLanguage('ar')}>العربية</button>
      <button onClick={() => changeLanguage('es')}>Español</button>
      <button onClick={() => changeLanguage('fr')}>Français</button>
    </div>
  );
}
```

#### 3. RTL Support

The `useLanguage` hook provides an `isRTL` flag for RTL-specific styling:

```tsx
import { useLanguage } from '@/app/contexts/languageContext';

function RTLAwareComponent() {
  const { isRTL } = useLanguage();

  return (
    <div className={isRTL ? 'rtl-container' : 'ltr-container'}>
      {/* Your content */}
    </div>
  );
}
```

### Translation Files

Translation files are JSON objects with nested keys. Example structure:

```json
{
  "header": {
    "title": "Welcome to KindAli",
    "nav": {
      "hotels": "Hotels",
      "holidays": "Holidays"
    }
  },
  "common": {
    "loading": "Loading...",
    "save": "Save",
    "cancel": "Cancel"
  }
}
```

### Adding New Languages

To add a new language:

1. Create a new translation file in `public/locales/[language-code]/common.json`
2. Add the language code to the `languages` array in `i18n.ts`
3. Add the language display name to the `languageMap` in `i18n.ts`
4. Import the new translation file in `i18n.ts` and add it to the resources object

### Demo Page

Visit `/language-demo` to see a demonstration of the multi-language functionality.

## Learn More

To learn more about Next.js and the technologies used in this project:

- [Next.js Documentation](https://nextjs.org/docs)
- [i18next Documentation](https://www.i18next.com/)
- [react-i18next Documentation](https://react.i18next.com/)

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
