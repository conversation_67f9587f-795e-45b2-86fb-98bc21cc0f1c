"use client";
import React from "react";
import "./shimmer.scss";
import PriceShimmer from "@/app/HotelSearchResult/components/HotelCard/PriceShimmer";

interface HotelCardShimmerProps {
  type?: "list" | "grid";
}

const HotelCardShimmer: React.FC<HotelCardShimmerProps> = ({ type = "list" }) => {
  return (
    <div className={`relative bg-white rounded-lg shadow-lg border border-gray-100 overflow-hidden ${
      type === "grid"
        ? "max-w-sm mx-auto mb-6"
        : "mb-6"
    }`}>

      <div className={`flex flex-col ${type === "grid" ? "" : "md:flex-row"} ${type === "grid" ? "min-h-auto" : "min-h-[280px]"}`}>
        {/* Image Section - Matches HotelCard exactly */}
        <div className={`relative ${type === "grid" ? "w-full h-48" : "md:w-[30%] h-48 md:h-auto"}`}>
          <div className="shimmer w-full h-full bg-gray-200"></div>
          {/* Heart Icon Shimmer */}
          <div className="absolute top-3 right-3 bg-white bg-opacity-70 p-2 rounded-full">
            <div className="shimmer w-5 h-5 rounded bg-gray-200"></div>
          </div>
        </div>

        {/* Content Section - Matches HotelCard exactly */}
        <div className={`p-4 ${type === "grid" ? "w-full border-b border-gray-200" : "md:p-6 md:w-[45%] border-b md:border-b-0 md:border-r border-gray-200"}`}>
          {/* Hotel Name & Type */}
          <div className="mb-3">
            <div className="flex items-center gap-2 mb-1">
              <div className="shimmer h-6 w-48 bg-gray-200 rounded"></div>
              <div className="shimmer h-5 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="flex items-center mt-1">
              <div className="flex gap-1 mr-2">
                <div className="shimmer w-4 h-4 bg-gray-200 rounded"></div>
                <div className="shimmer w-4 h-4 bg-gray-200 rounded"></div>
                <div className="shimmer w-4 h-4 bg-gray-200 rounded"></div>
                <div className="shimmer w-4 h-4 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="mb-4">
            <div className="flex items-center mb-1">
              <div className="shimmer w-3.5 h-3.5 bg-gray-200 rounded mr-2"></div>
              <div className="shimmer h-4 w-32 bg-gray-200 rounded"></div>
            </div>
            <div className="flex items-center">
              <div className="shimmer w-3.5 h-3.5 bg-gray-200 rounded mr-2"></div>
              <div className="shimmer h-4 w-40 bg-gray-200 rounded"></div>
            </div>
          </div>

          {/* Badges */}
          <div className="flex flex-wrap gap-2 mb-4">
            <div className="shimmer h-6 w-24 bg-gray-200 rounded-full"></div>
            <div className="shimmer h-6 w-20 bg-gray-200 rounded-full"></div>
            <div className="shimmer h-6 w-18 bg-gray-200 rounded-full"></div>
          </div>

          {/* Amenities */}
          <div className="flex flex-wrap gap-x-4 gap-y-1 mb-2">
            <div className="flex items-center">
              <div className="shimmer w-4 h-4 bg-gray-200 rounded mr-2"></div>
              <div className="shimmer h-4 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="flex items-center">
              <div className="shimmer w-4 h-4 bg-gray-200 rounded mr-2"></div>
              <div className="shimmer h-4 w-20 bg-gray-200 rounded"></div>
            </div>
            <div className="flex items-center">
              <div className="shimmer w-4 h-4 bg-gray-200 rounded mr-2"></div>
              <div className="shimmer h-4 w-24 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>

        {/* Pricing Section - Matches HotelCard exactly */}
        <div className={`p-4 ${type === "grid" ? "w-full" : "md:p-6 md:w-[25%]"} flex flex-col justify-between`}>
          {type === "grid" ? (
            /* Grid Layout - Split container like HotelCard */
            <div className="flex justify-between items-start mb-3">
              {/* Left side - Rating */}
              <div className="flex items-center gap-2">
                <div>
                  <div className="shimmer h-4 w-12 bg-gray-200 rounded mb-1"></div>
                  <div className="shimmer h-3 w-16 bg-gray-200 rounded"></div>
                </div>
                <div className="shimmer h-8 w-10 bg-gray-200 rounded"></div>
              </div>

              {/* Right side - Pricing */}
              <div className="text-right">
                <PriceShimmer layout="grid" />
              </div>
            </div>
          ) : (
            /* List Layout - Only rating badge like HotelCard */
            <>
              <div className="flex items-center justify-end gap-2 mb-3">
                <div className="text-right">
                  <div className="shimmer h-4 w-12 bg-gray-200 rounded mb-1"></div>
                  <div className="shimmer h-3 w-16 bg-gray-200 rounded"></div>
                </div>
                <div className="shimmer h-8 w-10 bg-gray-200 rounded"></div>
              </div>

              {/* Pricing section for list view only */}
              <div className="space-y-2">
                <PriceShimmer layout="list" />
                <div className="shimmer h-10 w-full bg-gray-200 rounded-lg"></div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HotelCardShimmer;
