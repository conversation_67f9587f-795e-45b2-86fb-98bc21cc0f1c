'use client'
import Image from 'next/image';
// import { FC } from 'react';
import {
  Calendar,
  MapPin,
  Star,
  Bed,
  User,
  Mail,
  Phone,
  Wifi,
  Coffee,
//   Pool,
//   Parking,
  Flower2,
  <PERSON><PERSON><PERSON>,
  CreditCard,
  Check,
  Printer,
  Download,
  Info
} from 'lucide-react';
import { useTranslation } from '@/app/hooks/useTranslation';
import { useLanguage } from '@/app/contexts/languageContext';

interface ItineraryProps {
  data: ItineraryData
}

interface ItineraryData {
  bookingId: string;
  hotelName: string;
  hotelAddress: string;
  hotelRating: number;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  roomType: string;
  guestCount: {
    rooms: number;
    adults: number;
  };
  primaryGuest: {
    title: string;
    name: string;
    email: string;
    phone: string;
  };
  priceDetails: {
    roomRate: number;
    nights: number;
    taxesAndFees: number;
    resortFee: number;
  };
  paymentInfo: {
    isPaid: boolean;
    cardType: string;
    cardLastDigits: string;
  };
  cancellationDeadline: string;
}

export default function Itinerary() {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Sample data matching the screenshot
  const data = {
    bookingId: "**********",
    hotelName: "The Luxury Collection Hotel",
    hotelAddress: "123 Palm Jumeirah, Dubai, UAE",
    hotelRating: 5,
    checkInDate: "April 28, 2025",
    checkOutDate: "May 1, 2025",
    checkInTime: "After 2:00 PM",
    checkOutTime: "Before 12:00 PM",
    roomType: "Deluxe Ocean View Suite",
    guestCount: {
      rooms: 1,
      adults: 2,
    },
    primaryGuest: {
      title: "Mr.",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+****************",
    },
    priceDetails: {
      roomRate: 750,
      nights: 3,
      taxesAndFees: 90,
      resortFee: 45,
    },
    paymentInfo: {
      isPaid: true,
      cardType: "Visa",
      cardLastDigits: "4567",
    },
    cancellationDeadline: "April 26, 2025",
  };

  const totalAmount =
    data.priceDetails.roomRate * data.priceDetails.nights +
    data.priceDetails.taxesAndFees +
    data.priceDetails.resortFee;

  // Generate stars based on rating
  const renderStars = () => {
    const stars = [];
    for (let i = 0; i < data.hotelRating; i++) {
      stars.push(<Star key={i} size={16} fill="#FBBF24" color="#FBBF24" />);
    }
    return stars;
  };

  return (
    <div className="py-8 px-4 bg-gray-50">
      <div className="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-1">
              {t('itinerary.bookingConfirmation')}
            </h2>
            <p className="text-gray-600">
              {t('itinerary.bookingId')}: {data.bookingId}
            </p>
            <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-700">
              <Check size={16} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
              {t('itinerary.confirmed')}
            </div>
          </div>
          <div className="flex flex-col space-y-2">
            <button className="flex items-center text-gray-600 hover:text-gray-800">
              <Printer size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} /> {t('itinerary.print')}
            </button>
            <button className="flex items-center text-gray-600 hover:text-gray-800">
              <Download size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} /> {t('itinerary.downloadPdf')}
            </button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          <div className="md:w-2/3">
            {/* Hotel Information */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <Image
                  width={80}
                  height={80}
                  src="/api/placeholder/160/160"
                  alt="Hotel Room"
                  className={`w-20 h-20 rounded-lg object-cover ${isRTL ? 'ml-4' : 'mr-4'}`}
                />
                <div>
                  <h3 className="text-lg font-bold text-gray-800">
                    {data.hotelName}
                  </h3>
                  <p className="text-gray-600 mb-1 flex items-center">
                    <MapPin size={16} className={`${isRTL ? 'ml-1' : 'mr-1'} text-amber-700`} /> {data.hotelAddress}
                  </p>
                  <div className="flex items-center text-sm">
                    <div className={`flex items-center ${isRTL ? 'ml-2' : 'mr-2'}`}>
                      {renderStars()}
                    </div>
                    <span className="text-gray-600">{data.hotelRating}{t('itinerary.starHotel')}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Stay Details */}
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                {t('itinerary.stayDetails')}
              </h4>
              <div className="flex flex-wrap">
                <div className="w-full md:w-1/2 mb-4">
                  <p className="text-gray-600">{t('itinerary.checkIn')}</p>
                  <div className="flex items-center">
                    <Calendar size={16} className={`text-amber-700 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span className="font-medium">{data.checkInDate}</span>
                    <span className={`text-gray-500 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      ({data.checkInTime})
                    </span>
                  </div>
                </div>
                <div className="w-full md:w-1/2 mb-4">
                  <p className="text-gray-600">{t('itinerary.checkOut')}</p>
                  <div className="flex items-center">
                    <Calendar size={16} className={`text-amber-700 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span className="font-medium">{data.checkOutDate}</span>
                    <span className={`text-gray-500 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      ({data.checkOutTime})
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <p className="text-gray-600">{t('itinerary.roomType')}</p>
                <div className="flex items-center">
                  <Bed size={16} className={`text-amber-700 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  <span className="font-medium">
                    {data.roomType}
                  </span>
                  <span className={`text-gray-500 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                    ({data.guestCount.rooms} {t(data.guestCount.rooms === 1 ? 'itinerary.room' : 'itinerary.rooms')},
                    {data.guestCount.adults} {t(data.guestCount.adults === 1 ? 'itinerary.adult' : 'itinerary.adults')})
                  </span>
                </div>
              </div>
            </div>

            {/* Guest Details */}
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                {t('itinerary.guestDetails')}
              </h4>
              <div className="space-y-3">
                <div className="flex items-center">
                  <User size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <div>
                    <p className="font-medium">{t('itinerary.primaryGuest')}</p>
                    <p className="text-gray-600">{data.primaryGuest.title} {data.primaryGuest.name}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Mail size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <p className="text-gray-600">{data.primaryGuest.email}</p>
                </div>
                <div className="flex items-center">
                  <Phone size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <p className="text-gray-600">{data.primaryGuest.phone}</p>
                </div>
              </div>
            </div>

            {/* Included Amenities */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                {t('itinerary.includedAmenities')}
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <Wifi size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <span className="text-gray-600">{t('itinerary.freeWifi')}</span>
                </div>
                <div className="flex items-center">
                  <Coffee size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <span className="text-gray-600">
                    {t('itinerary.breakfastIncluded')}
                  </span>
                </div>
                <div className="flex items-center">
                  <Coffee size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <span className="text-gray-600">{t('itinerary.poolAccess')}</span>
                </div>
                <div className="flex items-center">
                  <Coffee size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <span className="text-gray-600">{t('itinerary.freeParking')}</span>
                </div>
                <div className="flex items-center">
                  <Flower2 size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <span className="text-gray-600">{t('itinerary.spaAccess')}</span>
                </div>
                <div className="flex items-center">
                  <Dumbbell size={16} className={`text-amber-700 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <span className="text-gray-600">{t('itinerary.fitnessCenter')}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="md:w-1/3">
            {/* Price Details */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                {t('itinerary.priceDetails')}
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">
                    {t('itinerary.roomRate')} ({data.priceDetails.nights} {t('itinerary.nights')})
                  </span>
                  <span className="font-medium">${data.priceDetails.roomRate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t('itinerary.taxesAndFees')}</span>
                  <span className="font-medium">${data.priceDetails.taxesAndFees}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">{t('itinerary.resortFee')}</span>
                  <span className="font-medium">${data.priceDetails.resortFee}</span>
                </div>
                <div className="pt-3 border-t border-gray-200 flex justify-between">
                  <span className="font-bold">{t('itinerary.totalAmount')}</span>
                  <span className="font-bold text-amber-700">${totalAmount}</span>
                </div>
              </div>
            </div>

            {/* Payment Status */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                {t('itinerary.paymentStatus')}
              </h4>
              <div className="space-y-3">
                <div className="flex items-center text-green-600">
                  <Check size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  <span className="font-medium">
                    {t('itinerary.paidInFull')}
                  </span>
                </div>
                <div className="text-gray-600">
                  <p className="mb-1">{t('itinerary.paymentMethod')}</p>
                  <div className="flex items-center">
                    <CreditCard size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span>{data.paymentInfo.cardType} {t('itinerary.cardEnding')} {data.paymentInfo.cardLastDigits}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Cancellation Policy */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">
                {t('itinerary.cancellationPolicy')}
              </h4>
              <p className="text-gray-600 text-sm">
                {t('itinerary.freeCancellation')} {data.cancellationDeadline} (48 hours before
                check-in). {t('itinerary.cancellationFee')}
              </p>
              <button className="mt-4 w-full border border-red-500 text-red-500 py-2 px-4 rounded hover:bg-red-50">
                {t('itinerary.cancelBooking')}
              </button>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <div className="flex items-start">
            <Info size={16} className={`text-blue-500 mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div>
              <h4 className="text-lg font-semibold text-gray-800 mb-2">
                {t('itinerary.needAssistance')}
              </h4>
              <p className="text-gray-600 mb-3">
                {t('itinerary.supportMessage')}
              </p>
              <button className="bg-white text-blue-500 hover:bg-blue-500 hover:text-white px-4 py-2 rounded transition-colors">
                {t('itinerary.contactSupport')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}