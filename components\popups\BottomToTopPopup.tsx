"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import { createPortal } from "react-dom";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import "./BottomToTopPopup.scss";

// TypeScript Interfaces
export interface BottomToTopProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  heading?: string;
  type: 'search' | 'calendar' | 'default';
  enableDrag?: boolean;
  snapPoints?: number[];
}

interface DragState {
  isDragging: boolean;
  currentHeight: number;
  startY: number;
  startHeight: number;
}

const BottomToTopPopup: React.FC<BottomToTopProps> = ({
  children,
  isOpen,
  onClose,
  heading,
  type = 'default',
  enableDrag = true,
  snapPoints = [25, 50, 75, 95]
}) => {
  // State Management
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    currentHeight: 0,
    startY: 0,
    startHeight: 0
  });
  
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  
  // Refs
  const popupRef = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);
  
  // Scroll lock when popup is open
  useScrollLock(isOpen);

  // Initial Height Logic
  const getInitialHeight = useCallback((): number => {
    switch (type) {
      case 'search':
        return 100;
      case 'calendar':
        return 85;
      default:
        return 60;
    }
  }, [type]);

  // Mount detection for portal
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Initialize height when popup opens
  useEffect(() => {
    if (isOpen) {
      const initialHeight = getInitialHeight();
      setDragState(prev => ({
        ...prev,
        currentHeight: initialHeight
      }));
      setIsVisible(true);
      
      // Add popup-open class to body
      document.body.classList.add('popup-open');
    } else {
      setIsVisible(false);
      // Remove popup-open class from body
      document.body.classList.remove('popup-open');
      
      // Reset height when closing
      setTimeout(() => {
        setDragState(prev => ({
          ...prev,
          currentHeight: getInitialHeight()
        }));
      }, 400); // After transition completes
    }
    
    return () => {
      document.body.classList.remove('popup-open');
    };
  }, [isOpen, getInitialHeight]);

  // Snap to nearest point
  const snapToNearestPoint = useCallback((height: number): number => {
    const closest = snapPoints.reduce((prev, curr) => 
      Math.abs(curr - height) < Math.abs(prev - height) ? curr : prev
    );
    return closest;
  }, [snapPoints]);

  // Drag Event Handlers
  const handleDragStart = useCallback((clientY: number) => {
    if (!enableDrag) return;
    
    setDragState(prev => ({
      ...prev,
      isDragging: true,
      startY: clientY,
      startHeight: prev.currentHeight
    }));
    
    // Prevent text selection during drag
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
  }, [enableDrag]);

  const handleDragMove = useCallback((clientY: number) => {
    if (!dragState.isDragging) return;
    
    // Calculate delta (inverted for upward movement)
    const deltaY = dragState.startY - clientY;
    
    // Convert to percentage
    const deltaPercent = (deltaY / window.innerHeight) * 100;
    
    // Calculate new height and constrain it
    const newHeight = Math.max(10, Math.min(98, dragState.startHeight + deltaPercent));
    
    setDragState(prev => ({
      ...prev,
      currentHeight: newHeight
    }));
  }, [dragState.isDragging, dragState.startY, dragState.startHeight]);

  const handleDragEnd = useCallback(() => {
    if (!dragState.isDragging) return;
    
    // Restore text selection
    document.body.style.userSelect = '';
    document.body.style.webkitUserSelect = '';
    
    // Check if should close (below 20% threshold)
    if (dragState.currentHeight < 20) {
      onClose();
      return;
    }
    
    // Snap to nearest point
    const snappedHeight = snapToNearestPoint(dragState.currentHeight);
    
    setDragState(prev => ({
      ...prev,
      isDragging: false,
      currentHeight: snappedHeight
    }));
  }, [dragState.isDragging, dragState.currentHeight, snapToNearestPoint, onClose]);

  // Mouse Events
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    handleDragStart(e.clientY);
  }, [handleDragStart]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    handleDragMove(e.clientY);
  }, [handleDragMove]);

  const handleMouseUp = useCallback(() => {
    handleDragEnd();
  }, [handleDragEnd]);

  // Touch Events
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    if (touch) {
      handleDragStart(touch.clientY);
    }
  }, [handleDragStart]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    if (touch) {
      handleDragMove(touch.clientY);
    }
  }, [handleDragMove]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    e.preventDefault();
    handleDragEnd();
  }, [handleDragEnd]);

  // Global Event Listeners
  useEffect(() => {
    if (dragState.isDragging) {
      // Add global listeners when dragging
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd, { passive: false });
      
      return () => {
        // Clean up listeners
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [dragState.isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  // Handle overlay click to close
  const handleOverlayClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose]);

  // Don't render anything if not mounted (SSR safety)
  if (!isMounted) return null;

  const popupContent = (
    <div 
      className={`bottom-to-top-popup-container ${isVisible ? 'visible' : ''} ${isOpen ? 'show' : 'hide'}`}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby={heading ? "popup-heading" : undefined}
    >
      <div 
        className="dy-bootom-up-popup-overlay"
        onClick={handleOverlayClick}
      />
      <div 
        ref={popupRef}
        className={`dy-bootom-up-popup-div ${dragState.isDragging ? 'dragging' : ''} ${type}`}
        style={{
          height: `${dragState.currentHeight}vh`,
          transition: dragState.isDragging ? 'none' : 'transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), height 0.3s ease'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Draggable Handle */}
        {enableDrag && (
          <div 
            ref={dragHandleRef}
            className="header-menu-div"
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
            role="button"
            tabIndex={0}
            aria-label="Drag to resize popup"
            style={{
              cursor: dragState.isDragging ? 'grabbing' : 'grab'
            }}
          >
            <div className="drag-indicator" />
            {heading && (
              <h2 id="popup-heading" className="popup-heading">
                {heading}
              </h2>
            )}
          </div>
        )}
        
        {/* Content Body */}
        <div className="body-div">
          {children}
        </div>
      </div>
    </div>
  );

  return createPortal(popupContent, document.body);
};

export default BottomToTopPopup;
