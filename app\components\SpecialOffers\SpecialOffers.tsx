"use client";
import React from "react";
import Image from "next/image";

interface SpecialOffer {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  promoCode: string;
  image: string;
}

// Promotional banner style offers - exact match to your image
const specialOffers: SpecialOffer[] = [
  {
    id: "1",
    title: "Up to 25% Off",
    subtitle: "On Domestic Hotels",
    description: "*Offers is Valid On UPI Transactions Only",
    promoCode: "YTUPI",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Kerala.jpg"
  },
  {
    id: "2",
    title: "Up to 35% OFF",
    subtitle: "On International Hotels",
    description: "*Offers is Valid Only On Confirmed Hotel Bookings",
    promoCode: "YTICICEMI",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/goa.jpg"
  },
  {
    id: "3",
    title: "Up to 30% Off",
    subtitle: "On Weekend Bookings",
    description: "*Offer Valid On Axis Bank Credit Card EMI Transactions Only",
    promoCode: "YRAXISEMI",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Dubai.png"
  },
  {
    id: "4",
    title: "Up to 40% OFF",
    subtitle: "On Luxury Resorts",
    description: "*Valid on HDFC Bank Debit & Credit Cards",
    promoCode: "YHDFC40",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Maldives.png"
  },
  {
    id: "5",
    title: "Up to 20% Off",
    subtitle: "On Hill Station Hotels",
    description: "*Weekend Special Offer Valid Till Sunday",
    promoCode: "WEEKEND20",
    image: "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Himachal.png"
  }
];

function SpecialOffers() {
  const handleOfferClick = (offer: SpecialOffer) => {
    console.log("Clicked special offer:", offer);
    // Implement navigation to search with offer applied
  };

  const handleViewDetails = (e: React.MouseEvent, offer: SpecialOffer) => {
    e.stopPropagation();
    console.log("View details for:", offer.promoCode);
    // Implement view details logic
  };

  if (specialOffers.length === 0) {
    return null;
  }

  return (
    <div className="w-full  mt-[30px] md:mt-10">
      <div className="flex justify-between items-center mb-6 md:mb-8 sm:flex-col sm:items-start sm:gap-2">
        <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 md:text-2xl">Special Offers</h3>
        {/* <button className="bg-transparent border border-gray-300 text-gray-600 text-[13px] font-medium px-3 py-[6px] rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:text-gray-700 sm:self-end sm:text-xs sm:px-[10px] sm:py-[5px] md:text-sm md:px-4 md:py-2">
          View all
        </button> */}
      </div>

      <div className="relative">
        <div className="flex gap-5 overflow-x-auto pb-4 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden md:gap-4">
          {specialOffers.map((offer) => (
            <div
              key={offer.id}
              className="flex-[0_0_auto] w-[280px] h-[100px] bg-white rounded-2xl overflow-hidden shadow-[0_4px_16px_rgba(0,0,0,0.1)] transition-all duration-300 cursor-pointer flex hover:-translate-y-0.5 hover:shadow-[0_8px_24px_rgba(0,0,0,0.15)] md:w-[380px] md:h-[130px] lg:w-[420px] lg:h-[140px]"
              onClick={() => handleOfferClick(offer)}
            >
              <div className="flex-[0_0_90px] relative md:flex-[0_0_130px] lg:flex-[0_0_140px]">
                <div className="relative w-full h-full rounded-l-2xl overflow-hidden after:content-[''] after:absolute after:top-0 after:-right-5 after:w-10 after:h-full after:bg-white after:rounded-[50%] after:z-[2]">
                  <Image
                    src={offer.image}
                    alt={offer.title}
                    fill
                    className="object-cover transition-transform duration-300 hover:scale-105"
                  />
                </div>
              </div>

              <div className="flex-1 px-3 py-[10px] flex flex-col justify-between min-h-0 md:px-5 md:py-4 lg:px-[22px] lg:py-[18px]">
                <div className="mb-[14px] overflow-hidden">
                  <h3 className="text-[13px] font-bold text-[#1a1a1a] my-0 mb-[5px] leading-[1.25] whitespace-nowrap overflow-hidden text-ellipsis md:text-[17px] lg:text-[19px] lg:whitespace-normal">{offer.title}</h3>
                  <p className="text-[10px] text-[#555555] my-0 mb-[7px] font-medium leading-[1.3] whitespace-nowrap overflow-hidden text-ellipsis md:text-xs lg:text-[13px] lg:whitespace-normal">{offer.subtitle}</p>
                  <p className="text-[8px] text-[#777777] m-0 leading-[1.4] [-webkit-box-orient:vertical] overflow-hidden text-ellipsis [display:-webkit-box] [-webkit-line-clamp:1] md:text-[10px] md:[-webkit-line-clamp:2] lg:[-webkit-line-clamp:2]">{offer.description}</p>
                </div>

                <div className="flex justify-between items-center mt-3">
                  <button className="bg-gradient-to-br from-[#ff6b6b] to-[#ff5252] border-0 text-white text-[9px] font-bold px-2 py-1 rounded-[20px] cursor-pointer transition-all duration-200 uppercase tracking-[0.5px] hover:bg-gradient-to-br hover:from-[#ff5252] hover:to-[#ff4444] hover:-translate-y-px md:text-xs md:px-4 md:py-2 lg:px-4 lg:py-2">
                    {offer.promoCode}
                  </button>
                  <button
                    className="bg-transparent border-0 text-[#007bff] text-[10px] font-semibold cursor-pointer flex items-center gap-1 transition-all duration-200 hover:text-[#0056b3] md:text-[13px]"
                    onClick={(e) => handleViewDetails(e, offer)}
                  >
                    View Details
                    <i className="fa-solid fa-chevron-right text-[10px] transition-transform duration-200 hover:translate-x-0.5"></i>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default SpecialOffers;
