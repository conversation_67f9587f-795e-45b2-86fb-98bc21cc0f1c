@use '@styles/variable' as *;

.container-signup {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f4f4f4;
  }
  .signup-container {
    display: flex;
    width: 60%;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
  }
    @media screen and (max-width: 768px) {
      .signup-container {
        width: 95%;
        flex-direction: column;
      }
    }
  .left-section {
    flex: 1;
    padding: 40px;
    background-color: $primary-color;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    @media screen and (max-width: 768px) {
      padding: 10px;
    }
  }
  .right-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    @media screen and (max-width: 768px) {
      padding: 10px;
    }
  }
  .signup-form {
    display: flex;
    flex-direction: column;
  }
  .signup-form h2 {
    margin-bottom: 20px;
    text-align: center;
  }
  .signup-form input {
    margin-bottom: 15px;
    padding: 12px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  .phone-input {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .country-code {
    padding: 12px;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 4px 0 0 4px;
    font-size: 16px;
    margin-right: -1px;
    margin-bottom: 15px;
  }
  .phone-input input {
    flex: 1;
    border-radius: 0 4px 4px 0;
  }
  .terms-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .terms-link {
    color: #1976d2;
    cursor: pointer;
    margin-left: 5px;
    text-decoration: underline;
  }
  .terms-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .terms-popup-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    width: 50%;
    text-align: center;
    @media screen and (max-width: 768px) {
      width: 95%;
      padding: 10px;
    }
  }
  .terms-popup-content h2 {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    text-align: justify;
  }
  .terms-popup-content p {
    margin-bottom: 20px;
    text-align: justify;
    font-size: 12px;
  }
  .terms-popup-content button {
    padding: 10px 20px;
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  .checkbox{
    margin-bottom: 0 !important;
    margin-right: 10px;
  }