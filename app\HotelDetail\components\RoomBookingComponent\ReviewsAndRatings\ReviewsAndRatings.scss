@use "sass:color";
@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.ratings-reviews-container {
  width: 100%;
  padding: 20px 0;
  display: flex;
  flex-direction: row;
  gap: 30px;

  @media (max-width: $isMobile) {
    flex-direction: column;
    padding: 0;
  }

  .ratings {
    width: 35%;

    @media (max-width: $isMobile) {
      width: 100%;
    }

    .rating-summary {
      display: flex;
      flex-direction: row;
      margin-bottom: 30px;

      .rating-summary__score {
        font-size: 40px;
        font-weight: 700;
        padding: 10px;
        color: #17181c;
        margin-right: 10px;
      }

      .rating-summary__details {
        display: flex;
        flex-direction: column;

        .rating-summary__label {
          font-size: 16px;
          color: #17181c;
          font-weight: 600;
        }

        .rating-summary__count {
          font-size: 14px;
          color: #5e616e;
          font-weight: 500;
        }
      }
    }

    .rating-bar-container {
      display: flex;
      flex-direction: column;
      gap: 30px;
      margin-bottom: 20px;

      .rating-bar {
        display: flex;
        flex-direction: column;

        .rating-bar__progress {
          width: 100%;
          height: 6px;
          background-color: #d6d7db;
          margin-bottom: 5px;
          border-radius: 5px;
        }

        .rating-bar__fill {
          height: 100%;
          border-radius: 5px;
          background-color: #3a3c45;
        }

        .rating-bar__label-score {
          width: 100%;
          display: flex;
          flex-direction: row;
          justify-content: space-between;

          span {
            font-size: 12px;
            font-weight: 600;
            color: #3a3c45;
          }
        }
      }
    }

    .hotel-highlights {
      &__title {
        font-size: 20px;
        font-weight: 600;
        color: #17181c;

        @media (max-width: $breakpoint-md) {
          font-size: 18px;
        }

        @media (max-width: $breakpoint-sm) {
          font-size: 16px;
        }

        @media (max-width: $breakpoint-xs) {
          font-size: 15px;
        }

        .fa-thumbs-up {
          font-size: 18px;
        }
      }

      &__tags {
        margin: 15px 0 0 0;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
      }

      &__tag {
        padding: 2px 4px;
        font-size: 14px;
        font-weight: 500;
        color: #17181c;
        border: 1px solid rgba(0, 0, 0, 0.4);
        border-radius: 9999px;

        span {
          padding: 0 5px;
        }
      }
    }
  }

  .reviews {
    width: 65%;
    @media (max-width: $isMobile) {
      width: 100%;
    }

    .review-filter {
      &__title {
        font-size: 20px;
        color: #17181c;
        font-weight: 800;
        margin-bottom: 15px;

        @media (max-width: $breakpoint-md) {
          font-size: 18px;
        }

        @media (max-width: $breakpoint-sm) {
          font-size: 16px;
        }

        @media (max-width: $breakpoint-xs) {
          font-size: 15px;
        }
      }

      &__categories {
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        margin-bottom: 20px;
        overflow-y: auto;
        column-gap: 10px;

        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }
      }

      &__category {
        padding: 2px 4px;
        font-size: 14px;
        font-weight: 600;
        color: #17181c;
        border: 1px solid rgba(0, 0, 0, 0.4);
        border-radius: 9999px;
        white-space: nowrap;
        transition: background-color 0.2s ease;

        span {
          padding: 0 5px;
          user-select: none;
        }

        &:hover {
          background-color: color.adjust(white, $lightness: -10%);
        }

        &.active {
          //color: #0770e4;
          color: $secondary_color;
          border: 1px solid $primary-color;
          background-color: #f0fafa;
          cursor: pointer;
        }
      }
    }

    .review-container {
      .review {
        padding: 0 0 20px 0;
        margin: 0 0 20px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.2);
        cursor: pointer;

        .review-title {
          font-size: 20px;
          font-weight: 600;
          color: #17181c;
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 10px;
          margin-bottom: 5px;

          @media (max-width: $breakpoint-md) {
            font-size: 16px;
          }
          @media (max-width: $breakpoint-md) {
            font-size: 15px;
          }

          .review-rating {
            padding: 2px 5px;
            color: #17181c;
            font-size: 16px;
            font-weight: 500;
            background-color: #efeff0;
            border-radius: 5px;
          }
        }

        .review-text {
          font-size: 16px;
          line-height: 1.4;
          color: #17181c;
          margin: 0 0 10px 0;
          font-weight: 500;

          @media (max-width: $breakpoint-sm) {
            font-size: 14px;
          }
        }

        .review-user-meta {
          display: flex;
          flex-direction: row;
          align-items: center;

          .flagImg {
            height: 24px;
            width: 24px;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.2);
            position: relative;
          }

          p {
            margin-left: 10px;
            font-size: 14px;
            color: #5e616e;
          }
        }

        .review-user-meta span + span::before {
          content: "•";
          font-size: 17px;
          font-weight: 900;
          margin: 0 5px;
        }
      }

      .reviewLink {
        font-size: 16px;
        font-weight: 800;
        color: $primary-color;
        cursor: pointer;

        @media (max-width: $breakpoint-sm) {
          font-size: 15px;
        }
        @media (max-width: $breakpoint-xs) {
          font-size: 14px;
        }
      }
    }
  }

  .review-filter-modal-overlay {
    position: fixed;
    z-index: z-index(modal);
    inset: 0;
    background-color: rgba(0, 0, 0, 0.2);
    pointer-events: none;
  }
}

.slide-from-right-modal {
  .modal-content {
    .content {
      .rating__container__modal {
        margin: 30px 0 0 0;
        padding: 0 30px;

        @media (max-width: $breakpoint-md) {
          padding: 0 20px;
        }

        @media (max-width: $breakpoint-sm) {
          padding: 0 10px;
        }

        .rating__details {
          display: flex;
          flex-direction: row;
          justify-content: start;
          margin-bottom: 20px;

          .count {
            padding: 10px;
            margin: 0 10px 0 0;
            font-size: 40px;
            color: #17181c;
            font-weight: 700;
            border-radius: 10px;

            @media (max-width: $breakpoint-md) {
              font-size: 35px;
            }
          }

          .details {
            .detail1 {
              font-size: 16px;
              font-weight: 600;
              color: #17181c;
            }

            .detail2 {
              font-size: 14px;
              font-weight: 500;
              color: #5e616e;
            }
          }
        }
      }

      .rating-bar-container__modal {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        flex-wrap: wrap;
        column-gap: 15px;
        row-gap: 30px;
        margin-bottom: 20px;

        .rating-bar {
          display: flex;
          flex-direction: column;
          width: 47%;

          .rating-bar__progress {
            width: 100%;
            height: 6px;
            background-color: #d6d7db;
            margin-bottom: 5px;
            border-radius: 5px;
          }

          .rating-bar__fill {
            height: 100%;
            border-radius: 5px;
            background-color: #3a3c45;
          }

          .rating-bar__label-score {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            span {
              font-size: 12px;
              font-weight: 600;
              color: #3a3c45;
            }
          }
        }
      }

      .reviews__modal {
        width: 100%;

        .review-filter__modal {
          &__header {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
          }

          &__title {
            font-size: 20px;
            color: #17181c;
            font-weight: 800;

            @media (max-width: $breakpoint-md) {
              font-size: 18px;
            }

            @media (max-width: $breakpoint-sm) {
              font-size: 16px;
            }

            @media (max-width: $breakpoint-xs) {
              font-size: 15px;
            }
          }

          &__review-filter-button {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0 15px;
            border: 1px solid rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: border 0.2s ease;
            //z-index: 1002;

            &:not(.active):hover {
              box-shadow: 0 0 3px rgba(0, 0, 0, 0.3); // Adds a subtle glow effect
              border: 1px solid rgba(0, 0, 0, 0.5); // Sligh
            }

            &.active {
              outline: 2px solid#0770E4;
            }

            @media (max-width: $breakpoint-md) {
              padding: 3px 6px;
            }

            .input-field {
              outline: none;
              width: 100%;
              background-color: transparent;
              padding: 12px 0;
              color: #17181c;
              font-size: 15px;
              font-weight: 500;
              border: none;

              &::placeholder {
                color: #9ca3af;
              }

              @media (max-width: $breakpoint-md) {
                display: none;
              }
            }

            .arrowDown {
              color: #555;
            }
          }

          &__review-drop-down {
            position: absolute;
            left: 0;
            right: 0;
            top: 50px;
            width: 100%;
            border-radius: 20px;
            background-color: white;
            padding: 10px 0;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);

            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px); // Move up initially
            transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;

            @media (max-width: $breakpoint-md) {
              left: -150px;
              width: 185px;
            
            }

            &.active {
              opacity: 1;
              visibility: visible;
              transform: translateY(0); // Slide down smoothly
              z-index: z-index(dropdown);
            }

            .drop-down-item {
              font-size: 15px;
              font-weight: 500;
              padding: 10px 20px;
              display: flex;
              flex-direction: row;
              align-items: center;
              gap: 10px;
              color: #17181c;

                @media (max-width: $breakpoint-md) {
                  font-size: 14px;
                  padding: 8px 12px
                }

              .icon {
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }

          &__review-drop-down-overlay {
            position: fixed;
            z-index: z-index(overlay);
            inset: 0;
            background-color: transparent;
          }

          &__categories {
            width: 100%;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            margin-bottom: 20px;
            overflow-y: auto;
            column-gap: 10px;

            scrollbar-width: none;
            -ms-overflow-style: none;

            &::-webkit-scrollbar {
              display: none;
            }
          }

          &__category {
            padding: 2px 4px;
            font-size: 14px;
            font-weight: 600;
            color: #17181c;
            border: 1px solid rgba(0, 0, 0, 0.4);
            border-radius: 9999px;
            white-space: nowrap;
            transition: background-color 0.2s ease;

            span {
              padding: 0 5px;
              user-select: none;
            }

            &:hover {
              background-color: color.adjust(white, $lightness: -10%);
            }

            &.active {
              //color: #0770e4;
              color: $secondary_color;
              border: 1px solid $primary-color;
              background-color: #f0fafa;
              cursor: pointer;
            }
          }
        }

        .review-container__modal {
          .review__modal {
            padding: 0 0 20px 0;
            margin: 0 0 20px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.2);
            cursor: pointer;

            &:last-child {
              border-bottom: none;
            }

            .review-title {
              font-size: 20px;
              font-weight: 600;
              color: #17181c;
              display: flex;
              flex-direction: row;
              align-items: center;
              gap: 10px;
              margin-bottom: 5px;

              @media (max-width: $breakpoint-md) {
                font-size: 18px;
              }

              @media (max-width: $breakpoint-sm) {
                font-size: 16px;
              }

              @media (max-width: $breakpoint-xs) {
                font-size: 15px;
              }

              .review-rating {
                padding: 2px 5px;
                color: #17181c;
                font-size: 16px;
                font-weight: 500;
                background-color: #efeff0;
                border-radius: 5px;
              }
            }

            .review-text {
              font-size: 16px;
              line-height: 1.4;
              color: #17181c;
              margin: 0 0 10px 0;
              font-weight: 500;

              @media (max-width: $breakpoint-sm) {
                font-size: 14px;
              }
            }

            .review-user-meta {
              display: flex;
              flex-direction: row;
              align-items: center;

              .flagImg {
                height: 24px;
                width: 24px;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.2);
                position: relative;
              }

              p {
                margin-left: 10px;
                font-size: 14px;
                color: #5e616e;
              }
            }

            .review-user-meta span + span::before {
              content: "•";
              font-size: 17px;
              font-weight: 900;
              margin: 0 5px;
            }
          }
        }
      }
    }
  }
}

.detail-page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: z-index(overlay);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }
}
