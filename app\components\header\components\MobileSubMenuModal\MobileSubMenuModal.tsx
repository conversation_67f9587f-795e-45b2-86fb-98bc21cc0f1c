"use client";
import React from "react";
import { useTranslation } from "@/app/hooks/useTranslation";
import './MobileSubMenuModal.scss'

interface MobileMenuModalProps {
  isOpen: boolean;
  handleClose: () => void;
  children: React.ReactNode;
  title?: string;
  component_name?: string;
  isRTL?: boolean;
  onBack?: () => void; // New prop for back button functionality
}

const MobileSubMenuModal: React.FC<MobileMenuModalProps> = ({ isOpen, handleClose, children, title, component_name, isRTL, onBack }) => {
  const { t } = useTranslation("common");

  // Log for debugging
  console.log(`MobileSubMenuModal: title=${title}, hasBackButton=${!!onBack}`);
  return (
    <div onClick={handleClose} className={`slide-from-right-sub-modal ${isOpen ? "show" : ""} ${isRTL ? "rtl" : ""}`}>
      <div className={`modal-content ${component_name === "CancellationPolicy" ? 'cancellation-policy-modal' : ''}`} onClick={(e) => e.stopPropagation()}>
        <div className="header">
          <div className="header-content">
            {/* Always show back button when onBack is provided */}
            {onBack ? (
              <button
                className="back-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  onBack();
                }}
                aria-label="Back to menu"
              >
                <i className={`fa-solid ${isRTL ? 'fa-arrow-right' : 'fa-arrow-left'}`}></i>
                <span className="back-text">{t("common.back", "Back")}</span>
              </button>
            ) : (
              <button
                className="back-btn close-only"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClose();
                }}
                aria-label="Close"
              >
                <i className="fa-solid fa-xmark"></i>
                <span className="back-text">{t("common.close", "Close")}</span>
              </button>
            )}
            {title && <h2>{title}</h2>}
          </div>
        </div>

        <div className="content">{children}</div>
      </div>
    </div>
  );
};

export default MobileSubMenuModal;
