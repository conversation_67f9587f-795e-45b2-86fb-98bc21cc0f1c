import { useEffect, useCallback, useRef } from 'react';

interface ModalDimensions {
  maxHeight: number;
  topPosition: number;
  availableSpace: number;
}

interface DynamicModalHeightOptions {
  triggerElementRef: React.RefObject<HTMLDivElement | null>;
  modalType: 'location' | 'date' | 'travelers' | null;
  isOpen: boolean;
  isMobile?: boolean;
  minHeight?: number;
  safetyPadding?: number;
  desiredHeight?: number;
}

export const useDynamicModalHeight = ({
  triggerElementRef,
  modalType,
  isOpen,
  isMobile = false,
  minHeight = 300,
  safetyPadding = 40,
  desiredHeight = 500
}: DynamicModalHeightOptions) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate optimal modal dimensions
  const calculateModalDimensions = useCallback((): ModalDimensions => {
    if (!triggerElementRef.current) {
      return {
        maxHeight: minHeight,
        topPosition: 0,
        availableSpace: minHeight
      };
    }

    const triggerRect = triggerElementRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Calculate available space below trigger element
    const spaceBelow = viewportHeight - triggerRect.bottom;
    const spaceAbove = triggerRect.top;

    let maxHeight: number;
    let topPosition: number;
    let availableSpace: number;

    if (isMobile) {
      // Mobile: Use calc approach with dynamic positioning
      if (modalType === 'location') {
        // Location modal takes full height on mobile
        maxHeight = viewportHeight - safetyPadding;
        topPosition = safetyPadding / 2;
        availableSpace = maxHeight;
      } else {
        // Date and travelers modals use partial height
        const modalHeight = Math.min(
          desiredHeight,
          viewportHeight * 0.8 // 80% of viewport height
        );
        maxHeight = modalHeight - safetyPadding;
        topPosition = viewportHeight - modalHeight;
        availableSpace = maxHeight;
      }
    } else {
      // Desktop: Dynamic height based on available space
      const preferredSpace = Math.max(spaceBelow, spaceAbove);
      const useSpaceBelow = spaceBelow >= spaceAbove;

      if (useSpaceBelow) {
        // Position below trigger
        availableSpace = spaceBelow - safetyPadding;
        maxHeight = Math.max(
          minHeight,
          Math.min(desiredHeight, availableSpace)
        );
        topPosition = triggerRect.bottom;
      } else {
        // Position above trigger
        availableSpace = spaceAbove - safetyPadding;
        maxHeight = Math.max(
          minHeight,
          Math.min(desiredHeight, availableSpace)
        );
        topPosition = triggerRect.top - maxHeight;
      }

      // Ensure modal doesn't go off-screen
      if (topPosition < safetyPadding / 2) {
        topPosition = safetyPadding / 2;
        maxHeight = triggerRect.top - safetyPadding;
      }

      if (topPosition + maxHeight > viewportHeight - safetyPadding / 2) {
        maxHeight = viewportHeight - topPosition - safetyPadding / 2;
      }
    }

    return {
      maxHeight: Math.max(minHeight, maxHeight),
      topPosition,
      availableSpace
    };
  }, [triggerElementRef, modalType, isMobile, minHeight, safetyPadding, desiredHeight]);

  // Apply calculated dimensions to CSS custom properties
  const applyModalDimensions = useCallback(() => {
    if (!isOpen || !modalRef.current) return;

    const dimensions = calculateModalDimensions();
    const root = document.documentElement;

    // Set CSS custom properties
    root.style.setProperty('--modal-max-height', `${dimensions.maxHeight}px`);
    root.style.setProperty('--modal-top-position', `${dimensions.topPosition}px`);
    root.style.setProperty('--modal-available-space', `${dimensions.availableSpace}px`);
    root.style.setProperty('--modal-safety-padding', `${safetyPadding}px`);

    // Apply styles directly to modal element for immediate effect
    if (modalRef.current && triggerElementRef.current) {
      const triggerRect = triggerElementRef.current.getBoundingClientRect();

      modalRef.current.style.maxHeight = `${dimensions.maxHeight}px`;
      modalRef.current.style.position = 'fixed';

      if (isMobile) {
        // Mobile positioning - bottom sheet style
        modalRef.current.style.bottom = '0';
        modalRef.current.style.left = '0';
        modalRef.current.style.right = '0';
        modalRef.current.style.top = 'auto';
        modalRef.current.style.width = '100%';
        modalRef.current.style.height = modalType === 'location'
          ? `calc(100vh - ${safetyPadding}px)`
          : `${dimensions.maxHeight}px`;
      } else {
        // Desktop positioning - dropdown below trigger element
        modalRef.current.style.top = `${triggerRect.bottom + 5}px`;
        modalRef.current.style.left = `${triggerRect.left}px`;
        modalRef.current.style.bottom = 'auto';
        modalRef.current.style.right = 'auto';

        // Set width based on modal type
        if (modalType === 'location') {
          modalRef.current.style.width = `${Math.max(350, triggerRect.width)}px`;
        } else if (modalType === 'date') {
          modalRef.current.style.width = '650px';
        } else {
          modalRef.current.style.width = '350px';
        }
      }
    }
  }, [isOpen, calculateModalDimensions, safetyPadding, isMobile, modalType]);

  // Debounced resize handler
  const handleResize = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }
    
    resizeTimeoutRef.current = setTimeout(() => {
      applyModalDimensions();
    }, 100);
  }, [applyModalDimensions]);

  // Debounced scroll handler
  const handleScroll = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    scrollTimeoutRef.current = setTimeout(() => {
      applyModalDimensions();
    }, 50);
  }, [applyModalDimensions]);

  // Initial calculation with delay for DOM readiness
  useEffect(() => {
    if (isOpen) {
      const timeoutId = setTimeout(() => {
        applyModalDimensions();
      }, 50);

      return () => clearTimeout(timeoutId);
    }
  }, [isOpen, applyModalDimensions]);

  // Event listeners setup and cleanup
  useEffect(() => {
    if (isOpen) {
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, { passive: true });

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
        
        // Clear timeouts
        if (resizeTimeoutRef.current) {
          clearTimeout(resizeTimeoutRef.current);
        }
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [isOpen, handleResize, handleScroll]);

  // Cleanup CSS custom properties when modal closes
  useEffect(() => {
    if (!isOpen) {
      const root = document.documentElement;
      root.style.removeProperty('--modal-max-height');
      root.style.removeProperty('--modal-top-position');
      root.style.removeProperty('--modal-available-space');
      root.style.removeProperty('--modal-safety-padding');

      // Also reset modal element styles
      if (modalRef.current) {
        modalRef.current.style.position = '';
        modalRef.current.style.top = '';
        modalRef.current.style.left = '';
        modalRef.current.style.bottom = '';
        modalRef.current.style.right = '';
        modalRef.current.style.width = '';
        modalRef.current.style.height = '';
        modalRef.current.style.maxHeight = '';
      }
    }
  }, [isOpen]);

  return {
    modalRef,
    applyModalDimensions,
    calculateModalDimensions
  };
};
