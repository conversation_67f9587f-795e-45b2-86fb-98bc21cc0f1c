@use "/styles/variable" as *;

.nearby-attractions-map {
  padding: 20px 0;
  
  .attractions-list {
    margin-top: 20px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
      color: $primary_color;
    }
    
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 15px;
      
      @media (max-width: $breakpoint-sm) {
        grid-template-columns: 1fr;
      }
    }
    
    .attraction-item {
      padding: 12px;
      border-radius: 6px;
      background-color: white;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
      
      .attraction-name {
        font-weight: 600;
        font-size: 15px;
        margin-bottom: 5px;
      }
      
      .attraction-type {
        font-size: 13px;
        color: #666;
        margin-bottom: 5px;
      }
      
      .attraction-distance {
        font-size: 13px;
        color: $primary_color;
        font-weight: 500;
      }
    }
  }
}
