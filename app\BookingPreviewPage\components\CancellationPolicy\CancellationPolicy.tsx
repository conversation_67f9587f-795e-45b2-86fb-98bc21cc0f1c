"use client";
import React, {  useState } from "react";
import "./CancellationPolicy.scss";
import SlideFromRightModal from "../../../HotelDetail/components/SlideFromRightModal/SlideFromRightModal";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { CancellationBenefits } from "../../hotel-booking-details.model";

interface cancellationPolicyProps {
  cancellationBenefits: CancellationBenefits | undefined;
}

function CancellationPolicy({ cancellationBenefits }: cancellationPolicyProps) {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const formatPolicyCode = (code: string) => {
    return code
      .toLowerCase() // Convert to lowercase
      .replace(/_/g, " ") // Replace underscores with spaces
      .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word
  };

  const formatDateTime = (dateTimeString: string | undefined) => {
    if (!dateTimeString) {
      return { date: "", time: "" };
    }

    const dateObj = new Date(dateTimeString);

    // Format date as "01 Apr"
    const optionsDate: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "short",
    };
    const date = dateObj.toLocaleDateString("en-GB", optionsDate);

    // Format time as "11:59 PM"
    const optionsTime: Intl.DateTimeFormatOptions = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    const time = dateObj.toLocaleTimeString("en-US", optionsTime);

    return { date, time };
  };

  useScrollLock(isModalOpen);

  return (
    <div className="cancellation-policy">
      <div className="cancellation-policy__header">Cancellation Policy</div>

      <p className="cancellation-policy__description">
        100% amount will be deducted on cancellation.
      </p>



      <div className="cancellation-policy__timeline">
        {Array.isArray(cancellationBenefits?.policy) &&
        cancellationBenefits?.policy?.length > 0 ? (
          cancellationBenefits?.policy?.map((policy, index) => {
            const { date: startDate, time: startTime } = formatDateTime(
              policy?.startDate
            );

            return (
              <div
                key={index}
                className={`timeline-content  ${
                  cancellationBenefits?.code === "NON_REFUNDABLE"
                    ? "non-refundable"
                    : ""
                }`}
              >
                <p className="timeline-title">
                  {formatPolicyCode(policy.code)}
                </p>

                <div className="timeline-divider">
                  <div className={`line ${policy.code}`}></div>
                  <div
                    className={`timeline-label-dot ${
                      index !== 0 && "second-style-dot"
                    }`}
                  >
                    {index === 0 ? (
                      <>
                        <div className="dot left">
                          <span className="icon">
                            <i className="fa-solid fa-check"></i>
                          </span>
                        </div>
                      </>
                    ) : (
                      <div className="dot2 left">
                        <span className="icon"></span>
                      </div>
                    )}
                    {cancellationBenefits.policy.length > 1 && index === 0 ? (
                      <div className="label">Now</div>
                    ) : (
                      <div className="label-time">
                        <span className="label">{startDate || "01 Apr"}</span>
                        <span className="time">{startTime || "11:59 PM"}</span>
                      </div>
                    )}
                  </div>
                  {index === cancellationBenefits.policy.length - 1 && (
                    <div className="timeline-label-dot right">
                      <div className="dot">
                        <span className="icon">
                          <i className="fa-solid fa-key"></i>
                        </span>
                      </div>
                      <div className="label-time">
                        <span className="label">Check-In</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })
        ) : (
          <></>
        )}
      </div>

      <div className="cancellation-policy__link">
        <div
          className="view-cancellation-link"
          onClick={() => setIsModalOpen(true)}
        >
          View Cancellation Policy
        </div>
      </div>

      <SlideFromRightModal
        isOpen={isModalOpen}
        handleClose={handleCloseModal}
        title="Cancellation Policy"
        component_name="CancellationPolicy"
      >
        <div className="cancellation-policy-modal-content">
   

          <div className="cancellation-details-timeline-refundable">
            {Array.isArray(cancellationBenefits?.policy) &&
            cancellationBenefits?.policy?.length > 0 ? (
              cancellationBenefits.policy?.map((policy, index) => {
                const { date: startDate, time: startTime } = formatDateTime(
                  policy?.startDate
                );

                return (
                  <div
                    className="timeline-label-cancellation-details"
                    key={index}
                  >
                    <div className="timeline-container">
                      <div className="timeline-divider">
                        <div className="dot left">
                          <span className="icon"></span>
                        </div>
                        <div className={`line ${policy?.code}`}></div>
                        {index === cancellationBenefits.policy.length - 1 && (
                          <div className="dot right">
                            <span className="icon"></span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="timeline-label-cancellation-details">
                      {cancellationBenefits.policy.length > 1 && index === 0 ? (
                        <span className="timline-label">Now</span>
                      ) : (
                        <p className="timline-label">
                          {startDate || "01 Apr"} • {startTime || "11:59 PM"}
                        </p>
                      )}

                      <div className={`cancellation-details ${policy?.code}`}>
                        <p className="title">
                          {policy?.title ||
                            "Fully refundable before 01 Apr 2025 11:59 PM"}
                        </p>
                        <p className="description">
                          {policy?.subTitle ||
                            "Cancel your reservation before 01 Apr 2025 11:59 PM, to get a full refund"}
                        </p>
                      </div>
                      {index === cancellationBenefits.policy.length - 1 && (
                        <p className="timline-label">Check-In</p>
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <></>
            )}
          </div>

          <div className="note-container">
            <h6 className="note-container__title">Note:</h6>

            <p className="note-container__description">
              Date and time is displayed as per the local time zone of your
              hotel’s location.
            </p>
          </div>
        </div>
      </SlideFromRightModal>
    </div>
  );
}

export default CancellationPolicy;
