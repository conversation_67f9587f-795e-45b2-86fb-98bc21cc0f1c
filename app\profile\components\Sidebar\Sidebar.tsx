"use client";
import "./Sidebar.scss";
import Link from "next/link";

type Props = {
  activeTab: string;
  onTabChange: (tab: string) => void;
};

function Sidebar({ activeTab, onTabChange }: Props) {
  const tabs = [
    {
      label: "My bookings",
      icon: "fa-calendar-check",
    },
    {
      label: "Wishlist",
      icon: "fa-heart",
    },
    {
      label: "Reviews",
      icon: "fa-star",
    },

    {
      label: "Profile",
      icon: "fa-user",
    },
  ];

  return (
    <div className="sidebar-container">
      <div className="sidebar-container__sidebar">
        <ul>
          {tabs?.map((tab, index) => (
            <li
              onClick={() => onTabChange(tab?.label)}
              className={`${activeTab == tab?.label ? "active" : ""}`}
              key={index}
            >
              <Link href={""}>
                <i className={`fa-solid ${tab?.icon}`}></i>
                {tab?.label}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export default Sidebar;
