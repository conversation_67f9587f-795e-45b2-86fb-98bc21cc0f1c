@use "/styles/variable" as *;

.feature-highlights {
  width: 100%;
  margin: 50px 0;
  padding: 20px;
  //background-color:  rgb(205.6, 227.8, 224.6);
  //   background-color: rgb(175, 197, 194);
  // background-color: rgb(145, 170, 168);
  //background-color: rgb(140, 111, 88);
  //background: linear-gradient(135deg, rgb(140, 111, 88), rgb(200, 170, 140));
    // background: linear-gradient(135deg, $primary-color, $primary-light, $secondary-color);
    background: $primary-color;


  border-radius: 6px;
  &__header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: 5px 0 30px 0;



    

    .fa-paper-plane {
      color: #fff;
      font-size: 21px;
      margin-right: 10px;
    }

    h2 {
      display: inline-block;
      font-size: 26px;
      color: #fff;
      font-weight: 300;

      @media (max-width: $breakpoint-md) {
        font-size: 24px;
      }

      @media (max-width: $breakpoint-sm) {
        font-size: 21px;
      }

      span {
        font-weight: 700;
      }
    }
  }
  &__cards-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    column-gap: 80px;
    row-gap: 65px;
    margin-bottom: 20px;

    .card {
      width: 25%;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;

      
      @media (max-width: $breakpoint-sm) {
        width: 100%;
      }

      //margin-right: 80px;
      //margin-bottom: 65px;
    

      .card-icon {
        margin-bottom: 15px;
        .fa-solid {
          font-size: 34px;

          @media (max-width: $breakpoint-md) {
            font-size: 26px;
          }
        }
      }

      .card-content {
        .description,
        .price-details {
          text-align: center;
          
        }

        .description {
          font-weight: 600;
          font-size: 17px;
          font-weight: 500;

          @media (max-width: $breakpoint-md) {
            font-size: 12px;
          }
        }

        .price-details {
          font-weight: 400;
          font-size: 13px;
          margin: 10px 0px 0px 0px;
          font-weight: 300;

          @media (max-width: $breakpoint-md) {
            font-size: 11px;
          }
        }
      }
    }
  }
}
