@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

// Custom popup styles
.custom-popup-container {
  .leaflet-popup-content-wrapper {
    padding: 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 3px 14px rgba(0, 0, 0, 0.2);
  }

  .leaflet-popup-content {
    margin: 0;
    height: 100%;
    // width: 180px !important;
  }

  .custom-popup {
    font-family: 'Open Sans', sans-serif;

    .popup-content {
      padding: 10px;

      h3 {
        margin: 0 0 5px;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .popup-rating {
        color: $primary_color;
        font-size: 12px;
        margin-bottom: 4px;
      }

      .popup-price {
        font-weight: 600;
        color: $primary_color;
        font-size: 13px;
        margin-bottom: 8px;
      }

      .popup-view-btn {
        background-color: $primary_color;
        color: white;
        text-align: center;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
        margin-top: 8px;

        &:hover {
          background-color: darken($primary_color, 10%);
        }
      }
    }
  }
}

// Hotel marker tooltip styles
.hotel-marker-tooltip-container {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
  z-index: z-index(popover)-1 !important; /* Higher than the fullscreen map z-index */

  .leaflet-tooltip-content {
    width: 100%;
  }

  &::before {
    display: none !important; // Hide the tooltip arrow
  }

  .hotel-marker-tooltip {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 4px 8px;
    min-width: 80px;
    text-align: center;
    border: none;
    font-family: 'Open Sans', sans-serif;
    z-index: z-index(modal);

    // Price-focused design as shown in the image
    &__price {
      font-weight: 700;
      color: #333;
      font-size: 13px;
      display: block;
    }

    // Hide name by default, show on hover
    &__name {
      display: none;
      font-weight: 600;
      font-size: 12px;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
    }

    &__details {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &__rating {
      display: none;
      color: #FFB800;
      font-size: 11px;
    }

    // Hover state to show more details
    &:hover {
      z-index: z-index(popover)+1 !important;
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.25);
      padding: 6px 10px;

      .hotel-marker-tooltip__name {
        display: block;
        margin-bottom: 4px;
      }

      .hotel-marker-tooltip__rating {
        display: block;
        margin-right: 6px;
      }
    }
  }
}

// RTL support for popups and tooltips
html[dir="rtl"] {
  .custom-popup-container {
    .popup-content {
      text-align: right;
    }
  }

  .hotel-marker-tooltip-container {
    .hotel-marker-tooltip {
      text-align: right;

      &__details {
        justify-content: flex-start;
        flex-direction: row-reverse;
      }
    }
  }
}
