"use client";
import React from "react";
import './FareSummary.scss'
import { FareDetail, TravelDetails } from "../../hotel-booking-details.model";

type FareSummaryProps = {
  fareDetail?: FareDetail;
  travelDetails?: TravelDetails;
}

function FareSummary({ fareDetail, travelDetails } : FareSummaryProps) {
  return (
    <div className="fare-summary-container">
      <h6 className="fare-summary-container__header">Fare Summary</h6>
      <div className="fare-summary-container__label-price">
        <div className="label">{travelDetails?.roomCount || ""} Rooms, {travelDetails?.nightCount || ""} Night</div>
        <div className="price">
          ₹{Number(fareDetail?.displayedBaseFare).toFixed(2).split('.')[0]}
          <span className="sub">.
          {Number(fareDetail?.displayedBaseFare).toFixed(2).split('.')[1]}</span>
        </div>
      </div>
      <div className="fare-summary-container__label-price">
        <div className="label">
          Taxes & Charges{" "}
          <span>
            <i className="fa-solid fa-circle-exclamation"></i>
          </span>
        </div>

        <div className="price">
          ₹{Number(fareDetail?.taxesAndFees).toFixed(2).split('.')[0]}
          <span className="sub">.
           {Number(fareDetail?.taxesAndFees).toFixed(2).split('.')[1]}</span>
        </div>
      </div>
      <hr style={{marginBottom: '10px'}}/>
      <div className="fare-summary-container__label-price ">
        <div className="label discounts">
        Discounts{" "}
          <span>
            <i className="fa-solid fa-circle-exclamation"></i>
          </span>
        </div>

        <div className="price">
          ₹{Number(fareDetail?.totalDiscount).toFixed(2).split('.')[0]}
        <span className="sub discounts">.{Number(fareDetail?.taxesAndFees).toFixed(2).split('.')[1]}</span>
        </div>
      </div>

      <hr style={{marginBottom: '10px'}}/>

      <div className="fare-summary-container__label-price ">
        <div className="label netAmt">
        Net Amount Payable
        </div>

        <div className="price netAmt">
          ₹{Number(fareDetail?.totalPGAmount).toFixed(2).split('.')[0]}
          <span className="sub">. ₹{Number(fareDetail?.totalPGAmount).toFixed(2).split('.')[1]}</span>
        </div>
      </div>
    </div>
  );
}

export default FareSummary;
