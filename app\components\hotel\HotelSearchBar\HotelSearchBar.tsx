"use client";
import React, { useState, useRef, useEffect, useCallback } from "react";
import styles from "./HotelSearchBar.module.scss";
import HotelDateSelection from "./components/HotelDateField/HotelDateSelection";
import HotelTravelerSelector from "./components/HotelTravelerSelector/HotelTravelerSelector";
import LocationSearch from "./components/LocationInput/LocationSearch";
import { useRouter } from "next/navigation";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useTranslation } from "@/app/hooks/useTranslation";
import { Hotel } from "../../hotel-search-result.model";
import { popularPlaces } from './components/LocationInput/LocationSearch';
import { HotelSearchData } from "@/models/hotel/search-page.model";
import { convertFormDataToSearchData, saveHotelSearchDataToStorage, extractCityName } from "@/app/utils/hotelSearchUtils";
import { useDynamicModalHeight } from "./hooks/useDynamicModalHeight";
import BottomToTopPopup from "@/components/popups/BottomToTopPopup";
import BottomUpPopup from "@/components/popups/BottomUpPopup";

import {
  MapPin,
  RotateCcw,
  LocateFixed,
  Calendar,
  Users,
  X,
  Share2,
  Search,
} from "lucide-react";
import { getAutoSuggest, getLocationDetails } from "@/api/hotel/search-page-service";
import { AutoSuggestionRes, LocationSuggestion } from "@/models/hotel/search-page.model";

// Define types for room data to match HotelTravelerSelector
interface ChildAge {
  id: number;
  age: number;
}

interface Room {
  id: number;
  adults: number;
  children: number;
  childrenAges: ChildAge[];
}

// Define the types for our form state
export interface HotelSearchFormData {
  searchQuery: string; // Display name (e.g., "Dubai")
  fullLocationData?: string; // Full location data (e.g., "Dubai, United Arab Emirates")
  locationId?: string; // Location ID from auto-suggest API
  geoCode?: { lat: string; long: string }; // Coordinates from auto-suggest API
  checkInDate: string | null; // Store as ISO string for localStorage
  checkOutDate: string | null; // Store as ISO string for localStorage
  travelers: {
    adults: number;
    rooms: number;
    children: number;
  };
  // Add rooms array to store detailed room information
  roomsData: Room[];
}

interface HotelSearchBarProps {
  isModify?: boolean;
  shareButtonFlag?: boolean;
  selectedHotels?: Hotel[];
}

const HotelSearchBar: React.FC<HotelSearchBarProps> = ({
  isModify = false,
  shareButtonFlag,
  selectedHotels,
}) => {
  // Router and context
  const { t } = useTranslation();
  const navigation = useRouter();
  const { setIsLoading, setHotelSearchFormData, setHotelSearchData } = useCommonContext();

  // State for validation errors
  const [validationError, setValidationError] = useState<string | null>(null);

  // State to track if location is being fetched
  const [isLocationLoading, setIsLocationLoading] = useState(false);

  // State for window width tracking (for mobile modal) - Initialize with safe defaults
  const [windowWidth, setWindowWidth] = useState<number | undefined>(undefined);

  // State for mobile view detection - Initialize as undefined to prevent hydration mismatch
  const [isMobileView, setIsMobileView] = useState<boolean | undefined>(undefined);

  // State to track if component is mounted (prevents hydration issues)
  const [isMounted, setIsMounted] = useState(false);

  // Remove separate mobile modal states - we'll use the dropdown states for both mobile and desktop

  // State for location search query (separate from formData.searchQuery)
  const [locationSearchQuery, setLocationSearchQuery] = useState('');

  // Ref to track last search term to prevent duplicate API calls
  const lastSearchTermRef = useRef<string>("");

  const { isShareGroupVisible, setIsShareGroupVisible, setIsMobileModalOpen: setGlobalMobileModalOpen } = useCommonContext();

  // Default form values
  const getDefaultFormData = (): HotelSearchFormData => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    return {
      searchQuery: "",
      checkInDate: new Date().toISOString(),
      checkOutDate: tomorrow.toISOString(),
      travelers: {
        adults: 2,
        rooms: 1,
        children: 0,
      },
      // Initialize with a single room with 2 adults and no children
      roomsData: [
        {
          id: 1,
          adults: 2,
          children: 0,
          childrenAges: [],
        },
      ],
    };
  };

  // Initialize state with defaults first, then load from localStorage in useEffect
  const [formData, setFormData] = useState<HotelSearchFormData>(getDefaultFormData);
  const [locationList,setLocationList] = useState<LocationSuggestion[]>([]);
  const [localFilteredResults, setLocalFilteredResults] = useState<LocationSuggestion[]>([]);
  const [showLocalResults, setShowLocalResults] = useState(true);


  // Load data from localStorage after component mounts to prevent hydration mismatch
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedData = localStorage.getItem("hotelSearchFormData");
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          // Ensure roomsData exists in the parsed data
          if (!parsedData.roomsData) {
            // Create roomsData from travelers info if not present
            parsedData.roomsData = [
              {
                id: 1,
                adults: parsedData.travelers.adults || 2,
                children: parsedData.travelers.children || 0,
                childrenAges: Array.from(
                  { length: parsedData.travelers.children || 0 },
                  (_, i) => ({
                    id: i + 1,
                    age: 0,
                  })
                ),
              },
            ];
          }

          // Handle backward compatibility and extract city name for display
          if (parsedData.searchQuery && !parsedData.fullLocationData) {
            // If we have searchQuery but no fullLocationData, it might be old data
            // Check if searchQuery contains comma (full location format)
            if (parsedData.searchQuery.includes(',')) {
              parsedData.fullLocationData = parsedData.searchQuery; // Store full data
              const parts = parsedData.searchQuery.split(',');
              parsedData.searchQuery = parts[0].trim(); // Display only city name
            }
          }

          setFormData(parsedData);
        } catch (e) {
          console.error("Error parsing saved form data:", e);
        }
      }
    }
  }, []);

  // Dropdown state
  const [isLocationDropdownOpen, setIsLocationDropdownOpen] = useState(false);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [isTravelersDropdownOpen, setIsTravelersDropdownOpen] = useState(false);

  // Refs for dropdown positioning
  const locationFieldRef = useRef<HTMLDivElement>(null);
  const dateFieldRef = useRef<HTMLDivElement>(null);
  const travelersFieldRef = useRef<HTMLDivElement>(null);
  const locationDropdownRef = useRef<HTMLDivElement>(null);
  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const travelersDropdownRef = useRef<HTMLDivElement>(null);

  // Dynamic modal height hooks for each modal type
  const locationModalHeight = useDynamicModalHeight({
    triggerElementRef: locationFieldRef,
    modalType: isLocationDropdownOpen ? 'location' : null,
    isOpen: isLocationDropdownOpen,
    isMobile: isMobileView || false,
    minHeight: 200,
    safetyPadding: isMobileView ? 20 : 40,
    desiredHeight: 450
  });

  const dateModalHeight = useDynamicModalHeight({
    triggerElementRef: dateFieldRef,
    modalType: isDateDropdownOpen ? 'date' : null,
    isOpen: isDateDropdownOpen,
    isMobile: isMobileView || false,
    minHeight: 400,
    safetyPadding: isMobileView ? 20 : 40,
    desiredHeight: 600
  });

  const travelersModalHeight = useDynamicModalHeight({
    triggerElementRef: travelersFieldRef,
    modalType: isTravelersDropdownOpen ? 'travelers' : null,
    isOpen: isTravelersDropdownOpen,
    isMobile: isMobileView || false,
    minHeight: 250,
    safetyPadding: isMobileView ? 20 : 40,
    desiredHeight: 400
  });

  // Remove mobile modal height management - using popup components instead

  // Format dates for display in "31 May' 25" format
  const formatDateForDisplay = (dateIsoString: string | null): string => {
    if (!dateIsoString) return "";
    const date = new Date(dateIsoString);

    // Get day, month, and year
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear().toString().slice(-2); // Get last 2 digits of year

    return `${day} ${month}' ${year}`;
  };

  // Function to check if hotels are selected before sharing
  const checkHotelsSelected = () => {
    if (!selectedHotels || selectedHotels.length === 0) {
      alert("Please select at least one journey!");
      return false;
    }
    return true;
  };

  // Function to prepare selected hotels data for sharing in the desired format
  const prepareFormattedMessage = () => {
    if (!selectedHotels || selectedHotels.length === 0) {
      return "No hotels selected";
    }

    // Format the location from search query
    const location = formData.searchQuery || "Your destination";

    // Format check-in and check-out dates using the new format
    const checkIn = formData.checkInDate
      ? formatDateForDisplay(formData.checkInDate)
      : "Not specified";

    const checkOut = formData.checkOutDate
      ? formatDateForDisplay(formData.checkOutDate)
      : "Not specified";

    // Calculate number of nights
    const nights =
      formData.checkInDate && formData.checkOutDate
        ? Math.round(
            (new Date(formData.checkOutDate).getTime() -
              new Date(formData.checkInDate).getTime()) /
              (1000 * 60 * 60 * 24)
          )
        : 0;

    // Format hotel details
    const hotelsList = selectedHotels
      .map((hotel, index) => {
        // Assuming price is in the format we need, otherwise we'd format it accordingly
        return `${index + 1}. ${hotel.name} - ${"DH"} ${
          hotel?.fareDetail?.displayedBaseFare || ""
        }`;
      })
      .join("\n\n");

    // Prepare the full message in the exact format requested
    const message = `Your Hotel information for ${location}

    Check In : ${checkIn}
    Check Out : ${checkOut}
    ${formData.travelers.adults} Pax, ${formData.travelers.rooms} Rooms, ${nights} Nights

    ${hotelsList}

    Thank you for choosing KindAli Travels
    Contact: 1234567890
    Email: <EMAIL>`;

    return message;
  };

  // Function to share via WhatsApp
  const shareViaWhatsApp = () => {
    // Check if hotels are selected before proceeding
    if (!checkHotelsSelected()) return;

    const message = prepareFormattedMessage();

    // Encode the message for WhatsApp URL
    const encodedMessage = encodeURIComponent(message);

    // Open WhatsApp web with the message
    window.open(`https://wa.me/?text=${encodedMessage}`, "_blank");
  };

  // Function to share via Email
  const shareViaEmail = () => {
    // Check if hotels are selected before proceeding
    if (!checkHotelsSelected()) return;

    const message = prepareFormattedMessage();

    const subject = encodeURIComponent("Your Hotel Booking Information");
    const body = encodeURIComponent(message);

    // Open default email client with the message
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  };

  // Function to convert popular places to LocationSuggestion format
  const convertPopularPlacesToLocationSuggestions = (): LocationSuggestion[] => {
    try {
      return popularPlaces.map((place, index) => ({
        id: `popular_${index}`,
        name: place?.name || '',
        fullName: `${place?.name || ''}, ${place?.city || ''}, ${place?.country || ''}`,
        type: 'popular_place',
        state: place?.city || '',
        country: place?.country || '',
        coordinates: {
          lat: 0, // Default coordinates since not available
          long: 0
        },
        referenceScore: place?.propertyCount || 0,
        code: place?.countryCode || '',
        city: place?.city || '',
        referenceId: `popular_${index}`
      }));
    } catch (error) {
      console.error('Error converting popular places to LocationSuggestion format:', error);
      return [];
    }
  };

  // Function to convert recent searches to LocationSuggestion format
  const convertRecentSearchesToLocationSuggestions = (): LocationSuggestion[] => {
    if (typeof window === 'undefined') return [];

    try {
      const existingSearches = localStorage.getItem('recentSearches');
      const recentSearches = existingSearches ? JSON.parse(existingSearches) : [];

      return recentSearches.slice(0, 5).map((search: any, index: number) => ({
        id: search?.id || `recent_${index}`,
        name: search?.destination || '',
        fullName: search?.destination || '',
        type: 'recent_search',
        state: '',
        country: '',
        coordinates: {
          lat: 0,
          long: 0
        },
        referenceScore: 0,
        code: '',
        city: '',
        referenceId: search?.id || `recent_${index}`
      }));
    } catch (error) {
      console.error('Error converting recent searches:', error);
      return [];
    }
  };

  // Function to add search to recent searches
  const addToRecentSearches = (searchData: HotelSearchFormData) => {
    if (typeof window === 'undefined') return;

    try {
      // Get existing recent searches
      const existingSearches = localStorage.getItem('recentSearches');
      let recentSearches = existingSearches ? JSON.parse(existingSearches) : [];

      // Create new search entry
      const newSearch = {
        id: Date.now().toString(),
        destination: searchData.fullLocationData || searchData.searchQuery, // Use full location data if available
        checkIn: formatDateForDisplay(searchData.checkInDate),
        checkOut: formatDateForDisplay(searchData.checkOutDate),
        guests: searchData.travelers.adults,
        rooms: searchData.travelers.rooms,
      };

      // Remove any existing search with the same destination to avoid duplicates
      recentSearches = recentSearches.filter(
        (search: { destination: string }) => search.destination !== newSearch.destination
      );

      // Add new search to the beginning
      recentSearches.unshift(newSearch);

      // Keep only the last 10 searches
      recentSearches = recentSearches.slice(0, 10);

      // Save back to localStorage
      localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
    } catch (error) {
      console.error('Error saving to recent searches:', error);
    }
  };



  // Handle search function
  const handleSearch = () => {
    // Validate location is provided
    if (!formData.searchQuery || formData.searchQuery.trim() === "") {
      setValidationError(t("search.validation.location_required"));
      // Open the location dropdown to make it clear what needs to be filled
      setIsLocationDropdownOpen(true);
      setIsDateDropdownOpen(false);
      setIsTravelersDropdownOpen(false);
      return;
    }

    // Clear any previous validation errors
    setValidationError(null);

    // Add to recent searches before navigating
    addToRecentSearches(formData);

    // Convert form data to API search data format
    const searchData = convertFormDataToSearchData(formData);

    // Save both formats to localStorage and context
    localStorage.setItem("hotelSearchFormData", JSON.stringify(formData));
    saveHotelSearchDataToStorage(searchData);

    // Update context with both data formats
    setHotelSearchFormData(formData);
    setHotelSearchData(searchData);

    console.log("Form Data:", formData);
    console.log("API Search Data:", searchData);

    // Navigate to search results
    navigation.push("/HotelSearchResult");
  };

  // Handle location selection
  const handleLocationSelect = (location: string) => {
    // Extract just the city name for display purposes
    // Examples: "Dubai, United Arab Emirates" -> "Dubai"
    //          "Taj Mahal, Agra, India" -> "Taj Mahal"
    const extractCityName = (fullLocation: string): string => {
      if (!fullLocation) return fullLocation;

      // Split by comma and take the first part (city/landmark name)
      const parts = fullLocation.split(',');
      return parts[0].trim();
    };

    const displayName = extractCityName(location);

    setFormData((prev) => ({
      ...prev,
      searchQuery: displayName, // Show only the city name in the input field
      fullLocationData: location, // Store the full location data for API calls
      // Clear location ID and geoCode when manually selecting location
      // These will be set by handleLocationObjectSelect when API data is available
      locationId: undefined,
      geoCode: undefined,
    }));

    // Clear any validation errors when a location is selected
    if (validationError) setValidationError(null);
    setIsLocationDropdownOpen(false);
  };

  // Handle location object selection (with ID and coordinates)
  const handleLocationObjectSelect = (locationObject: LocationSuggestion) => {
    const displayName = locationObject.name;
    const fullLocation = locationObject.fullName || locationObject.name;

    setFormData((prev) => ({
      ...prev,
      searchQuery: displayName,
      fullLocationData: fullLocation,
      locationId: locationObject.id,
      geoCode: {
        lat: locationObject.coordinates.lat.toString(),
        long: locationObject.coordinates.long.toString(),
      },
    }));

    console.log('Location object selected:', {
      displayName,
      fullLocation,
      locationId: locationObject.id,
      coordinates: locationObject.coordinates,
    });

    // Clear any validation errors when a location is selected
    if (validationError) setValidationError(null);
    setIsLocationDropdownOpen(false);
  };

  // Handle location input change
  const handleSearchQueryChange = (value: React.SetStateAction<string>) => {
    setFormData((prev) => ({
      ...prev,
      searchQuery:
        typeof value === "function" ? value(prev.searchQuery) : value,
    }));

    // Clear validation error when user starts typing
    if (validationError) setValidationError(null);
  };

    const handleAutoSuggestApi = useCallback(async(searchTerm:string, autoSelectFirst: boolean = false)=>{
    if(searchTerm && searchTerm.trim().length > 0){
      setIsLocationLoading(true)
      setShowLocalResults(false) // Hide local results when API call starts

      try{
        const api = await getAutoSuggest(searchTerm.trim())
        console.log('Raw API response:', api); // Debug the full response structure

        // Handle new API response structure with nested data
        if(api.data && api.data.status === 'success'){
          const locationSuggestions = api.data.locationSuggestions || [];
          if(locationSuggestions.length > 0){
            console.log('Auto-suggest API response:', {
              searchTerm: searchTerm,
              totalResults: locationSuggestions.length,
              results: locationSuggestions
            });
            setLocationList(locationSuggestions)
            setShowLocalResults(false) // Keep showing API results

            // Auto-select first result if requested (for current location)
            if(autoSelectFirst) {
              const firstSuggestion = locationSuggestions[0];

              // Use handleLocationObjectSelect to properly set the location with ID and coordinates
              handleLocationObjectSelect(firstSuggestion);

              // Call location details API for the auto-selected location
              if (firstSuggestion.id) {
                handleLocationDetailsApi(firstSuggestion.id, firstSuggestion.fullName || firstSuggestion.name);
              }

              console.log('Auto-selected first suggestion:', {
                searchTerm: searchTerm,
                selectedLocation: firstSuggestion.fullName || firstSuggestion.name,
                locationId: firstSuggestion.id,
                coordinates: firstSuggestion.coordinates,
                suggestion: firstSuggestion
              });

              return; // Exit early since we've selected the location
            }
          } else {
            setLocationList([]) // Clear list if no results
            setShowLocalResults(true) // Show local results if API returns empty
          }
        } else {
          // Handle old API response structure as fallback
          if(api.status === 'success' && api.locationSuggestions){
            const locationSuggestions = api.locationSuggestions || [];
            if(locationSuggestions.length > 0){
              console.log('Auto-suggest API response (old format):', {
                searchTerm: searchTerm,
                totalResults: locationSuggestions.length,
                results: locationSuggestions
              });
              setLocationList(locationSuggestions)
              setShowLocalResults(false)

              if(autoSelectFirst) {
                const firstSuggestion = locationSuggestions[0];
                handleLocationObjectSelect(firstSuggestion);
                return;
              }
            } else {
              setLocationList([])
              setShowLocalResults(true)
            }
          } else {
            console.log('API response error or no data:', api);
            setLocationList([])
            setShowLocalResults(true)
          }
        }
      }catch (error){
        console.log(error);
        setLocationList([]) // Clear list on error
        setShowLocalResults(true) // Show local results on error
      } finally {
        setIsLocationLoading(false)
      }
    } else {
      setLocationList([]) // Clear list when search term is empty
      setShowLocalResults(true) // Show local results when no search term
      setIsLocationLoading(false)
    }
  },[handleLocationSelect])

  // Special auto-suggest function for current location that auto-selects first result
  const handleCurrentLocationAutoSuggest = useCallback(async(searchTerm: string) => {
    return handleAutoSuggestApi(searchTerm, true); // Pass true to auto-select first result
  }, [handleAutoSuggestApi]);

  // Function to call location details API
  const handleLocationDetailsApi = useCallback(async (locationId: string, locationName: string) => {
    if (!locationId) {
      console.warn('No location ID provided for location details API');
      return;
    }

    try {
      console.log('Calling location details API:', {
        locationId: locationId,
        locationName: locationName,
        endpoint: `/location?location_id=${locationId}`
      });

      const locationDetails = await getLocationDetails(locationId);

      console.log('Location details API response:', {
        locationId: locationId,
        locationName: locationName,
        response: locationDetails
      });

      // You can store the location details in state or context if needed
      // For now, just logging the response

    } catch (error) {
      console.error('Error calling location details API:', {
        locationId: locationId,
        locationName: locationName,
        error: error
      });
    }
  }, []);

  // Local filtering function for immediate results
  const performLocalFiltering = useCallback((searchTerm: string) => {
    if (!searchTerm || searchTerm.trim().length === 0) {
      setLocalFilteredResults([]);
      return;
    }

    // Convert popular places to LocationSuggestion format for local filtering
    const popularPlacesAsLocationSuggestions = popularPlaces.map((place, index) => ({
      id: `popular-${index}`,
      name: place.name,
      fullName: `${place.name}, ${place.city}`,
      type: 'City',
      country: place.countryCode.toUpperCase(),
      coordinates: {
        lat: 0, // We don't have coordinates for popular places
        long: 0
      },
      referenceScore: place.propertyCount
    }));

    // Filter popular places based on search term
    const filtered = popularPlacesAsLocationSuggestions.filter(place =>
      place.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      place.fullName.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setLocalFilteredResults(filtered);
  }, []);

  const handleLocationSearchQueryChange = useCallback((value:string)=>{
    setLocationSearchQuery(value)

    // Immediately show local filtered results
    performLocalFiltering(value)
    setShowLocalResults(true)

    // The API call will be handled by the useEffect with debouncing
  },[performLocalFiltering])



  // Handle rooms change from HotelTravelerSelector
  const handleRoomsChange = (updatedRooms: Room[]) => {
    // Calculate total adults, children, and room count
    let totalAdults = 0;
    let totalChildren = 0;

    updatedRooms.forEach((room) => {
      totalAdults += room.adults;
      totalChildren += room.children;
    });

    setFormData((prev) => ({
      ...prev,
      travelers: {
        adults: totalAdults,
        children: totalChildren,
        rooms: updatedRooms.length,
      },
      roomsData: updatedRooms,
    }));
  };

  // Handle getting current location with auto-suggest API integration
  const getCurrentLocation = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (navigator.geolocation) {
      setIsLocationLoading(true);
      // Clear any validation errors
      if (validationError) setValidationError(null);
      setFormData((prev) => ({
        ...prev,
        searchQuery: t("search.fetching_location"),
      }));

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;

          try {
            // Reverse geocoding using a free service
            const response = await fetch(
              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`
            );
            const data = await response.json();

            // Extract city name from the response
            const city =
              data.address.city ||
              data.address.town ||
              data.address.village ||
              data.address.county ||
              data.address.state;

            const fullLocationName = city
              ? `${city}, ${data.address.country}`
              : data.display_name;

            // Call auto-suggest API with ONLY the city name and auto-select first result
            if (city) {
              try {
                setFormData((prev) => ({
                  ...prev,
                  searchQuery: t("search.searching_location"),
                }));

                // Use ONLY the city name for auto-suggest API call
                await handleCurrentLocationAutoSuggest(city.trim());

                console.log('Current location auto-suggest initiated with city:', {
                  cityName: city,
                  fullLocation: fullLocationName
                });
              } catch (apiError) {
                console.error("Error calling auto-suggest API for current location:", apiError);
                // Fallback to full location name if API call fails
                setFormData((prev) => ({
                  ...prev,
                  searchQuery: fullLocationName,
                }));
              }
            } else {
              // If no city found, use display name as fallback
              setFormData((prev) => ({
                ...prev,
                searchQuery: data.display_name,
              }));
            }

            setIsLocationLoading(false);
          } catch (error) {
            console.error("Error reverse geocoding:", error);
            setFormData((prev) => ({
              ...prev,
              searchQuery: t("search.current_location"),
            }));
            setIsLocationLoading(false);
          }
        },
        (error) => {
          console.error("Error getting location:", error);
          setFormData((prev) => ({
            ...prev,
            searchQuery: "",
          }));
          setIsLocationLoading(false);

          // Show error message based on error code
          let errorMessage = t("search.location_error.default");
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = t("search.location_error.permission_denied");
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = t("search.location_error.unavailable");
              break;
            case error.TIMEOUT:
              errorMessage = t("search.location_error.timeout");
              break;
          }

          alert(errorMessage);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        }
      );
    } else {
      alert(t("search.location_error.not_supported"));
    }
  };

  // Enhanced location initialization logic - run after formData is loaded
  useEffect(() => {
    const initializeLocation = async () => {
      // Only run if location is empty, we're on the client side, and component is mounted
      if (typeof window === 'undefined' || !isMounted || formData.searchQuery.trim() !== '') {
        return;
      }

      try {
        // Step 1: Check if localStorage has previous data (already handled in useState)
        const savedData = localStorage.getItem("hotelSearchFormData");
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData.searchQuery && parsedData.searchQuery.trim() !== '') {
            return; // Already has location data
          }
        }

        // Step 2: Try to get recent searches data and use the most recent one
        const recentSearchesData = localStorage.getItem("recentSearches");
        if (recentSearchesData) {
          const recentSearches = JSON.parse(recentSearchesData);
          if (recentSearches.length > 0) {
            const mostRecentSearch = recentSearches[0];
            setFormData((prev) => ({
              ...prev,
              searchQuery: mostRecentSearch.destination || mostRecentSearch.searchQuery,
            }));
            return;
          }
        }

        // Step 3: If no recent searches, try to auto-fetch user location
        if (navigator.geolocation) {
          setIsLocationLoading(true);
          setFormData((prev) => ({
            ...prev,
            searchQuery: t("search.fetching_location"),
          }));

          navigator.geolocation.getCurrentPosition(
            async (position) => {
              const { latitude, longitude } = position.coords;

              try {
                // Reverse geocoding using OpenStreetMap Nominatim API
                const response = await fetch(
                  `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`
                );
                const data = await response.json();

                // Extract city and country from the response
                const city =
                  data.address?.city ||
                  data.address?.town ||
                  data.address?.village ||
                  data.address?.county ||
                  data.address?.state;

                const locationName = city
                  ? `${city}, ${data.address?.country}`
                  : data.display_name;

                setFormData((prev) => ({
                  ...prev,
                  searchQuery: locationName,
                }));
                setIsLocationLoading(false);
              } catch (error) {
                console.error("Error reverse geocoding:", error);
                setFormData((prev) => ({
                  ...prev,
                  searchQuery: t("search.current_location"),
                }));
                setIsLocationLoading(false);
              }
            },
            (error) => {
              console.error("Error getting location:", error);
              setIsLocationLoading(false);

              // Step 4: If geolocation fails, use first popular place as fallback
              if (popularPlaces.length > 0) {
                const firstPopularPlace = popularPlaces[0];
                const popularPlaceLocation = `${firstPopularPlace.name}, ${firstPopularPlace.city}`;
                setFormData((prev) => ({
                  ...prev,
                  searchQuery: popularPlaceLocation,
                }));
              } else {
                // Final fallback - leave empty for manual selection
                setFormData((prev) => ({
                  ...prev,
                  searchQuery: "",
                }));
              }
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 300000, // Cache for 5 minutes
            }
          );
        } else {
          // Step 4: If geolocation is not supported, use first popular place as fallback
          if (popularPlaces.length > 0) {
            const firstPopularPlace = popularPlaces[0];
            const popularPlaceLocation = `${firstPopularPlace.name}, ${firstPopularPlace.city}`;
            setFormData((prev) => ({
              ...prev,
              searchQuery: popularPlaceLocation,
            }));
          }
        }
      } catch (error) {
        console.error("Error initializing location:", error);

        // If any error occurs during initialization, use first popular place as fallback
        if (popularPlaces.length > 0) {
          const firstPopularPlace = popularPlaces[0];
          const popularPlaceLocation = `${firstPopularPlace.name}, ${firstPopularPlace.city}`;
          setFormData((prev) => ({
            ...prev,
            searchQuery: popularPlaceLocation,
          }));
        }
      }
    };

    // Run initialization after a short delay to ensure component is mounted
    const timeoutId = setTimeout(initializeLocation, 100);

    return () => clearTimeout(timeoutId);
  }, [isMounted, formData.searchQuery, t]); // Run when mounted state or search query changes

  // Handle window resize for mobile modal detection and mobile view
  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        setWindowWidth(width);
        setIsMobileView(width <= 950);
      }
    };

    // Set mounted state and initial window width
    setIsMounted(true);
    handleResize();

    // Add event listener for window resize
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
    }

    // Cleanup event listener
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  // Simplified modal functions - use dropdown states for both mobile and desktop
  const closeModal = useCallback(() => {
    setIsLocationDropdownOpen(false);
    setIsDateDropdownOpen(false);
    setIsTravelersDropdownOpen(false);
    setGlobalMobileModalOpen(false); // Update global state
  }, [setGlobalMobileModalOpen]);

  const openModal = (type: 'location' | 'date' | 'travelers') => {
    // Clear location search query when opening location modal for fresh search
    if (type === 'location') {
      setLocationSearchQuery('');
    }

    // Close all dropdowns first
    setIsLocationDropdownOpen(false);
    setIsDateDropdownOpen(false);
    setIsTravelersDropdownOpen(false);

    // Open the specific modal
    switch (type) {
      case 'location':
        setIsLocationDropdownOpen(true);
        break;
      case 'date':
        setIsDateDropdownOpen(true);
        break;
      case 'travelers':
        setIsTravelersDropdownOpen(true);
        break;
    }

    setGlobalMobileModalOpen(true); // Update global state
  };

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && (isLocationDropdownOpen || isDateDropdownOpen || isTravelersDropdownOpen)) {
        closeModal();
      }
    };

    if (isLocationDropdownOpen || isDateDropdownOpen || isTravelersDropdownOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isLocationDropdownOpen, isDateDropdownOpen, isTravelersDropdownOpen, closeModal]);

  // Cleanup body scroll on component unmount
  useEffect(() => {
    return () => {
      // Restore body scroll if component unmounts while modal is open
      if (typeof document !== 'undefined') {
        document.body.style.overflow = 'unset';
      }
    };
  }, []);

  // Trigger API call when location search query changes with debouncing
  useEffect(() => {
    if (locationSearchQuery && locationSearchQuery.trim().length > 0) {
      const trimmedQuery = locationSearchQuery.trim();

      // Don't call API for status/loading messages
      const statusMessages = [
        t("search.fetching_location"),
        t("search.searching_location"),
        t("search.current_location")
      ];

      const isStatusMessage = statusMessages.some(msg =>
        locationSearchQuery.includes(msg) || msg.includes(locationSearchQuery)
      );

      // Don't call API if it's the same search term as last time
      if (!isStatusMessage && lastSearchTermRef.current !== trimmedQuery) {
        // Debounce API call - only call when user stops typing for 500ms
        const timeoutId = setTimeout(() => {
          lastSearchTermRef.current = trimmedQuery; // Update the ref before API call
          handleAutoSuggestApi(trimmedQuery);
        }, 500);

        return () => clearTimeout(timeoutId);
      }
    } else {
      // Clear location list when search query is empty
      lastSearchTermRef.current = ""; // Reset the ref
      setLocationList([]);
      setLocalFilteredResults([]);
      setShowLocalResults(true);
      setIsLocationLoading(false);
    }
  }, [locationSearchQuery, t]); // Removed handleAutoSuggestApi from dependencies

  // Handle field clicks - unified for both mobile and desktop
  const handleFieldClick = (type: 'location' | 'date' | 'travelers') => {
    // Check if the specific modal is already open and toggle it
    const isCurrentlyOpen =
      (type === 'location' && isLocationDropdownOpen) ||
      (type === 'date' && isDateDropdownOpen) ||
      (type === 'travelers' && isTravelersDropdownOpen);

    if (isCurrentlyOpen) {
      closeModal();
    } else {
      openModal(type);
    }
  };

  // Handle date selection
  const handleDateChange = (dates: [Date | null, Date | null]) => {
    setFormData((prev) => ({
      ...prev,
      checkInDate: dates[0]?.toISOString() || null,
      checkOutDate: dates[1]?.toISOString() || null,
    }));
  };

  // Close all dropdowns when clicking outside, with special handling for date picker
  const closeAllDropdowns = (e?: React.MouseEvent | MouseEvent) => {
    setIsLocationDropdownOpen(false);
    setIsTravelersDropdownOpen(false);

    // Only close date dropdown if click is outside the date picker
    if (
      e &&
      dateDropdownRef.current &&
      !dateDropdownRef.current.contains(e.target as Node)
    ) {
      setIsDateDropdownOpen(false);
    }
  };

  // Note: Dropdown positioning is now handled by the useDynamicModalHeight hook
  // The old positioning logic has been replaced with the dynamic modal height system

  // Conditional classes based on isModify prop
  const containerClasses = `${
    styles.searchContainer
  } bg-white rounded-xl shadow-2xl overflow-hidden border border-gray-100 ${
    isModify ? styles.modifyMode : ""
  }`;
  const fieldPadding = isModify ? "py-2" : "py-4";
  const searchBtnClasses = `px-6 text-white font-medium flex items-center justify-center transition-all duration-300 cursor-pointer ${
    styles.searchButton
  } ${isModify ? styles.modifyButton : ""}`;

  // Prevent hydration mismatch by not rendering mobile-specific content until mounted
  if (!isMounted || windowWidth === undefined || isMobileView === undefined) {
    return (
      <div className={`w-full ${isModify ? "max-w-full" : "max-w-5xl"} mx-auto relative`}>
        <div className={containerClasses}>
          <div className="flex items-center justify-center p-8">
            <div className="animate-pulse text-gray-500">Loading...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`w-full ${
        isModify ? "max-w-full" : "w-full"
      } mx-auto relative ${isModify ? "" : isMobileView ? "mt-0" : "-mt-8"}`}
    >
      <div className={containerClasses} >
        {isMobileView ? (
          // Mobile Layout - Stacked Cards
          <div className={styles.mobileSearchContainer}>
            {/* Location Card */}
            <div
              className={styles.mobileSearchCard}
              onClick={() => handleFieldClick('location')}
            >
              <div className={styles.mobileCardHeader}>
                <MapPin size={18} className={styles.textPrimary} />
                <span className={styles.mobileCardLabel}>
                  {t("search.destination_header")}
                </span>
                {validationError && (
                  <span className={styles.mobileErrorText}>
                    {validationError}
                  </span>
                )}
              </div>
              <div className={styles.mobileCardContent}>
                <input
                  type="text"
                  placeholder={t("search.destination_placeholder")}
                  className={styles.mobileInput}
                  value={formData.searchQuery}
                  onChange={(e) => handleSearchQueryChange(e.target.value)}
                  readOnly
                />
                <button
                  className={styles.mobileLocationButton}
                  onClick={(e) => {
                    e.stopPropagation();
                    getCurrentLocation(e);
                  }}
                  disabled={isLocationLoading}
                >
                  {isLocationLoading ? (
                    <RotateCcw size={18} className="animate-spin" />
                  ) : (
                    <LocateFixed size={18} />
                  )}
                </button>
              </div>
            </div>

            {/* Date Card */}
            <div
              className={styles.mobileSearchCard}
              onClick={() => handleFieldClick('date')}
            >
              <div className={styles.mobileCardHeader}>
                <Calendar size={18} className={styles.textPrimary} />
                <span className={styles.mobileCardLabel}>
                  {t("search.checkin_header")} - {t("search.checkout_header")}
                </span>
              </div>
              <div className={styles.mobileCardContent}>
                <div className={styles.mobileCardValue}>
                  {formData.checkInDate && formData.checkOutDate
                    ? `${formatDateForDisplay(formData.checkInDate)} - ${formatDateForDisplay(formData.checkOutDate)}`
                    : `${t("search.checkin_header")} - ${t("search.checkout_header")}`}
                </div>
              </div>
            </div>

            {/* Travelers Card */}
            <div
              className={styles.mobileSearchCard}
              onClick={() => handleFieldClick('travelers')}
            >
              <div className={styles.mobileCardHeader}>
                <Users size={18} className={styles.textPrimary} />
                <span className={styles.mobileCardLabel}>
                  {t("search.travelers_header")}
                </span>
              </div>
              <div className={styles.mobileCardContent}>
                <div className={styles.mobileCardValue}>
                  {formData.travelers.adults} {t("search.travelers.adults")}, {formData.travelers.rooms} {t("search.travelers.rooms")}
                  {formData.travelers.children > 0 && `, ${formData.travelers.children} ${t("search.travelers.children")}`}
                </div>
              </div>
            </div>

            {/* Mobile Search Button */}
            <button
              className={styles.mobileSearchButton}
              onClick={handleSearch}
            >
              <Search size={20} className="mr-2" />
              {t("search.button")}
            </button>
          </div>
        ) : (
          // Desktop Layout - Original Horizontal
          <div className="flex flex-col md:flex-row">
          {/* Location Field */}
          <div
            ref={locationFieldRef}
            className="relative flex-1 group border-b md:border-b-0 md:border-r border-gray-200"
          >
            <div
              className={`h-full px-4 ${fieldPadding} cursor-pointer`}
              onClick={() => handleFieldClick('location')}
            >
              <div className="flex flex-row items-center">
                <div style={{width:'100%'}}>
                  <div className="flex items-center text-gray-500 mb-1">
                    <MapPin
                      size={15}
                      className={`mr-1 ${styles.textPrimary}`}
                      style={{ marginTop: "1px" }}
                    />
                    <span
                      style={{ fontSize: "13px" }}
                      className={` font-medium ${isModify ? "text-xs" : ""}`}
                    >
                      {t("search.destination_header")}
                    </span>
                    {validationError && (
                      <span className={`ml-2 text-xs text-red-500`}>
                        {validationError}
                      </span>
                    )}
                  </div>
                  <div className="relative flex items-center">
                    <input
                      type="text"
                      placeholder={t("search.destination_placeholder")}
                      className={`w-full font-bold text-gray-800 focus:outline-none border-none ${
                        isModify ? "" : "text-sm"
                      }  ${validationError ? "border-red-500" : ""}`}
                      value={formData.searchQuery}
                      onChange={(e) => handleLocationSearchQueryChange(e.target.value)}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFieldClick('location');
                      }}
                    />
                  </div>
                </div>

                <button
                  className="absolute right-0 p-1 text-gray-500 hover:text-gray-700 transition-colors cursor-pointer rtl-location-icon"
                  onClick={getCurrentLocation}
                  disabled={isLocationLoading}
                >
                  {isLocationLoading ? (
                    <RotateCcw size={16} className="mr-2 animate-spin" />
                  ) : (
                    <LocateFixed size={16} className="mr-2 w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Date Field */}
          <div
            ref={dateFieldRef}
            className="relative flex-1 group border-b md:border-b-0 md:border-r border-gray-200"
          >
            <div
              className={`h-full px-4 ${fieldPadding} cursor-pointer`}
              onClick={() => handleFieldClick('date')}
            >
              <div className="flex items-center text-gray-500 mb-1">
                <Calendar size={15} className={`mr-1 ${styles.textPrimary}`} />
                <span
                  style={{ fontSize: "13px" }}
                  className={` font-medium ${isModify ? "" : ""}`}
                >
                  {t("search.checkin_header")} - {t("search.checkout_header")}
                </span>
              </div>
              <div
                className={`text-gray-800 font-bold ${
                  isModify ? "text-xs" : "text-sm"
                }`}
              >
                {formData.checkInDate && formData.checkOutDate
                  ? `${formatDateForDisplay(
                      formData.checkInDate
                    )} - ${formatDateForDisplay(formData.checkOutDate)}`
                  : `${t("search.checkin_header")} - ${t(
                      "search.checkout_header"
                    )}`}
              </div>
            </div>
          </div>

          {/* Travelers Field */}
          <div
            ref={travelersFieldRef}
            className={`relative flex-1 group travel-field-container ${styles.rtlBorder}`}
          >
            <div
              className={`h-full px-4 ${fieldPadding} cursor-pointer`}
              onClick={() => handleFieldClick('travelers')}
            >
              <div className="flex items-center text-gray-500 mb-1">
                <Users
                  size={15}
                  className={`mr-1 ${styles.textPrimary}`}
                  style={{ marginTop: "1px" }}
                />
                <span
                  style={{ fontSize: "13px" }}
                  className={` font-medium ${isModify ? "" : ""}`}
                >
                  {t("search.travelers_header")}
                </span>
              </div>
              <div
                className={`text-gray-800  font-bold ${
                  isModify ? "text-xs" : "text-sm"
                }`}
              >
                {formData.travelers.adults} {t("search.travelers.adults")},{" "}
                {formData.travelers.rooms} {t("search.travelers.rooms")}
                {formData.travelers.children > 0
                  ? `, ${formData.travelers.children} ${t(
                      "search.travelers.children"
                    )}`
                  : ""}
              </div>
            </div>
          </div>

          {shareButtonFlag && (
            <>
              {/* share group */}
              <ul
                className={`${styles.shareGroup} ${
                  isShareGroupVisible ? styles.active : ""
                }`}
              >
                <li className={styles.labelWrapper}>
                  <div className={styles.label}>
                    <span className={styles.count}>
                      {selectedHotels?.length || 0}
                    </span>
                    <span className={styles.label}>Item selected</span>
                  </div>
                </li>
                <li className={styles.item} onClick={shareViaWhatsApp}>
                  <i
                    className={`fa-brands fa-whatsapp ${styles.socialMediaIcon}`}
                  ></i>
                </li>
                <li className={styles.item} onClick={shareViaEmail}>
                  <i
                    className={`fa-regular fa-envelope ${styles.envelopeIcon}`}
                  ></i>
                </li>
              </ul>

              {/* share button */}
              <div
                className={styles.shareButtonContainer}
                onClick={() => setIsShareGroupVisible((prev) => !prev)}
              >
                <button type="button" className={styles.shareButton}>
                  {isShareGroupVisible ? (
                    <X className={styles.shareIcon} size={20} />
                  ) : (
                    <Share2 className={styles.shareIcon} size={20} />
                  )}
                </button>
              </div>
            </>
          )}

          {/* Search Button */}
          <div className="flex items-stretch">
            <button className={searchBtnClasses} onClick={handleSearch}>
              <Search size={16} className="mr-1" />
              {t("search.button")}
            </button>
          </div>
        </div>
        )}
      </div>

      {/* Desktop Dropdowns - Only render on desktop */}
      {!isMobileView && (
        <>
          {/* Location Dropdown */}
          {isLocationDropdownOpen && (
            <div
              ref={(el) => {
                locationDropdownRef.current = el;
                locationModalHeight.modalRef.current = el;
              }}
              className={`${styles.dropdown} ${styles.locationDropdown} z-base`}
              onClick={(e) => e.stopPropagation()}
            >
              <LocationSearch
                onLocationSelect={handleLocationSelect}
                onLocationObjectSelect={(locationObject) => {
                  // Handle location object selection with ID and coordinates
                  handleLocationObjectSelect(locationObject);

                  // Call location details API when a location object is selected
                  if (locationObject.id) {
                    handleLocationDetailsApi(locationObject.id, locationObject.fullName || locationObject.name);
                  }
                }}
                searchQuery={locationSearchQuery}
                setSearchQuery={(value: string) => handleLocationSearchQueryChange(value)}
                handleClose={() => setIsLocationDropdownOpen(false)}
                autoFetchLocation={(!locationSearchQuery || locationSearchQuery.trim() === "") && (!formData.searchQuery || formData.searchQuery.trim() === "")}
                popularPlacesData={convertPopularPlacesToLocationSuggestions()}
                recentSearchesData={convertRecentSearchesToLocationSuggestions()}
                apiSuggestions={locationList}
                localFilteredResults={localFilteredResults}
                showLocalResults={showLocalResults}
                isLoading={isLocationLoading}
                onAutoSuggestApi={handleCurrentLocationAutoSuggest}
              />
            </div>
          )}

          {/* Date Dropdown */}
          {isDateDropdownOpen && (
            <div
              ref={(el) => {
                dateDropdownRef.current = el;
                dateModalHeight.modalRef.current = el;
              }}
              className={`${styles.dropdown} ${styles.dateDropdown} z-base`}
              onClick={(e) => e.stopPropagation()}
            >
              <HotelDateSelection
                onDatesChange={handleDateChange}
                startDate={
                  formData.checkInDate ? new Date(formData.checkInDate) : null
                }
                endDate={
                  formData.checkOutDate ? new Date(formData.checkOutDate) : null
                }
                onClose={() => {
                  setIsDateDropdownOpen(false);
                }}
              />
            </div>
          )}

          {/* Travelers Dropdown */}
          {isTravelersDropdownOpen && (
            <div
              ref={(el) => {
                travelersDropdownRef.current = el;
                travelersModalHeight.modalRef.current = el;
              }}
              className={`${styles.dropdown} ${styles.travelerDropdown} z-base`}
              onClick={(e) => e.stopPropagation()}
            >
              <HotelTravelerSelector
                initialRooms={formData.roomsData}
                onRoomsChange={handleRoomsChange}
                handleClose={() => setIsTravelersDropdownOpen(false)}
              />
            </div>
          )}
        </>
      )}

      {/* Mobile Popups - Only render on mobile */}
      {isMobileView && (
        <>
          {/* Location Popup */}
          <BottomToTopPopup
            isOpen={isLocationDropdownOpen}
            onClose={closeModal}
            heading={t("search.destination_header")}
            type="search"
            enableDrag={true}
            snapPoints={[25, 50, 75, 95]}
          >
            <LocationSearch
              onLocationSelect={(location: string) => {
                handleLocationSelect(location);
                closeModal();
              }}
              onLocationObjectSelect={(locationObject) => {
                // Handle location object selection with ID and coordinates
                handleLocationObjectSelect(locationObject);

                // Call location details API when a location object is selected
                if (locationObject.id) {
                  handleLocationDetailsApi(locationObject.id, locationObject.fullName || locationObject.name);
                }
                closeModal();
              }}
              searchQuery={locationSearchQuery}
              setSearchQuery={handleLocationSearchQueryChange}
              handleClose={closeModal}
              autoFetchLocation={(!locationSearchQuery || locationSearchQuery.trim() === "") && (!formData.searchQuery || formData.searchQuery.trim() === "")}
              popularPlacesData={convertPopularPlacesToLocationSuggestions()}
              recentSearchesData={convertRecentSearchesToLocationSuggestions()}
              apiSuggestions={locationList}
              localFilteredResults={localFilteredResults}
              showLocalResults={showLocalResults}
              isLoading={isLocationLoading}
              onAutoSuggestApi={handleCurrentLocationAutoSuggest}
            />
          </BottomToTopPopup>

          {/* Date Popup */}
          <BottomToTopPopup
            isOpen={isDateDropdownOpen}
            onClose={closeModal}
            // heading={`${t("search.checkin_header")} - ${t("search.checkout_header")}`}
            type="calendar"
            enableDrag={true}
            snapPoints={[25, 50, 75, 95]}
          >
            <HotelDateSelection
              onDatesChange={(dates) => {
                handleDateChange(dates);
                // Only close modal when both dates are selected
                if (dates[0] && dates[1]) {
                  closeModal();
                }
              }}
              startDate={
                formData.checkInDate ? new Date(formData.checkInDate) : null
              }
              endDate={
                formData.checkOutDate ? new Date(formData.checkOutDate) : null
              }
              onClose={closeModal}
            />
          </BottomToTopPopup>

          {/* Travelers Popup */}
          <BottomUpPopup
            isOpen={isTravelersDropdownOpen}
            OnClose={closeModal}
            heading={t("search.travelers_header")}
            zindex={500}
            enableDrag={true}
            minHeight={30}
            maxHeight={90}
            snapPoints={[30, 60, 90]}
          >
            <HotelTravelerSelector
              initialRooms={formData.roomsData}
              onRoomsChange={(rooms) => {
                handleRoomsChange(rooms);
                // Don't close modal automatically - let user continue editing
              }}
              handleClose={closeModal}
            />
          </BottomUpPopup>
        </>
      )}

      {/* Overlay to close dropdowns when clicking outside - Desktop only */}
      {!isMobileView && (isLocationDropdownOpen ||
        isDateDropdownOpen ||
        isTravelersDropdownOpen) && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 transition-opacity duration-200"
          onClick={(e) => closeAllDropdowns(e as React.MouseEvent)}
          style={{ zIndex: 99 }}
        ></div>
      )}
    </div>
  );
};

export default HotelSearchBar;
