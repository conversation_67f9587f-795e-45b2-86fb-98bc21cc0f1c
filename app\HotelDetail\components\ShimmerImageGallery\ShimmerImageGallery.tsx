"use client";
import React from 'react'
import './ShimmerImageGallery.scss'

function ShimmerImageGallery() {
  return (
    <div className="hotel-overview-container">
      <div className="hotel-overview-container__left-section">
        <div className="left-section-row1">
          <div className="left-section-row1__flex-col col1">
            <div className="shimmer-effect"></div>
          </div>
          <div className="left-section-row1__flex-col col2">
            <div className="sub-col">
              <div className="shimmer-effect"></div>
            </div>
            <div className="sub-col">
              <div className="shimmer-effect"></div>
            </div>
          </div>
        </div>
        <div className="left-section-row2">
          {Array.from({ length: 5 }).map((_, index) => (
            <div className="left-section-row2__col" key={index}>
              <div className="shimmer-effect"></div>
              {index === 4 && <div className="image-overlay-shimmer"></div>}
            </div>
          ))}
          <div className="morePhotos-shimmer">
            <div className="shimmer-icon"></div>
            <div className="shimmer-text"></div>
          </div>
        </div>
      </div>
      <div className="hotel-overview-container__right-section">
        {/* review floater shimmer */}
        <div className="reviewFloater">
          <div className="hp-gallery-score-card">
            <div className="review">
              <div className="rating-detail">
                <div className="detail-shimmer detail1"></div>
                <div className="detail-shimmer detail2"></div>
              </div>
              <div className="rating-shimmer"></div>
            </div>
          </div>
          <div className="best-review-score-card">
            <div className="best-review-score-card__label-shimmer"></div>
            <div className="best-review-score-card__count-shimmer"></div>
          </div>
        </div>

        {/* map floater shimmer */}
        <div className="hotel-search-map-container-shimmer">
          <div className="map-shimmer"></div>
        </div>
      </div>
    </div>
  )
}

export default ShimmerImageGallery