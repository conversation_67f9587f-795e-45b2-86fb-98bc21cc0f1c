import { Hotel } from "@/app/HotelSearchResult/hotel-search-result.model";
import { HotelFilterData } from "./filterHotels";

export const addFilterInList = (filterData: HotelFilterData | undefined, list: Hotel[]): Hotel[] => {
  // Early validation
  if (!filterData || !list || list.length === 0) {
    console.log("No filter data or empty hotel list provided, returning original list");
    return list;
  }

  

  // Create a deep copy to avoid mutations
  const hotelList = JSON.parse(JSON.stringify(list));

  // Check if any filters are active
  const hasActiveFilters = (
    (filterData.priceRange && 
      (filterData.priceRange.values.minimum > filterData.priceRange.min || 
       filterData.priceRange.values.maximum < filterData.priceRange.max)) ||
    filterData.starRatings.some(sr => sr.isSelected) ||
    filterData.userRatingCategories.some(ur => ur.isSelected) ||
    filterData.accommodationTypes.some(at => at.isSelected) ||
    filterData.amenities.some(am => am.isSelected) ||
    filterData.roomTypes.some(rt => rt.isSelected) ||
    (filterData.bedroom && filterData.bedroom.selected > 0) ||
    (filterData.bathroom && filterData.bathroom.selected > 0) ||
    filterData.specialOffers.some(so => so.isSelected) ||
    filterData.availabilityStatus.limitedRooms.isSelected ||
    filterData.availabilityStatus.lastMinuteDeals.isSelected
  );

  if (!hasActiveFilters) {
    console.log("No active filters, returning original list");
    return list;
  }

  // Helper function to parse distance string to number (in meters)
  const parseDistance = (distanceStr: string): number => {
    const match = distanceStr?.match(/(\d+(?:\.\d+)?)\s*(m|km)/i);
    if (!match) return 0;
    
    const value = parseFloat(match[1]);
    const unit = match[2].toLowerCase();
    
    return unit === "km" ? value * 1000 : value;
  };

  // Process each hotel
  hotelList.forEach((hotel: Hotel) => {
    // Default to visible
    hotel.isVisible = true;

    // Filter by price range
    if (hotel.isVisible && filterData.priceRange) {
      const price = hotel.fareDetail?.totalPrice ?? 0;
      if (price < filterData.priceRange.values.minimum || price > filterData.priceRange.values.maximum) {
        hotel.isVisible = false;
      }
    }

    // Filter by star rating
    if (hotel.isVisible && filterData.starRatings.some(sr => sr.isSelected)) {
      const selectedRatings = filterData.starRatings.filter(sr => sr.isSelected);
      const ratingMatch = selectedRatings.some(sr => sr.key === hotel.starRating);
      if (!ratingMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by user rating category
    if (hotel.isVisible && filterData.userRatingCategories.some(ur => ur.isSelected)) {
      const selectedCategories = filterData.userRatingCategories.filter(ur => ur.isSelected);
      const ratingCategory = hotel.userRatingCategory?.toLowerCase() || "";
      const categoryMatch = selectedCategories.some(ur => ur.key === ratingCategory);
      if (!categoryMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by accommodation type
    if (hotel.isVisible && filterData.accommodationTypes.some(at => at.isSelected)) {
      const selectedTypes = filterData.accommodationTypes.filter(at => at.isSelected);
      const accType = hotel.accommodationType?.toLowerCase() || "";
      const typeMatch = selectedTypes.some(at => at.key === accType);
      if (!typeMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by amenities
    if (hotel.isVisible && filterData.amenities.some(am => am.isSelected)) {
      const selectedAmenities = filterData.amenities.filter(am => am.isSelected);
      const amenitiesMatch = selectedAmenities.every(selectedAmenity => {
        return hotel.amenities?.some(hotelAmenity => 
          hotelAmenity.name?.toLowerCase() === selectedAmenity.key
        );
      });
      if (!amenitiesMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by room type
    if (hotel.isVisible && filterData.roomTypes.some(rt => rt.isSelected)) {
      const selectedRoomTypes = filterData.roomTypes.filter(rt => rt.isSelected);
      const roomTypeMatch = selectedRoomTypes.some(selectedRoomType => {
        return hotel.roomDetails?.some(room => room.type === selectedRoomType.key);
      });
      if (!roomTypeMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by bedroom count
    if (hotel.isVisible && filterData.bedroom && filterData.bedroom.selected > 0) {
      const bedroomMatch = hotel.roomDetails?.some(room => room.bedroom >= filterData.bedroom.selected);
      if (!bedroomMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by bathroom count
    if (hotel.isVisible && filterData.bathroom && filterData.bathroom.selected > 0) {
      const bathroomMatch = hotel.roomDetails?.some(room => room.bathroom >= filterData.bathroom.selected);
      if (!bathroomMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by distance from landmark
    if (hotel.isVisible && filterData.distanceFromLandmark &&
        filterData.distanceFromLandmark.min < filterData.distanceFromLandmark.max) {
      const distance = parseDistance(hotel.distanceFromSearchedEntity || "0m");
      if (distance < filterData.distanceFromLandmark.min || 
          distance > filterData.distanceFromLandmark.max) {
        hotel.isVisible = false;
      }
    }

    // Filter by special offers
    if (hotel.isVisible && filterData.specialOffers.some(so => so.isSelected)) {
      const selectedOffers = filterData.specialOffers.filter(so => so.isSelected);
      const offerMatch = selectedOffers.some(selectedOffer => {
        return hotel.topOfferings?.some(offer => 
          offer.toLowerCase() === selectedOffer.key
        );
      });
      if (!offerMatch) {
        hotel.isVisible = false;
      }
    }

    // Filter by availability status
    if (hotel.isVisible) {
      if (filterData.availabilityStatus.limitedRooms.isSelected) {
        if (!(hotel.roomsCountLeft && hotel.roomsCountLeft < 5)) {
          hotel.isVisible = false;
        }
      }
      
      if (hotel.isVisible && filterData.availabilityStatus.lastMinuteDeals.isSelected) {
        const hasLastMinuteDeal = hotel.fomoTags?.some(tag => 
          tag.fomoType === "LAST_MINUTE_DEAL"
        );
        
        if (!hasLastMinuteDeal) {
          hotel.isVisible = false;
        }
      }
    }
  });

  // Return filtered list
  const result = hotelList.filter((hotel: Hotel) => hotel.isVisible === true);
  console.log(`Final filtered count: ${result.length}`);
  
  return result;
};
