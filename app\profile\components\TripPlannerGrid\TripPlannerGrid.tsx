"use client";
import React from "react";
import "./TripPlannerGrid.scss";

function TripPlannerGrid() {
  const services = [
    {
      label: "Find Places to Stay",
      icon: "fa-building",
    },
    {
      label: "Find Acitivities",
      icon: "fa-person-hiking",
    },
    {
      label: "Find Flights",
      icon: "fa-plane",
    },
    {
      label: "Find Airport Transfer",
      icon: "fa-van-shuttle",
    },
    {
      label: "Find Car Rentals",
      icon: "fa-car",
    },
    {
      label: "Find Transport Options",
      icon: "fa-route",
    },
    {
      label: "Find eSIM Cards",
      icon: "fa-sim-card",
    },
  ];

  return (
    <div className="trip-planner-grid">
      <h3 className="trip-planner-grid__header">
        Start planning your next trip?
      </h3>

      <div className="trip-planner-grid__cards-container">
        {services?.map((service, index) => (
          <div key={index} className="trip-planner-grid__card">
            <div className="trip-planner-grid__card-image">
              <i className={`fa-solid ${service?.icon}`}></i>
            </div>
            <p>{service?.label}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

export default TripPlannerGrid;
