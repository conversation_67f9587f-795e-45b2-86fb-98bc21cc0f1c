# Active Context

## Current Task
Updated HotelSearchBar component mobile layout (≤950px) to use popular stacked cards approach following industry standards.

## Recent Changes
- **Implemented Full-Space Mobile Design**: Removed background image and padding for mobile (≤950px) to let search bar utilize full available space
- **Mobile-First Search Experience**: Clean background with centered search cards taking full viewport height
- **Fixed Search Bar Top Cutoff at 768px**: Reduced negative margin from -mt-8 to -mt-4 for mobile views to prevent clipping
- **Fixed 768px Breakpoint Issue**: Resolved media query conflicts that were breaking styles at exactly 768px
- **Reorganized Media Queries**: Used range-based media queries to prevent overlapping styles
- **Updated .hotel-searchbar-landing-main-container**: Enhanced responsive design for both mobile and desktop views
- **Enhanced Desktop Experience**: Increased height, better gradients, and improved visual depth
- **Added Visual Depth**: Subtle overlay patterns and improved gradient backgrounds
- **Responsive Height Management**: Auto height on mobile with proper min-height constraints
- **Performance Optimizations**: Scroll-based background attachment on mobile for better performance
- **Modern Border Radius**: Updated border radius values for contemporary design
- **Consistent Breakpoint Usage**: Aligned with established 950px mobile breakpoint pattern

## Current Implementation

### .hotel-searchbar-landing-main-container Styling
- **Desktop (>950px):**
  - **Height**: 500px fixed height for consistent hero section
  - **Padding**: 40px 20px for proper spacing
  - **Background**: 135deg gradient with fixed attachment for parallax effect
  - **Border Radius**: 16px bottom corners for modern appearance
  - **Search Bar Width**: 85% with 1000px max-width for optimal readability

- **Mobile (≤950px) - Full Space Utilization:**
  - **Height**: Auto with content-driven height
  - **Padding**: Removed (0) for full space utilization
  - **Background**: Clean light background (#f8f9fa) instead of image
  - **Border Radius**: Removed for full-width mobile design
  - **Search Container**: Full viewport height with centered cards
  - **Search Bar Width**: 100% for maximum mobile utilization

- **Visual Enhancements:**
  - **Overlay Pattern**: Radial gradient overlay for visual depth
  - **Z-index Management**: Proper layering of background, overlay, and search bar
  - **Performance**: Optimized background attachment for mobile devices

### HotelSearchBar Component
- **Mobile Layout (≤950px) - Stacked Cards:**
  - **Location Card**: Search destination with location icon, input field, and GPS button
  - **Date Card**: Check-in/check-out dates with calendar icon and formatted date display
  - **Travelers Card**: Guest and room selection with users icon and summary display
  - **Search Button**: Large, prominent CTA button with gradient background and hover effects
  - **Touch-Friendly Design**: Large cards with proper spacing for mobile interaction
  - **Visual Feedback**: Hover effects, shadows, and smooth transitions
  - **Modal Integration**: Maintains existing modal system for field editing

- **Desktop Layout (>950px):**
  - **Original Horizontal Layout**: Preserved existing side-by-side field arrangement
  - **Dropdown System**: Maintains existing dropdown functionality
  - **All Features Intact**: No changes to desktop behavior or styling

- **Desktop Layout (>950px):**
  - Original table-like layout preserved
  - Sticky booking summary on the right side
  - Header with room type dropdown functionality
  - All existing functionality maintained

- **Responsive Design:**
  - Uses established codebase pattern for mobile detection
  - Proper server-side rendering safety with `typeof window !== 'undefined'`
  - Window resize events handled with cleanup
  - Consistent with other components in the application

## Technical Details
- Added `isMobileView` state variable
- Conditional rendering based on screen width
- Mobile-specific CSS classes with proper styling
- Maintained all existing functionality (room selection, pricing, etc.)
- Sticky positioning for mobile booking summary

## Next Steps
1. Test mobile layout on devices ≤950px width
2. Verify desktop layout remains unchanged
3. Test room selection functionality in both layouts
4. Test sticky booking summary behavior on mobile
5. Verify window resize detection works properly
6. Test booking flow from mobile cards to booking preview
