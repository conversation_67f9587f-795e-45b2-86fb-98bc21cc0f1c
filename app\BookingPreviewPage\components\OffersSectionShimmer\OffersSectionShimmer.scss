@use "/styles/variable" as *;
@use "sass:color";

.offers-section-shimmer-container {
  width: 100%;
  padding: 20px 20px 10px;
  background-color: #ffffff;
  border-radius: 15px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

  @media (max-width: $breakpoint-md) {
    display: none;
  }

  //shimmer animation for all blocks

  .shimmer {
    background: linear-gradient(90deg, #f5f5f5 25%, #f5f5f5 50%, #eaeaea 75%);
    background-size: 200% 100%;
    border-radius: 4px;
    animation: shimmer 1.5s infinite;
    

  }

  &__header {
    width: 60%;
    height: 20px;
    margin-bottom: 10px;
    @extend .shimmer;
  }

  hr {
    margin: 8px 0 10px 0;
    padding: 0 15px;
    background: #eaeaea;
    height: 1;

   
  }

  .input-container {
    width: 100%;
    height: 30px;
    max-width: 400px;
    @extend .shimmer;
  }

  &__promo-selector {
    .promo-selector-option {
      padding: 10px 0;
      display: flex;
      align-items: flex-start;
      column-gap: 5px;

      .radio-button {
      
        width: 20px;
          height: 20px;
          border-radius: 50% !important;
         @extend .shimmer;
      }

      .promo-selector__content {
        flex-grow: 1;

        .promo-selector__header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .promo-selector__title {
            width: 40%;
            height: 18px;
            @extend .shimmer;
          }

          .promo-selector__discount {
            width: 18%;
            height: 15px;
            @extend .shimmer;
          }
        }

        .promo-selector__description {
          margin: 5px 0 0 0;
          width: 90%;
          height: 15px;
          @extend .shimmer;
        }

        .promo-selector__link {
          margin: 5px 0 0 0;
          width: 40%;
          height: 15px;
          @extend .shimmer;
        }
      }
    }

    .promo-selector-button-field {
      margin-top: 15px;

      .promo-selector-button {
        width: 45%;
        height: 25px;
        @extend .shimmer;
      }
    }
  }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
