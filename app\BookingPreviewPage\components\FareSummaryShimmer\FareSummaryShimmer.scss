@use "/styles/variable" as *;

.fare-summary-shimmer-container {
  width: 100%;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

  @media (max-width: $breakpoint-md) {
    display: none;
  }

  // Shimmer animation for all blocks
  .shimmer {
    background: linear-gradient(90deg, #f5f5f5 25%, #f5f5f5 50%, #eaeaea 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
  }

  &__header {
    height: 20px;
    width: 70%;
    margin-bottom: 15px;
    @extend .shimmer;
  }

  &__label-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .label {
      height: 15px;
      width: 40%;
      @extend .shimmer;

      &.discounts {
        width: 40%;
        height: 15px;
      }

      &.netAmt {
        width: 40%;
        height: 15px;
      }
    }

    .price {
      height: 14px;
      width: 20%;
      @extend .shimmer;

   

      &.netAmt {
        width: 20%;
        height: 14px;
      }
    }
  }

  hr {
    border: none;
    height: 1px;
    background: #eaeaea;
    margin-bottom: 12px;
  }
}

// Keyframes for shimmer animation
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
