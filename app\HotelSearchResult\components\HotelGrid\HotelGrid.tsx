"use client";
import React, { useState } from "react";
import "./HotelGrid.scss";
import Link from "next/link";

function HotelGrid() {
  const [isHeartActive, setIsHeartActive] = useState<boolean>(false);
  return (
    <div className="HotelGrid">
      <div className="grid-image">
        <img
          src="https://cf.bstatic.com/xdata/images/hotel/square600/625013916.webp?k=911e11ec311028bc2db58981f526f4be987dbd514ebb55ba4fd7339cd8355825&o="
          alt="room image"
        />

        <span
          onClick={() => setIsHeartActive(!isHeartActive)}
          className={`heartIcon ${isHeartActive ? "active" : ""}`}
        >
          <i className="fa-solid fa-heart "></i>
        </span>
      </div>
      <div className="grid-details">
        <div className="details">
          <div className="detail1">
          <div className="header">
              <span className="heading">
                <PERSON><PERSON><PERSON> Resort Munnar
              </span>

             <span className="icons"> <i className="fa-solid fa-star"></i>
                <i className="fa-solid fa-star"></i>
                <i className="fa-solid fa-star"></i>
                <i className="fa-solid fa-thumbs-up thumbsUpIcon"></i></span>

              {/* <p className="rating-stars-thumbsup">
            <i className="fa-solid fa-star"></i> 
            <i className="fa-solid fa-star"></i>
            <i className="fa-solid fa-star"></i> 
            <span className="thumbsUpIcon">
            <i className="fa-solid fa-thumbs-up"></i>
            </span>
            
            </p> */}
            </div>

            <div className="rating-review">
              <div className="rating">
                <span>8.4</span>
              </div>
              <div className="review">
                <span className="msg">Very good</span>
                <div className="dot">
                    .
                </div>
                <p className="count">
                  734 reviews
                </p>
              </div>
            </div>

            <p className="overview">Comfort 8.7</p>

            <div className="location-distance">
             <div className="links">
             <Link href={""} className="location">
                Munnar
              </Link>
                <div className="dot">
                    .
                </div>
              <Link href={""} className="location">
                Shoe on map
              </Link>
              <div className="dot">
                    .
                </div>
             </div>
              <p className="distance">1.2 km from centre</p>
            </div>

            <div className="list">
              <p className="list-title">Grande Suite</p>
              <ul>
                <li>Private suite</li>
                <li>1 bedroom</li>
                <li>1 living room</li>
                <li>1 bathroom</li>
                <li>41m²</li>
                <li>1 double bed</li>
              </ul>

              <div className="message">
                Only 4 left at this price on our site
              </div>

              
            </div>

            <div className="travel-details">
              <p className="detail">19 nights, 2 adults</p>
              <span className="price">₹ 38,887</span>
              <p className="detail">+₹ 4,666 taxes and charges</p>
            </div>
          </div>
      
        </div>
        {/* <div className="see-availability-button-field">
          <div className="seeAvailabilityBtn">
            See availability <i className="fa-solid fa-greater-than"></i>
          </div>
        </div> */}
      </div>
    </div>
  );
}

export default HotelGrid;
