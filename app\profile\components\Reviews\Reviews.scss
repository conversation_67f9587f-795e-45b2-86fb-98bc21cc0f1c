@use "/styles/variable" as *;
@use "sass:color";



.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 380px;
  
  p {
    color: #555;
    font-size: 15px;
    font-weight: 500;
  }
}

.section-header {
  margin-bottom: 20px;
  
  .section-heading {
    font-weight: 700;
    margin-bottom: 5px;
    color: #495057;
    font-size: 1rem;
  }
  
  .section-subheading {
    font-size: 14px;
    color: #666;
  }
}

.add-review-section {
  margin-bottom: 30px;
}

.booked-hotels-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.booked-hotel-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 10px;
  background-color: #f9f9f9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;  

  @media (max-width: $breakpoint-xs) {
    flex-direction: column;
    row-gap: 10px;
    align-items: start;
    
  }
  
  &:hover {
    transform: translateY(-2px);
  }
  
  .hotel-info {
    h5 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
      color: $primary-color;
    }
    
    .location {
      font-size: 13px;
      color: #666;
      font-weight: 500;
      margin-bottom: 3px;
    }
    
    .stay-dates {
      font-size: 12px;
      color: #888;
    }
  }
  
  .add-review-button {
    background-color: $primary-color;
    color: white;
    font-size: 13px;
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: color.adjust($primary-color, $lightness: -5%);
    }
  }
}

.no-hotels-to-review {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 10px;
  
  p {
    color: #666;
    font-size: 14px;
  }
}

.review-form-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    @media (max-width: $breakpoint-xs) {
      align-items: start;
      
    }
    
    
    h5 {
      font-size: 18px;
      font-weight: 600;
      color: $primary-color;

      @media (max-width: $breakpoint-xs) {
        font-size: 16px;
        
      }
    }
    
    .close-button {
      background: none;
      border: none;
      font-size: 24px;
      color: #999;
      cursor: pointer;

      @media (max-width: $breakpoint-xs) {
        height: 30px;
        width: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

   
      
      &:hover {
        color: #666;
      }
    }
  }
}

.review-form {
  .form-group {
    margin-bottom: 20px;
    
    label {
      display: block;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #444;
    }
    
    textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 14px;
      resize: vertical;
      
      &:focus {
        outline: none;
        border-color: $primary-color;
        box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
      }
    }
    
    .file-input {
      display: block;
      width: 100%;
      padding: 10px 0;
      font-size: 12px;
    }
  }
  
  .selectable-stars {
    display: flex;
    align-items: center;
    
    .star {
      font-size: 24px;
      color: #ccc;
      cursor: pointer;
      margin-right: 5px;
      transition: color 0.2s ease;
      
      &:hover {
        color: color.adjust($secondary-color, $lightness: -5%);
      }
      
      &.filled {
        color: $secondary-color;
      }
    }
  }
  
  .image-previews {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    
    .image-preview-container {
      position: relative;
      width: 80px;
      height: 80px;
      
      .image-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
      }
      
      .remove-image {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: white;
        color: #666;
        border: 1px solid #ddd;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        cursor: pointer;
        
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
    
    button {
      padding: 10px 20px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .cancel-button {
      background-color: #f5f5f5;
      color: #666;
      border: 1px solid #ddd;
      
      &:hover {
        background-color: #eee;
      }
    }
    
    .submit-button {
      background-color: $primary-color;
      color: white;
      border: none;
      
      &:hover {
        background-color: color.adjust($primary-color, $lightness: -5%);
      }
    }
  }
}

.reviews-section {
  margin-top: 40px;
}

.reviews-heading {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.review-card {
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background-color: #fff;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }
  
  .review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    
    .hotel-info {
      h5 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
        color: $primary-color;
      }
      
      .location {
        font-size: 13px;
        color: #666;
        font-weight: 500;
      }
    }
    
    .review-date {
      font-size: 12px;
      color: #888;
    }
  }
  
  .rating {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    
    .star {
      color: #ccc;
      font-size: 18px;
      margin-right: 2px;
      
      &.filled {
        color: $secondary-color;
      }
      
      &.half-filled {
        position: relative;
        color: #ccc;
        
        &:before {
          content: '★';
          position: absolute;
          color: $secondary-color;
          width: 50%;
          overflow: hidden;
        }
      }
    }
    
    .rating-value {
      margin-left: 5px;
      font-weight: 600;
      color: $secondary-color;
    }
  }
  
  .review-comment {
    font-size: 14px;
    line-height: 1.5;
    color: #444;
    margin-bottom: 15px;
  }
  
  .review-images {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    
    .review-image {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 8px;
      transition: transform 0.2s ease;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

.no-reviews-found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  
  h5 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 3px;
  }
  
  p {
    color: #555;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 15px;
  }
  
  .getStartedBtn {
    background-color: $primary-color;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 13px 27px;
    border-radius: 9999px;
    transition: background-color 0.2s ease;
    border: none;
    cursor: pointer;
    
    &:hover {
      background-color: color.adjust($primary-color, $lightness: -3%);
    }
  }
}