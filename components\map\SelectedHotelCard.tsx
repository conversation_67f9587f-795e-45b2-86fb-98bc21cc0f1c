'use client';

import React from 'react';
import Image from 'next/image';
import { Hotel as HotelType } from '@/app/HotelSearchResult/hotel-search-result.model';
import { useTranslation } from '@/app/hooks/useTranslation';
import './SelectedHotelCard.scss';

interface SelectedHotelCardProps {
  hotel: HotelType;
  onClose: () => void;
}

const SelectedHotelCard: React.FC<SelectedHotelCardProps> = ({ hotel, onClose }) => {
  const { t } = useTranslation();

  return (
    <div className="selected-hotel-card">
      <div className="card-header">
        <h3>{hotel.name}</h3>
        <button className="close-btn" onClick={onClose}>
          <i className="fa-solid fa-xmark"></i>
        </button>
      </div>

      <div className="card-content">
        <div className="hotel-image">
          {hotel.imageInfoList && hotel.imageInfoList.length > 0 && hotel.imageInfoList[0].url ? (
            <div className="image-container">
              <Image
                src={hotel.imageInfoList[0].url}
                alt={hotel.name || 'Hotel image'}
                fill
                style={{ objectFit: 'cover' }}
                sizes="(max-width: 768px) 100vw, 300px"
              />
            </div>
          ) : (
            <div className="no-image">{t('map.noImageAvailable')}</div>
          )}
        </div>

        <div className="hotel-details">
          <div className="rating">
            {Array.from({ length: Math.max(0, hotel.starRating || 0) }).map((_, index) => (
              <i key={index} className="fa-solid fa-star"></i>
            ))}
          </div>

          <div className="location">
            <i className="fa-solid fa-location-dot"></i>
            <span>{hotel.locality || ''}{hotel.locality && hotel.city ? ', ' : ''}{hotel.city || ''}</span>
          </div>

          <div className="price">
            <span className="price-label">{t('map.price')}:</span>
            <span className="price-value">₹ {hotel.fareDetail?.totalPrice || 0}</span>
          </div>

          {hotel.userRating && (
            <div className="user-rating">
              <div className="rating-badge">{hotel.userRating}</div>
              <div className="rating-text">
                <span className="rating-category">{hotel.userRatingCategory || 'Good'}</span>
                <span className="rating-count">{hotel.userRatingCount || 0} {t('map.reviews')}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="card-footer">
        <a
          href={hotel.hotelId ? `/HotelDetail?hotelId=${hotel.hotelId}` : '#'}
          target={typeof window !== 'undefined' && window.innerWidth <= 768 ? '_self' : '_blank'}
          className="view-details-btn"
          onClick={(e) => {
            if (!hotel.hotelId) {
              e.preventDefault();
              console.error('Hotel ID is missing');
            }
          }}
        >
          {t('map.viewDetails')}
        </a>
      </div>
    </div>
  );
};

export default SelectedHotelCard;
