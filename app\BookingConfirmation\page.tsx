// pages/confirmation.js

"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import './BookingConfimation.scss';

export default function BookingConfirmation() {

  const [bookingStatus, setBookingStatus] = useState('success');


  const bookingDetails = {
    confirmationNumber: "HB78912345",
    hotel: "Ocean View Resort",
    roomType: "Deluxe King Suite",
    checkIn: "2025-05-15",
    checkOut: "2025-05-20",
    nights: 5,
    guests: 2,
    price: 1250.00,
    tax: 112.50,
    total: 1362.50,
    paymentMethod: "Visa ****1234",
    bookingDate: "2025-04-28",
    errorMessage: "Payment processing failed. Your credit card was declined."
  };

  // In a real app, you would get the status from a query parameter or context
  useEffect(() => {
    // Check if we're in the browser before accessing window
    if (typeof window !== 'undefined') {
      // Example: Check URL parameters for status
      const urlParams = new URLSearchParams(window.location.search);
      const status = urlParams.get('status');
      if (status === 'failed') {
        setBookingStatus('failed');
      }
    }
  }, []);

  const handlePrint = () => {
    if (typeof window !== 'undefined') {
      window.print();
    }
  };

  const handleRetry = () => {
    // In a real app, this would redirect to the payment page or booking flow
    if (typeof window !== 'undefined') {
      window.location.href = '/booking';
    }
  };

  // Render the success confirmation UI
  const renderSuccessContent = () => (
    <>
      <div className="confirmation-header success">
        <i className="fa-solid fa-circle-check"></i>
        <h1>Booking Confirmed</h1>
        <p>Your reservation has been successfully confirmed.</p>
      </div>

      <div className="confirmation-code">
        <p>Confirmation Code: <strong>{bookingDetails.confirmationNumber}</strong></p>
      </div>

      <div className="confirmation-details">
        <div className="section">
          <h2>Reservation Details</h2>
          <div className="detail-row">
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-hotel"></i>
                <div className="detail-text">
                  <label>Hotel</label>
                  <p>{bookingDetails.hotel}</p>
                </div>
              </div>
            </div>
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-bed"></i>
                <div className="detail-text">
                  <label>Room Type</label>
                  <p>{bookingDetails.roomType}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="detail-row">
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-calendar-days"></i>
                <div className="detail-text">
                  <label>Check-in</label>
                  <p>{bookingDetails.checkIn}</p>
                </div>
              </div>
            </div>
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-calendar-days"></i>
                <div className="detail-text">
                  <label>Check-out</label>
                  <p>{bookingDetails.checkOut}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="detail-row">
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-user"></i>
                <div className="detail-text">
                  <label>Guests</label>
                  <p>{bookingDetails.guests} Adults</p>
                </div>
              </div>
            </div>
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-moon"></i>
                <div className="detail-text">
                  <label>Length of Stay</label>
                  <p>{bookingDetails.nights} Nights</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="section">
          <h2>Payment Summary</h2>
          <div className="payment-summary">
            <div className="payment-row">
              <span>Room Rate ({bookingDetails.nights} nights)</span>
              <span>${bookingDetails.price.toFixed(2)}</span>
            </div>
            <div className="payment-row">
              <span>Taxes & Fees</span>
              <span>${bookingDetails.tax.toFixed(2)}</span>
            </div>
            <div className="payment-row total">
              <span>Total Amount</span>
              <span>${bookingDetails.total.toFixed(2)}</span>
            </div>
            <div className="payment-method">
              <span>Payment Method:</span>
              <span>{bookingDetails.paymentMethod}</span>
            </div>
          </div>
        </div>

        <div className="section info-section">
          <div className="info-item">
            <i className="fa-solid fa-circle-info"></i>
            <p>Check-in time starts at 3:00 PM, and check-out time is until 11:00 AM.</p>
          </div>
          <div className="info-item">
            <i className="fa-solid fa-envelope"></i>
            <p>A confirmation email has been sent to your registered email address.</p>
          </div>
        </div>

        <div className="actions">
          <Link href="/Itinerary" className="btn secondary">
            View Itinerary
          </Link>
          <Link href="/profile" className="btn secondary">
            My Bookings
          </Link>
          <button onClick={handlePrint} className="btn primary">
            <i className="fa-solid fa-print"></i> Print
          </button>
        </div>
      </div>
    </>
  );

  // Render the booking failed UI
  const renderFailedContent = () => (
    <>
      <div className="confirmation-header failed">
        <i className="fa-solid fa-circle-xmark"></i>
        <h1>Booking Failed</h1>
        <p>We encountered a problem with your reservation.</p>
      </div>

      <div className="error-message">
        <p>{bookingDetails.errorMessage}</p>
      </div>

      <div className="confirmation-details">
        <div className="section">
          <h2>Booking Details</h2>
          <div className="detail-row">
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-hotel"></i>
                <div className="detail-text">
                  <label>Hotel</label>
                  <p>{bookingDetails.hotel}</p>
                </div>
              </div>
            </div>
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-bed"></i>
                <div className="detail-text">
                  <label>Room Type</label>
                  <p>{bookingDetails.roomType}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="detail-row">
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-calendar-days"></i>
                <div className="detail-text">
                  <label>Check-in</label>
                  <p>{bookingDetails.checkIn}</p>
                </div>
              </div>
            </div>
            <div className="detail-col">
              <div className="detail-item">
                <i className="fa-solid fa-calendar-days"></i>
                <div className="detail-text">
                  <label>Check-out</label>
                  <p>{bookingDetails.checkOut}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="section payment-failed-section">
          <div className="info-item">
            <i className="fa-solid fa-triangle-exclamation"></i>
            <p>Your booking was not completed. Please try again or use a different payment method.</p>
          </div>
          <div className="info-item">
            <i className="fa-solid fa-credit-card"></i>
            <p>No charges have been made to your payment method.</p>
          </div>
        </div>

        <div className="actions">
          <button onClick={handleRetry} className="btn primary">
            <i className="fa-solid fa-rotate-right"></i> Try Again
          </button>
          <Link href="/contact" className="btn secondary">
            <i className="fa-solid fa-headset"></i> Contact Support
          </Link>
        </div>
      </div>
    </>
  );

  return (
    <div className={`booking-confirmation ${bookingStatus}`}>
      {bookingStatus === 'success' ? renderSuccessContent() : renderFailedContent()}

      <div className="footer">
        <p>Thank you for choosing KindAli</p>
        <div className="contact">
          <span><i className="fa-solid fa-phone"></i> **************</span>
          <span><i className="fa-solid fa-envelope"></i> <EMAIL></span>
        </div>
      </div>
    </div>
  );
}