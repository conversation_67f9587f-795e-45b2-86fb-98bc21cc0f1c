"use client";
import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import "./GuestDetailsForm.scss";
import Image from "next/image";
import { useCommonContext } from "@/app/contexts/commonContext";
import { Country } from "@/common-models/common.model";

// Define validation interface
export interface GuestDetailsFormRef {
  validateForm: () => boolean;
  getFormData: () => any;
}

// Define validation errors interface
interface ValidationErrors {
  email?: string;
  mobile?: string;
  nationality?: string;
  passportNumber?: string;
  guestRooms?: Array<{
    firstName?: string;
    lastName?: string;
  }>;
}

const GuestDetailsForm = forwardRef<GuestDetailsFormRef>((props, ref) => {
  const { hotelSearchFormData } = useCommonContext();
  const [countries, setCountries] = useState<Country[]>([]);
  const [
    isNationalityCodeDropDownVisible,
    setIsNationalityCodeDropdownVisible,
  ] = useState<boolean>(false);
  const [
    isNationalitySelectionDropDownVisible,
    setIsNationalitySelectionDropdownVisible,
  ] = useState<boolean>(false);

  const [searchQuery, setSearchQuery] = useState<string>("91");
  const [nationalitySearchQuery, setNationalitySearchQuery] =
    useState<string>("");
  const [isInputSelectFocused, setIsInputSelectFocused] =
    useState<boolean>(false);

  // State for checkbox to control guest information visibility
  const [isBookingForSomeoneElse, setIsBookingForSomeoneElse] =
    useState<boolean>(false);

  // State for contact details title selection
  const [contactTitle, setContactTitle] = useState<string>("Mr.");

  // Initialize guest rooms data based on room count
  const [guestRooms, setGuestRooms] = useState<
    Array<{
      title: string;
      firstName: string;
      lastName: string;
    }>
  >([{ title: "Mr.", firstName: "", lastName: "" }]);

  // Form validation states
  const [email, setEmail] = useState<string>("");
  const [mobile, setMobile] = useState<string>("");
  const [passportNumber, setPassportNumber] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {}
  );
  const [showErrors, setShowErrors] = useState<boolean>(false);

  // Update guestRooms when hotelSearchFormData changes
  useEffect(() => {
    if (hotelSearchFormData && hotelSearchFormData.travelers) {
      const roomCount = hotelSearchFormData.travelers.rooms || 1;
      // Create a NEW array with the correct number of rooms
      const initialGuestRooms = Array(roomCount)
        .fill(null)
        .map(() => ({
          title: "Mr.",
          firstName: "",
          lastName: "",
        }));
      setGuestRooms(initialGuestRooms);
    }
  }, [hotelSearchFormData]);

  useEffect(() => {
    fetch("/assets/json/countries.json")
      .then((response) => response.json())
      .then((data) => setCountries(data))
      .catch((error) => console.error("Error fetching JSON:", error));
  }, []);

  const filteredCountries = countries?.filter(
    (nationality) =>
      nationality.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      nationality.phonecode
        .replace("+", "")
        .includes(searchQuery.replace("+", "").toLowerCase()) ||
      nationality.iso2.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredCountriesForNationalitySelection = countries?.filter(
    (nationality) =>
      nationality.name
        .toLowerCase()
        .includes(nationalitySearchQuery.toLowerCase()) ||
      nationality.iso2
        .toLowerCase()
        .includes(nationalitySearchQuery.toLowerCase())
  );

  const handleTitleChange = (index: number, value: string) => {
    const updatedGuestRooms = [...guestRooms];
    updatedGuestRooms[index] = { ...updatedGuestRooms[index], title: value };
    setGuestRooms(updatedGuestRooms);
  };

  const handleGuestInputChange = (
    index: number,
    field: string,
    value: string
  ) => {
    const updatedGuestRooms = [...guestRooms];
    updatedGuestRooms[index] = { ...updatedGuestRooms[index], [field]: value };
    setGuestRooms(updatedGuestRooms);

    // Clear specific guest room error when user types
    if (
      showErrors &&
      validationErrors.guestRooms?.[index]?.[
        field as keyof (typeof validationErrors.guestRooms)[0]
      ]
    ) {
      const updatedErrors = { ...validationErrors };
      if (updatedErrors.guestRooms && updatedErrors.guestRooms[index]) {
        delete updatedErrors.guestRooms[index][
          field as keyof (typeof updatedErrors.guestRooms)[0]
        ];

        // If no more errors for this room, remove the room from errors
        if (Object.keys(updatedErrors.guestRooms[index]).length === 0) {
          updatedErrors.guestRooms[index] = {};
        }

        // If no more guest room errors, remove guestRooms from errors
        if (
          updatedErrors.guestRooms.every(
            (roomError) => Object.keys(roomError).length === 0
          )
        ) {
          delete updatedErrors.guestRooms;
        }

        setValidationErrors(updatedErrors);
      }
    }
  };

  // Validation functions
  const validateEmail = (email: string): string | null => {
    if (!email.trim()) return "Email is required";
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return "Please enter a valid email address";
    return null;
  };

  const validateMobile = (mobile: string): string | null => {
    if (!mobile.trim()) return "Mobile number is required";
    const mobileRegex = /^[0-9]{10}$/;
    if (!mobileRegex.test(mobile.replace(/\s/g, "")))
      return "Please enter a valid 10-digit mobile number";
    return null;
  };

  const validatePassportNumber = (passportNumber: string): string | null => {
    if (!passportNumber.trim()) return "Passport number is required";
    if (passportNumber.length < 6)
      return "Passport number must be at least 6 characters";
    return null;
  };

  const validateGuestDetails = (): Array<{
    firstName?: string;
    lastName?: string;
  }> => {
    const guestErrors: Array<{ firstName?: string; lastName?: string }> = [];

    guestRooms.forEach((room, index) => {
      const roomErrors: { firstName?: string; lastName?: string } = {};

      if (!room.firstName.trim()) {
        roomErrors.firstName = "First name is required";
      }

      if (!room.lastName.trim()) {
        roomErrors.lastName = "Last name is required";
      }

      guestErrors[index] = roomErrors;
    });

    return guestErrors;
  };

  const validateForm = (): boolean => {
    const errors: ValidationErrors = {};

    // Validate email
    const emailError = validateEmail(email);
    if (emailError) errors.email = emailError;

    // Validate mobile
    const mobileError = validateMobile(mobile);
    if (mobileError) errors.mobile = mobileError;

    // Validate nationality (if not India, passport is required)
    if (
      nationalitySearchQuery &&
      countries.some(
        (country) =>
          (country.name === nationalitySearchQuery ||
            country.iso3 === nationalitySearchQuery) &&
          country.name !== "India" &&
          country.iso3 !== "IN"
      )
    ) {
      const passportError = validatePassportNumber(passportNumber);
      if (passportError) errors.passportNumber = passportError;
    }

    // Validate guest details if booking for someone else
    if (isBookingForSomeoneElse) {
      const guestErrors = validateGuestDetails();
      if (guestErrors.some((error) => error.firstName || error.lastName)) {
        errors.guestRooms = guestErrors;
      }
    }

    setValidationErrors(errors);
    setShowErrors(true);

    return Object.keys(errors).length === 0;
  };

  const getFormData = () => {
    return {
      contactTitle,
      email,
      mobile,
      nationality: nationalitySearchQuery,
      passportNumber,
      isBookingForSomeoneElse,
      guestRooms,
    };
  };

  // Clear specific field error when user types valid data
  const clearFieldError = (fieldName: keyof ValidationErrors) => {
    if (showErrors && validationErrors[fieldName]) {
      const updatedErrors = { ...validationErrors };
      delete updatedErrors[fieldName];
      setValidationErrors(updatedErrors);
    }
  };

  // Handle email change with real-time validation clearing
  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (showErrors && value.trim() && validateEmail(value) === null) {
      clearFieldError("email");
    }
  };

  // Handle mobile change with real-time validation clearing
  const handleMobileChange = (value: string) => {
    setMobile(value);
    if (showErrors && value.trim() && validateMobile(value) === null) {
      clearFieldError("mobile");
    }
  };

  // Handle passport change with real-time validation clearing
  const handlePassportChange = (value: string) => {
    setPassportNumber(value);
    if (showErrors && value.trim() && validatePassportNumber(value) === null) {
      clearFieldError("passportNumber");
    }
  };

  // Expose validation methods to parent component
  useImperativeHandle(ref, () => ({
    validateForm,
    getFormData,
  }));

  return (
    <div className="guest-details-form-container">
      {/* Contact Details Section */}
      <div className="guest-details-form">
        <div className="guest-details-form__header">Contact Details</div>
        <div className="radio-group">
          <label className="radio-button-label" htmlFor="contact-mr">
            <div className="radio-button">
              <input
                type="radio"
                id="contact-mr"
                name="contact-identity"
                value="Mr."
                checked={contactTitle === "Mr."}
                onChange={(e) => setContactTitle(e.target.value)}
              />
              <span className="radio"></span>
            </div>
            <span className="label">Mr.</span>
          </label>

          <label className="radio-button-label" htmlFor="contact-mrs">
            <div className="radio-button">
              <input
                type="radio"
                id="contact-mrs"
                name="contact-identity"
                value="Mrs."
                checked={contactTitle === "Mrs."}
                onChange={(e) => setContactTitle(e.target.value)}
              />
              <span className="radio"></span>
            </div>
            <span className="label">Mrs.</span>
          </label>

          <label className="radio-button-label" htmlFor="contact-miss">
            <div className="radio-button">
              <input
                type="radio"
                id="contact-miss"
                name="contact-identity"
                value="Miss."
                checked={contactTitle === "Miss."}
                onChange={(e) => setContactTitle(e.target.value)}
              />
              <span className="radio"></span>
            </div>
            <span className="label">Miss.</span>
          </label>
        </div>
        <div className="guest-details-form__input-field-container">
          <form>
            <div className="form-row">
              <div className="wf-50 flex flex-col">
                <div className="input-box ">
                  <div className="input-container">
                    <input
                      type="email"
                      id="Email"
                      placeholder=" "
                      required
                      value={email}
                      onChange={(e) => handleEmailChange(e.target.value)}
                    />
                    <label htmlFor="Email">Email Address</label>
                  </div>
                </div>
                {showErrors && validationErrors.email && (
                  <div className="error-message-outside wf-50">
                    {validationErrors.email}
                  </div>
                )}
              </div>

              <div className="wf-50 flex flex-col">
                <div className="country-code-input-box">
                  <div
                    className="country-code-input"
                    onClick={() => setIsNationalityCodeDropdownVisible(true)}
                  >
                    <input
                      type="text"
                      className="search-input-onButton"
                      placeholder=""
                      value={"+" + searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    {isNationalityCodeDropDownVisible ? (
                      <span className="material-icons">keyboard_arrow_up</span>
                    ) : (
                      <span className="material-icons">
                        keyboard_arrow_down
                      </span>
                    )}

                    {isNationalityCodeDropDownVisible && (
                      <div className="nationality-drop-down">
                        <div className="search-input-container">
                          <input
                            type="text"
                            className="search-input"
                            placeholder="Search by name or code"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                          />
                        </div>
                        {/* Check if results are empty */}
                        {filteredCountries?.length === 0 ? (
                          <div className="no-results">
                            <p>No results found</p>
                          </div>
                        ) : (
                          filteredCountries?.map((nationality, index) => (
                            <React.Fragment key={index}>
                              <div
                                className="nationality-drop-down__drop-down-item"
                                onClick={(e) => {
                                  setSearchQuery(nationality?.phonecode);
                                  e.stopPropagation();
                                  setIsNationalityCodeDropdownVisible(false); // Close dropdown after selection
                                }}
                              >
                                <div className="image">
                                  <Image
                                    src={`/assets/img/country-logo/${nationality.iso2.toLowerCase()}.webp`}
                                    alt="flag image"
                                    width={36}
                                    height={36}
                                    style={{
                                      objectFit: "cover",
                                      width: "100%",
                                      height: "100%",
                                    }}
                                  />
                                </div>
                                <div className="label-code">
                                  <span className="label">
                                    {nationality?.name}
                                  </span>
                                  <span className="code">
                                    {nationality?.phonecode}
                                  </span>
                                </div>
                              </div>
                              <hr className="border-bottom" />
                            </React.Fragment>
                          ))
                        )}
                      </div>
                    )}
                  </div>

                  {isNationalityCodeDropDownVisible && (
                    <div
                      className="nationality-drop-down-overlay"
                      onClick={() => setIsNationalityCodeDropdownVisible(false)}
                    ></div>
                  )}

                  <div className="input-box-phone">
                    <div className="input-container">
                      <input
                        type="text"
                        id="Mobile"
                        placeholder=" "
                        required
                        value={mobile}
                        onChange={(e) => handleMobileChange(e.target.value)}
                      />
                      <label htmlFor="Mobile">Mobile No</label>
                    </div>
                  </div>
                </div>

                {showErrors && validationErrors.mobile && (
                  <div className="error-message-outside wf-50">
                    {validationErrors.mobile}
                  </div>
                )}
              </div>
            </div>

            {/* Error messages row */}
            {/* <div className="form-row-errors">
          
            </div> */}
            <p className="hint-text">
              *Booking voucher will be sent to this email ID & whatsapp number
            </p>

            <div className="form-row">
              <div
                className={`input-box-select wf-50 ${
                  isInputSelectFocused ? "focused" : ""
                }`}
                onClick={() => {
                  setIsNationalitySelectionDropdownVisible(true);
                  setIsInputSelectFocused(true);
                }}
              >
                <div className="input-container">
                  <div className="input">
                    <input
                      type="text"
                      id="Nationality"
                      value={nationalitySearchQuery}
                      placeholder=" "
                      onChange={(e) =>
                        setNationalitySearchQuery(e.target.value)
                      }
                    />
                    <label htmlFor="Nationality">Nationality</label>
                    {isNationalitySelectionDropDownVisible ? (
                      <span className="material-icons">keyboard_arrow_up</span>
                    ) : (
                      <span className="material-icons">
                        keyboard_arrow_down
                      </span>
                    )}

                    {isNationalitySelectionDropDownVisible && (
                      <div className="nationality-drop-down">
                        {/* Check if results are empty */}
                        {filteredCountriesForNationalitySelection?.length ===
                        0 ? (
                          <div className="no-results">
                            <p>No results found</p>
                          </div>
                        ) : (
                          filteredCountriesForNationalitySelection?.map(
                            (nationality, index) => (
                              <React.Fragment key={index}>
                                <div
                                  className="nationality-drop-down__drop-down-item"
                                  onClick={(e) => {
                                    setNationalitySearchQuery(
                                      nationality?.name
                                    );
                                    e.stopPropagation();
                                    setIsNationalitySelectionDropdownVisible(
                                      false
                                    );
                                  }}
                                >
                                  <div className="image">
                                    <Image
                                      src={`/assets/img/country-logo/${nationality.iso2.toLowerCase()}.webp`}
                                      alt="flag image"
                                      width={36}
                                      height={36}
                                      style={{
                                        objectFit: "cover",
                                        width: "100%",
                                        height: "100%",
                                      }}
                                    />
                                  </div>
                                  <div className="label-code">
                                    <span className="label">
                                      {nationality?.name}
                                    </span>
                                  </div>
                                </div>
                                <hr className="border-bottom" />
                              </React.Fragment>
                            )
                          )
                        )}
                      </div>
                    )}
                  </div>

                  {isNationalitySelectionDropDownVisible && (
                    <div
                      className="nationality-drop-down-overlay"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsNationalitySelectionDropdownVisible(false);
                        setIsInputSelectFocused(false);
                      }}
                    ></div>
                  )}
                </div>
              </div>

              {nationalitySearchQuery &&
                countries.some(
                  (country) =>
                    (country.name === nationalitySearchQuery ||
                      country.iso3 === nationalitySearchQuery) &&
                    country.name !== "India" &&
                    country.iso3 !== "IN"
                ) && (
                  <div className="input-box wf-50">
                    <div className="input-container">
                      <input
                        type="text"
                        id="Passport Number"
                        placeholder=" "
                        required
                        value={passportNumber}
                        onChange={(e) => handlePassportChange(e.target.value)}
                      />
                      <label htmlFor="Passport Number">Passport Number</label>
                    </div>
                  </div>
                )}
            </div>

            {/* Error messages for nationality row */}
            {nationalitySearchQuery &&
              countries.some(
                (country) =>
                  (country.name === nationalitySearchQuery ||
                    country.iso3 === nationalitySearchQuery) &&
                  country.name !== "India" &&
                  country.iso3 !== "IN"
              ) && (
                <div className="form-row-errors">
                  <div className="error-message-placeholder wf-50"></div>
                  {showErrors && validationErrors.passportNumber && (
                    <div className="error-message-outside wf-50">
                      {validationErrors.passportNumber}
                    </div>
                  )}
                </div>
              )}
          </form>
        </div>
        <div className="checkbox-container">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={isBookingForSomeoneElse}
              onChange={(e) => setIsBookingForSomeoneElse(e.target.checked)}
            />
            <span className="hint-text">
              I am making this booking for someone else
            </span>
          </label>
        </div>
      </div>

      {/* Checkbox for booking for someone else */}

      {/* Guest Information Section - Only show when checkbox is checked */}
      {isBookingForSomeoneElse &&
        guestRooms.map((room, roomIndex) => (
          <div className="guest-details-form" key={`room-${roomIndex}`}>
            <div className="guest-details-form__header">
              Guest Details - Room {roomIndex + 1}
            </div>

            <div className="guest-details-form__input-field-container">
              <form>
                <div className="radio-group">
                  <label
                    className="radio-button-label"
                    htmlFor={`guest-mr-${roomIndex}`}
                  >
                    <div className="radio-button">
                      <input
                        type="radio"
                        id={`guest-mr-${roomIndex}`}
                        name={`identity-${roomIndex}`}
                        value="Mr."
                        checked={room.title === "Mr."}
                        onChange={() => handleTitleChange(roomIndex, "Mr.")}
                      />
                      <span className="radio"></span>
                    </div>
                    <span className="label">Mr.</span>
                  </label>

                  <label
                    className="radio-button-label"
                    htmlFor={`guest-mrs-${roomIndex}`}
                  >
                    <div className="radio-button">
                      <input
                        type="radio"
                        id={`guest-mrs-${roomIndex}`}
                        name={`identity-${roomIndex}`}
                        value="Mrs."
                        checked={room.title === "Mrs."}
                        onChange={() => handleTitleChange(roomIndex, "Mrs.")}
                      />
                      <span className="radio"></span>
                    </div>
                    <span className="label">Mrs.</span>
                  </label>

                  <label
                    className="radio-button-label"
                    htmlFor={`guest-miss-${roomIndex}`}
                  >
                    <div className="radio-button">
                      <input
                        type="radio"
                        id={`guest-miss-${roomIndex}`}
                        name={`identity-${roomIndex}`}
                        value="Miss."
                        checked={room.title === "Miss."}
                        onChange={() => handleTitleChange(roomIndex, "Miss.")}
                      />
                      <span className="radio"></span>
                    </div>
                    <span className="label">Miss.</span>
                  </label>
                </div>

                <div className="form-row">
                  <div className="wf-50 flex flex-col">
                    <div className="input-box">
                      <div className="input-container">
                        <input
                          type="text"
                          id={`FirstName-${roomIndex}`}
                          placeholder=" "
                          required
                          value={room.firstName}
                          onChange={(e) =>
                            handleGuestInputChange(
                              roomIndex,
                              "firstName",
                              e.target.value
                            )
                          }
                        />
                        <label htmlFor={`FirstName-${roomIndex}`}>
                          First Name
                        </label>
                      </div>
                    </div>
                    {showErrors &&
                      validationErrors.guestRooms?.[roomIndex]?.firstName && (
                        <div className="error-message-outside wf-50">
                          {validationErrors.guestRooms[roomIndex].firstName}
                        </div>
                      )}
                  </div>

                  <div className="wf-50 flex-flex-col">
                    <div className="input-box">
                      <div className="input-container">
                        <input
                          type="text"
                          id={`LastName-${roomIndex}`}
                          placeholder=" "
                          required
                          value={room.lastName}
                          onChange={(e) =>
                            handleGuestInputChange(
                              roomIndex,
                              "lastName",
                              e.target.value
                            )
                          }
                        />
                        <label htmlFor={`LastName-${roomIndex}`}>
                          Last Name
                        </label>
                      </div>
                    </div>
                    {showErrors &&
                      validationErrors.guestRooms?.[roomIndex]?.lastName && (
                        <div className="error-message-outside wf-50">
                          {validationErrors.guestRooms[roomIndex].lastName}
                        </div>
                      )}
                  </div>
                </div>

                {/* Error messages for guest names */}
                {/* <div className="form-row-errors">
              
                </div> */}
              </form>
            </div>
          </div>
        ))}
    </div>
  );
});

GuestDetailsForm.displayName = "GuestDetailsForm";

export default GuestDetailsForm;
