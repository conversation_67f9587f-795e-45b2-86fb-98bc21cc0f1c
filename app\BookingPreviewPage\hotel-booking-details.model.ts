export interface Root {
  data: Data
}

export interface Data {
  bookingDetail: BookingDetail
}

export interface BookingDetail {
  locationId: number
  hotelId: number
  name: string
  locality: string
  city: string
  address: string
  starRating: string
  thumbnailImageURL: string
  travellerDetails: TravellerDetails
  travelDetails: TravelDetails
  cancellationBenefits: CancellationBenefits
  roomBenefits: RoomBenefits
  metadata: Metadata
  freeCancellationDate: string
  bwzPaymentDueDate: string
  paymentOptions: PaymentOption[]
  providerId: number
  hotelInfoToken: string
  accommodationType: string
  rateChange: RateChange
  bestPriceGuarantee: boolean
  fareDetail: FareDetail2
  roomDetail: RoomDetail
}

export interface TravellerDetails {
  primaryGuestName: string | null
}

export interface TravelDetails {
  travelDates: TravelDates
  roomCount: number
  adultCount: number
  childCount: number
  childAges: number[]
  roomType: string
  nightCount: number
  stayHours: number | null
  durationOfStay: string
  nextDayHourlyCheckout: boolean
}

export interface TravelDates {
  checkinDate: string
  checkoutDate: string
  formattedCheckinDate: string
  formattedCheckoutDate: string
  epochCheckinDate: number
  epochCheckoutDate: number
  checkinTime: string
  checkoutTime: string
}

export interface CancellationBenefits {
  code: string
  data: string
  policy: Policy[]
  remarks: Remarks
}

export interface Policy {
  code: string
  title: string
  subTitle: string
  cancellationDate: string
  checkInDate: string
  startDate: string
  endDate: string
  cancellationAmount: number
}

export interface Remarks {
  title: string
  text: string | null
  subText: string
}

export interface RoomBenefits {
  mealBenefits: string[] | null
  otherBenefits: string[]
}

export interface Metadata {
  text: string
}

export interface PaymentOption {
  title: string
  subTitle: string
  note: string | null
  totalPGAmount: number
  paymentType: string
  subTitleParams: SubTitleParams
  saveAmountText: string | null
  enabled: boolean
  fareDetail: FareDetail
}

export interface SubTitleParams {
  "{{payableAmount}}"?: string
}

export interface FareDetail {
  displayedBaseFare: number
  baseFare: number
  markUpFare: number
  markupDiscountPercent: number | null
  taxesAndFees: number
  totalPrice: number
  burnMoneyInfo: BurnMoneyInfo
  couponCode: string
  cashback: number | null
  cashbackText: string | null
  instantDiscount: number
  totalDiscount: number
  totalPGAmount: number
  totalBookingAmount: number
  bankOfferTextIncluded: boolean
  offerText: OfferText
  offerMessage: string | null
  taxAndChargeMap: TaxAndChargeMap
  discountMap: DiscountMap
  excludedChargeMap: ExcludedChargeMap
  discountViewType: string
  method: string
  totalBaseFare: number
  totalTaxesAndFees: number
  convenienceFee: number
  totalConvenienceFee: number
  paymentType: string
  payAtHotelAmount: number | null
  offset: number
}

export interface BurnMoneyInfo {
  burnAmount: number
  burnIxiMoneyAmount: number
  burnIxiMoneyMaxAmount: number
  title: string
  text: string
}

export type OfferText = Record<string, unknown>

export interface TaxAndChargeMap {
  "Hotel Tax": number
  "Convenience Fee": number
}

export interface DiscountMap {
  "Reversal of Convenience fee": number
  "Coupon Discount": number
  "Supplier Discount": number
}

export type ExcludedChargeMap = Record<string, unknown>

export interface RateChange {
  text: string
  amount: number
}

export interface FareDetail2 {
  displayedBaseFare: number
  baseFare: number
  markUpFare: number
  markupDiscountPercent: number | null
  taxesAndFees: number
  totalPrice: number
  burnMoneyInfo: BurnMoneyInfo2
  couponCode: string
  cashback: number | null
  cashbackText: string | null
  instantDiscount: number
  totalDiscount: number
  totalPGAmount: number
  totalBookingAmount: number
  bankOfferTextIncluded: boolean
  offerText: OfferText2
  offerMessage: string | null
  taxAndChargeMap: TaxAndChargeMap2
  discountMap: DiscountMap2
  excludedChargeMap: ExcludedChargeMap2
  discountViewType: string
  method: string
  totalBaseFare: number
  totalTaxesAndFees: number
  convenienceFee: number
  totalConvenienceFee: number
  paymentType: string
  payAtHotelAmount: number | null
  offset: number
}

export interface BurnMoneyInfo2 {
  burnAmount: number
  burnIxiMoneyAmount: number
  burnIxiMoneyMaxAmount: number
  title: string
  text: string
}

export type OfferText2 = Record<string, unknown>

export interface TaxAndChargeMap2 {
  "Hotel Tax": number
  "Convenience Fee": number
}

export interface DiscountMap2 {
  "Reversal of Convenience fee": number
  "Coupon Discount": number
  "Supplier Discount": number
}

export type ExcludedChargeMap2 = Record<string, unknown>

export interface RoomDetail {
  roomImageUrl: string
  properties: Property[]
}

export interface Property {
  code: string
  data: string
}