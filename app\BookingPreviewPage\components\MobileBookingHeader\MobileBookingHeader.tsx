"use client";
import React from "react";
import "./MobileBookingHeader.scss";
import { useRouter } from "next/navigation";
import { TravelDetails } from "../../hotel-booking-details.model";

type MobileBookingHeaderProps = {
  name?: string;
  travelDetails?: TravelDetails;
};

function MobileBookingHeader({ name, travelDetails }: MobileBookingHeaderProps) {
  const router = useRouter();

  const handleBackClick = () => {
    router.back();
  };

  // Format dates for compact display
  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const checkinDate = formatDate(travelDetails?.travelDates?.formattedCheckinDate || "");
  const checkoutDate = formatDate(travelDetails?.travelDates?.formattedCheckoutDate || "");

  return (
    <div className="mobile-booking-header">
      <div className="mobile-booking-header__top">
        <button className="back-button" onClick={handleBackClick}>
          <i className="fa-solid fa-arrow-left"></i>
        </button>
        <h1 className="hotel-name">{name || "Hotel Name"}</h1>
      </div>

      <div className="mobile-booking-header__details">
        <div className="booking-info">
          <div className="info-item">
            <i className="fa-solid fa-door-open"></i>
            <span>{travelDetails?.roomCount || "0"} Room{(Number(travelDetails?.roomCount) || 0) !== 1 ? 's' : ''}</span>
          </div>
          <div className="info-item">
            <i className="fa-solid fa-user-group"></i>
            <span>{(Number(travelDetails?.adultCount) || 0) + (Number(travelDetails?.childCount) || 0)} Guests</span>
          </div>
        </div>

        <div className="dates-info">
          <span className="date-range">{checkinDate} - {checkoutDate}</span>
          <span className="nights-count">{travelDetails?.nightCount || 0}N</span>
        </div>
      </div>
    </div>
  );
}

export default MobileBookingHeader;
