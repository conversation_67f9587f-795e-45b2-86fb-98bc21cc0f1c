@use "sass:color";
@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.locationInput-container {
  width: 100%;
  min-width: 380px;
  min-height: 50px;
  display: flex;
  align-items: center;
  padding: 8px 25px;
  cursor: pointer;
  position: relative;
  background-color: #F4F5F5;
  border-radius: 6px;

  .locationInput {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .icon-label-inputField {
      display: flex;
      flex-direction: row;
      gap: 14px;
      align-items: center;

      .icon {
        .fa-location-dot {
          font-size: 18px;
          color: #555;
        }
      }

      .label-inputField {
        display: flex;
        flex-direction: column;
        min-width: 190px;
        

        .label {
          font-weight: 700;
          font-size: 12px;
          
        }

        .inputField {
          width: 100%;
       
         
          input{
            background-color: #F4F5F5;
            border: none;
            width: 100%;
            outline: none;
            font-size: 14px;
            color: black;
            &::placeholder{
                font-size: 14px;
                color: black;
                
              
            }
          }
        }
      }
    }

    .icon {
     
      .fa-map-location-dot {
        font-size: 18px;
        color: #555;
      }
    }

    .closeIcon {
      padding-right: 15px;
    }
  }

  .location-selector {
    position: absolute;
    top: 60px;
    left: 0;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    z-index: z-index(modal);
    min-width: 430px;
    // padding: 10px 15px;
    cursor: default;
    max-height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 8px; // Width of the scrollbar
      height: 8px; // Height for horizontal scrollbar (if any)
    }

    &::-webkit-scrollbar-track {
      background: #f0f0f0; // Background color of the scrollbar track
      border-radius: 10px; // Rounded corners for the track
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(45deg, #616161, #9a9a9a); // Gradient thumb
      border-radius: 10px; // Rounded corners for the thumb
      border: 2px solid #f0f0f0; // Adds padding between track and thumb
    }

    &::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(
        45deg,
        #1e1e1e,
        #616161
      ); // Thumb color on hover
    }

    h5{
        font-size: 15px;
        font-weight: 700;
        padding: 10px 12px;
    }

    .dropdown{
        display: flex;
        flex-direction: column;

        .dropdown-item{
            display: flex;
            flex-direction: row;
            gap: 14px;
            align-items: center;
            padding: 8px 15px;
            border-bottom: 1px solid #d6d6d6;
            transition: background-color 0.2s ease;
    
            .icon{
                font-size: 22px;
                color: #555;
            }
    
            .location{
                display: flex;
                flex-direction: column;
    
                .place{
                    font-size: 14px;
                    font-weight: 800;
                }
    
                .country{
                    font-size: 12px;
                    font-weight: 500;
                    font-size: #555;
                }
            }

            &:hover{
                background-color: color.adjust(#ffffff , $lightness:-5%) ;

            }
        }

        .dropdown-item:last-child{
            border-bottom: none;
        }

    }

    
  }

  .locationSelectorOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    //background-color: rgba(0, 0, 0, 0.3);
    z-index: z-index(overlay);
    background: transparent;
    cursor: default;
  }
}
