@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

// Body scroll management
body.popup-open {
  overflow: hidden;
}

// Main container
.bottom-to-top-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: z-index(modal);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease;

  &.visible {
    opacity: 1;
    pointer-events: auto;
  }

  &.show {
    opacity: 1;
    pointer-events: auto;
  }

  &.hide {
    opacity: 0;
    pointer-events: none;
  }
}

// Overlay
.dy-bootom-up-popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

// Main popup div
.dy-bootom-up-popup-div {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 98vh; // Limit height to 98% of viewport, leaving 2% gap at top
  background: white;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), height 0.3s ease;

  .bottom-to-top-popup-container.show & {
    transform: translateY(0);
  }

  .bottom-to-top-popup-container.hide & {
    transform: translateY(100%);
  }

  &.dragging {
    transition: none;
  }

  // Type-specific styling
  &.search {
    border-radius: 20px 20px 0 0;

    .header-menu-div {
      background: var(--primary-color); // Use solid primary color instead of gradient
      color: white;

      .popup-heading {
        color: white;
      }
    }
  }

  &.calendar {
    .header-menu-div {
      background: var(--primary-color);
      color: white;
      border-bottom: 2px solid var(--primary-color);
    }
  }

  &.default {
    .header-menu-div {
      background: var(--primary-color);
      color: white;
    }
  }
}

// Draggable header
.header-menu-div {
  flex-shrink: 0;
  padding: 16px 20px;
  background: var(--primary-color); // Use primary color for consistency
  color: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: grab;
  user-select: none;
  position: relative;

  &:active {
    cursor: grabbing;
  }

  // Drag indicator
  .drag-indicator {
    width: 40px;
    height: 4px;
    background: var(--border-color2);
    border-radius: 2px;
    transition: background 0.2s ease;
  }

  &:hover .drag-indicator {
    background: var(--black-color2);
  }

  // Popup heading
  .popup-heading {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white; // White text on primary background
    text-align: center;
  }
}

// Content body
.body-div {
  flex: 1;
  overflow: hidden; // Remove scroll from popup body - let child components handle scrolling
  // padding: 20px;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Responsive design
@media (max-width: $isMobile) {
  .dy-bootom-up-popup-div {
    border-radius: 12px 12px 0 0;
    
    &.search {
      border-radius: 16px 16px 0 0;
    }
  }

  .header-menu-div {
    padding: 12px 16px;
    
    .popup-heading {
      font-size: 16px;
    }
  }

  .body-div {
    // padding: 16px;
  }
}

@media (max-width: $breakpoint-sm) {
  .dy-bootom-up-popup-div {
    border-radius: 8px 8px 0 0;
  }

  .header-menu-div {
    padding: 10px 14px;
    
    .drag-indicator {
      width: 32px;
      height: 3px;
    }
    
    .popup-heading {
      font-size: 15px;
    }
  }

  .body-div {
    // padding: 14px;
  }
}

// Focus and accessibility
.header-menu-div:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// Animation keyframes
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .dy-bootom-up-popup-div {
    border: 2px solid var(--black-color);
  }

  .header-menu-div {
    border-bottom: 2px solid var(--black-color);
  }

  .drag-indicator {
    background: var(--black-color);
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .dy-bootom-up-popup-div {
    transition: none;
  }

  .bottom-to-top-popup-container {
    transition: none;
  }
}
