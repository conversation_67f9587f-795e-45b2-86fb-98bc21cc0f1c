import { AutoSuggestionRes } from "@/models/hotel/search-page.model"
import apiService from "../api-service"


export const getAutoSuggest = async (term:string) : Promise<AutoSuggestionRes> => {
    return apiService.get<AutoSuggestionRes>(`autosuggest?term=${term}`)
}

export const getLocationDetails = async (locationId: string) : Promise<any> => {
    return apiService.get<any>(`location?location_id=${locationId}`)
}

export const getCalendarPrice = async (body:any) : Promise<any> => {
    return apiService.post('getCalendar',body)
}

