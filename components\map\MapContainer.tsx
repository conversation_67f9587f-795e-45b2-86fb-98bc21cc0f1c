'use client';

import React, { useState } from 'react';
import MapWrapper from './MapWrapper';
import { MapMarker } from './Map';
import { useLanguage } from '@/app/contexts/languageContext';
import { useTranslation } from '@/app/hooks/useTranslation';
import FullScreenMap from './FullScreenMap';
import './MapContainer.scss';

export interface MapLocation {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface HotelData {
  id: string;
  name: string;
  location: MapLocation;
  rating?: number;
  price?: string;
  image?: string;
}

export interface PointOfInterest {
  name: string;
  location: MapLocation;
  type?: string;
  description?: string;
}

export interface MapContainerProps {
  // Map configuration
  center?: MapLocation;
  zoom?: number;
  height?: string;
  width?: string;

  // Content
  hotels?: HotelData[];
  pointsOfInterest?: PointOfInterest[];

  // UI options
  showHeader?: boolean;
  title?: string;
  showGoogleMapsLink?: boolean;
  googleMapsLinkText?: string;

  // Callbacks
  onHotelClick?: (hotel: HotelData) => void;
  onPoiClick?: (poi: PointOfInterest) => void;

  // Full-screen map options
  enableFullScreenHotelSearch?: boolean;
  filterData?: any; // Added back for passing to FullScreenMap
  actualHotels?: any[]; // Using any[] for flexibility, ideally should be a specific type
}

const MapContainer: React.FC<MapContainerProps> = ({
  // Default values for props
  center,
  zoom = 14,
  height = '400px',
  width = '100%',
  hotels = [],
  pointsOfInterest = [],
  showHeader = true,
  title,
  showGoogleMapsLink = true,
  googleMapsLinkText,
  onHotelClick,
  onPoiClick, // Kept for future use
  enableFullScreenHotelSearch = false,
  filterData,
  actualHotels
}) => {
  const { isRTL } = useLanguage();
  const { t } = useTranslation();

  // State to control full-screen map visibility
  const [isFullScreenMapOpen, setIsFullScreenMapOpen] = useState(false);



  // Convert hotels to a format that can be used by the FullScreenHotelSearchMap
  // This is a partial implementation of the Hotel type with just the required fields
  const hotelSearchData = hotels.map(hotel => ({
    hotelId: parseInt(hotel.id) || 0,
    name: hotel.name,
    locality: '',
    city: '',
    userRating: '',
    userRatingCategory: '',
    starRating: hotel.rating || 0,
    userRatingCount: 0,
    geoLocationInfo: {
      latitude: hotel.location.latitude,
      longitude: hotel.location.longitude,
      lat: hotel.location.latitude,
      lon: hotel.location.longitude
    },
    fareDetail: {
      totalPrice: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '0'),
      displayedBaseFare: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '0')
    },
    imageInfoList: hotel.image ? [{
      url: hotel.image,
      pictureId: '1',
      caption: hotel.name,
      imageCategory: 'hotel',
      rank: 1
    }] : [],
    locationId: 0,
    roomDetails: [],
    comfortRating: 0,
    bookingDetails: [],
    taxesAndCharges: 0,
    accommodationType: '',
    topOfferings: [],
    roomsCountLeft: 0,
    distanceFromSearchedEntity: '',
    fomoTags: [],
    amenities: [],
    isVisible: true
  }));

  // Toggle full-screen map
  const toggleFullScreenMap = () => {
    setIsFullScreenMapOpen(!isFullScreenMapOpen);
  };

  // Determine map center
  const mapCenter: [number, number] = center
    ? [center.latitude, center.longitude]
    : hotels.length > 0
      ? [hotels[0].location.latitude, hotels[0].location.longitude]
      : pointsOfInterest.length > 0
        ? [pointsOfInterest[0].location.latitude, pointsOfInterest[0].location.longitude]
        : [51.505, -0.09]; // Default center (London)

  // Create markers for hotels
  const hotelMarkers: MapMarker[] = hotels.map(hotel => ({
    position: [hotel.location.latitude, hotel.location.longitude],
    iconType: 'hotel',
    details: {
      name: hotel.name,
      rating: hotel.rating,
      price: hotel.price
    }
  }));

  // Create markers for points of interest
  const poiMarkers: MapMarker[] = pointsOfInterest.map(poi => ({
    position: [poi.location.latitude, poi.location.longitude],
    popup: poi.name,
    iconType: 'poi'
  }));

  // Combine all markers
  const allMarkers = [...hotelMarkers, ...poiMarkers];

  // Open Google Maps with the center location
  const openGoogleMaps = () => {
    const latitude = center?.latitude || (hotels.length > 0 ? hotels[0].location.latitude : mapCenter[0]);
    const longitude = center?.longitude || (hotels.length > 0 ? hotels[0].location.longitude : mapCenter[1]);
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    window.open(googleMapsUrl, '_blank');
  };

  // Get address to display
  const displayAddress = center?.address ||
    (hotels.length > 0 && hotels[0].location.address) ||
    '';

  return (
    <div className="map-container">
      {showHeader && (
        <div className="map-container__header">
          <h6>{title}</h6>

          {displayAddress && (
            <div className="location-details">
              <div className="location">
                <p>
                  <i className={`fa-solid fa-location-dot ${isRTL ? 'ml-1' : 'mr-1'}`}></i> {displayAddress}
                </p>
              </div>

              {showGoogleMapsLink && (
                <div className="view-map-btn" onClick={openGoogleMaps}>
                  {googleMapsLinkText || t('View On Google Maps')}
                </div>
              )}
            </div>
          )}
        </div>
      )}

      <div className="map-container__map">
        <button
          className="maximize-btn"
          onClick={toggleFullScreenMap}
          aria-label="Maximize map"
        >
          <i className="fa-solid fa-expand"></i>
        </button>
        <MapWrapper
          center={mapCenter}
          zoom={zoom}
          markers={allMarkers}
          style={{ height, width, borderRadius: '8px' }}
          autoCenter={true}
          fitBounds={allMarkers.length > 1}
        />
      </div>

      {/* Full-screen map component */}
      <FullScreenMap
        isOpen={isFullScreenMapOpen}
        onClose={() => setIsFullScreenMapOpen(false)}
        center={mapCenter}
        zoom={zoom}
        markers={allMarkers}
        title={title}
        hotels={actualHotels || hotelSearchData}
        destination={center ? {
          name: center.address || '',
          latitude: center.latitude,
          longitude: center.longitude
        } : undefined}
        isHotelSearchMap={enableFullScreenHotelSearch && hotels.length > 0}
        filterData={filterData}
        onHotelSelect={(hotelId) => {
          const hotel = hotels.find(h => parseInt(h.id) === hotelId);
          if (hotel && onHotelClick) {
            onHotelClick(hotel);
          }
        }}
      />
    </div>
  );
};

export default MapContainer;
