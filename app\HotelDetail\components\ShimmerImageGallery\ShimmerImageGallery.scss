@use "/styles/variable" as *;
@use "sass:color";

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

$shimmer-bg: linear-gradient(90deg, #e0e0e0 25%, #d6d6d6 50%, #e0e0e0 75%);
$shimmer-color: #d4d4d4;

.hotel-overview-container {
  width: 100%;
  margin-bottom: 20px;
  padding: 5px 0 0 0;
  display: flex;
  flex-direction: row;

  &__left-section {
    width: 75%;
    padding: 6px 8px 0;

    .left-section-row1 {
      width: 100%;
      display: flex;
      flex-direction: row;
      column-gap: 8px;
      flex-wrap: nowrap;
      margin-bottom: 10px;

      @media (max-width: $breakpoint-lg) {
        flex-wrap: wrap;
      }

      &__flex-col {
        width: 100%;
      }

      .col1 {
        width: 65%;
        height: 400px;
        border-radius: 6px;
        position: relative;
        overflow: hidden;

        .shimmer-effect {
          width: 100%;
          height: 100%;
          border-radius: 6px;
          background: $shimmer-bg;
          background-size: 400px 100%;
          animation: shimmer 1s infinite linear;
        }
      }

      .col2 {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: 20px;
        position: relative;
        overflow: hidden;
        max-width: 35%;

        .sub-col {
          height: 190px;
          border-radius: 6px;
          position: relative;
          overflow: hidden;

          .shimmer-effect {
            width: 100%;
            height: 100%;
            border-radius: 6px;
            background: $shimmer-bg;
            background-size: 400px 100%;
            animation: shimmer 1s infinite linear;
          }
        }
      }
    }

    .left-section-row2 {
      display: flex;
      gap: 8px;
      position: relative;

      &__col {
        flex: 1;
        height: 103px;
        border-radius: 6px;
        position: relative;
        overflow: hidden;

        .shimmer-effect {
          width: 100%;
          height: 100%;
          border-radius: 6px;
          background: $shimmer-bg;
          background-size: 400px 100%;
          animation: shimmer 1s infinite linear;
        }

        .image-overlay-shimmer {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.4);
          border-radius: 6px;
        }
      }

      .morePhotos-shimmer {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 2px 5px;
        background-color: #2a2b30;
        position: absolute;
        bottom: 10px;
        right: 10px;
        border-radius: 12px;
        gap: 5px;

        .shimmer-icon {
          width: 14px;
          height: 14px;
          background: $shimmer-bg;
          background-size: 400px 100%;
          animation: shimmer 1s infinite linear;
          border-radius: 2px;
        }

        .shimmer-text {
          width: 50px;
          height: 13px;
          background: $shimmer-bg;
          background-size: 400px 100%;
          animation: shimmer 1s infinite linear;
          border-radius: 2px;
        }
      }
    }
  }

  &__right-section {
    padding: 6px 8px 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 513px; /* Match the height of the left section (400px + 103px + 10px gap) */

    .reviewFloater {
      border: 1px solid #e4e4e4;
      margin-bottom: 16px;

      .hp-gallery-score-card {
        padding: 12px;
        display: flex;
        justify-content: end;
        border-bottom: 2px solid #e4e4e4;

        .review {
          display: flex;
          flex-direction: row;

          .rating-shimmer {
            width: 40px;
            height: 32px;
            background: $shimmer-bg;
            background-size: 400px 100%;
            animation: shimmer 1s infinite linear;
            border-radius: 6px;
          }

          .rating-detail {
            text-align: right;
            margin: 0 10px 0 0;
            display: flex;
            flex-direction: column;
            gap: 2px;

            .detail-shimmer {
              background: $shimmer-bg;
              background-size: 400px 100%;
              animation: shimmer 1s infinite linear;
              border-radius: 3px;
            }

            .detail1 {
              width: 60px;
              height: 16px;
            }

            .detail2 {
              width: 80px;
              height: 12px;
            }
          }
        }
      }

      .best-review-score-card {
        padding: 12px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        &__label-shimmer {
          width: 80px;
          height: 14px;
          background: $shimmer-bg;
          background-size: 400px 100%;
          animation: shimmer 1s infinite linear;
          border-radius: 3px;
        }

        &__count-shimmer {
          width: 30px;
          height: 24px;
          background: $shimmer-bg;
          background-size: 400px 100%;
          animation: shimmer 1s infinite linear;
          border-radius: 6px;
        }
      }
    }

    .hotel-search-map-container-shimmer {
      flex: 1;
      height: 100%;
      border: 1px solid #e4e4e4;
      border-radius: 6px;
      overflow: hidden;

      .map-shimmer {
        width: 100%;
        height: 100%;
        background: $shimmer-bg;
        background-size: 400px 100%;
        animation: shimmer 1s infinite linear;
      }
    }
  }
}
