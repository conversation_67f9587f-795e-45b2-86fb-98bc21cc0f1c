@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as *;

.guest-details-form-container {
  .guest-details-form {
    width: 100%;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 15px;
    margin-bottom: 20px;

    @media (max-width: $breakpoint-md) {
      border-radius: 0%;
    }

      .radio-group {
          width: 100%;
          display: flex;
          flex-direction: row;
          column-gap: 30px;

          .radio-button-label {
            display: flex;
            flex-direction: row;
            align-items: center;
            cursor: pointer; // Make entire label clickable

            .radio-button {
              position: relative;
              display: flex;
              align-items: center;
              pointer-events: auto; // Ensure clicks are allowed

              input {
                opacity: 0;
                position: absolute;
                width: 20px;
                height: 20px;
                cursor: pointer;
                //z-index: 10;
              }

              input:hover + .radio {
                background-color: color.adjust(#ffffff, $lightness: -4%);
              }

              input:checked + .radio {
                animation: pulse 0.3s ease-in-out;
              }

              @keyframes pulse {
                0% {
                  transform: scale(1);
                }
                50% {
                  transform: scale(1.2);
                }
                100% {
                  transform: scale(1);
                }
              }

              .radio {
                width: 18px;
                height: 18px;
                border: 1px solid rgba(0, 0, 0, 0.6);
                border-radius: 50%;
                display: inline-block;
                //z-index: 5;
                position: relative;
                transition: border-color 0.2s ease-in-out, background-color 0.2s ease;

                &::after {
                  content: "";
                  width: 10px;
                  height: 10px;
                  background-color: transparent;
                  border-radius: 50%;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  transition: background-color 0.2s ease-in-out;
                }
              }

              input:checked + .radio {
                border-color: $primary-color;

                &::after {
                  background-color: $primary-color;
                }
              }
            }

            .label {
              font-size: 16px;
              font-weight: 500;
              color: $booking_black;
              margin-left: 5px;
            }
          }
        }

    &__header {
      font-size: 20px;
      color: $booking_black;
      margin-bottom: 15px;
      font-weight: 700;
    }

    &__input-field-container {
      width: 100%;

      form {
        display: flex;
        flex-direction: column;
        //row-gap: 15px;

        .hint-text{
          font-size: 13px;
          color: #6B7280;
          font-weight: 500;
          padding-bottom: 15px;
          padding-left: 5px;
        }

        .radio-group {
          width: 100%;
          display: flex;
          flex-direction: row;
          column-gap: 30px;

          .radio-button-label {
            display: flex;
            flex-direction: row;
            align-items: center;
            cursor: pointer; // Make entire label clickable
            user-select: none; // Prevent text selection

            .radio-button {
              position: relative;
              display: flex;
              align-items: center;
              pointer-events: auto; // Ensure clicks are allowed

              input {
                opacity: 0;
                position: absolute;
                width: 20px;
                height: 20px;
                cursor: pointer;
                //z-index: 10;
              }

              input:hover + .radio {
                background-color: color.adjust(#ffffff, $lightness: -4%);
              }

              input:checked + .radio {
                animation: pulse 0.3s ease-in-out;
              }

              @keyframes pulse {
                0% {
                  transform: scale(1);
                }
                50% {
                  transform: scale(1.2);
                }
                100% {
                  transform: scale(1);
                }
              }

              .radio {
                width: 18px;
                height: 18px;
                border: 1px solid rgba(0, 0, 0, 0.6);
                border-radius: 50%;
                display: inline-block;
                //z-index: 5;
                position: relative;
                transition: border-color 0.2s ease-in-out, background-color 0.2s ease;

                &::after {
                  content: "";
                  width: 10px;
                  height: 10px;
                  background-color: transparent;
                  border-radius: 50%;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  transition: background-color 0.2s ease-in-out;
                }
              }

              input:checked + .radio {
                border-color: $primary-color;

                &::after {
                  background-color: $primary-color;
                }
              }
            }

            .label {
              font-size: 16px;
              font-weight: 500;
              color: $booking_black;
              margin-left: 5px;
            }
          }
        }

        .nationality-drop-down {
          padding: 10px 0;
          width: 360px;
          max-height: 250px;
          overflow-y: auto;
          height: auto;
          position: absolute;
          top: 53px;
          left: 0;
          z-index: z-index(dropdown);
          background-color: #ffffff;
          border-radius: 5px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

          .search-input-container {
            padding: 0 20px;
            .search-input {
              width: 100%;
              padding: 8px 12px;
              border-radius: 4px;
              margin-bottom: 8px;
              outline: none;
              border: 1px solid #ccc;
            }
          }

          .no-results {
            padding: 12px;
            text-align: center;
            color: #888;
            font-size: 14px;
          }

          &__drop-down-item {
            padding: 10px 20px;
            display: flex;
            flex-direction: row;
            gap: 12px;
            align-items: center;

            .image {
              width: 36px;
              height: 36px;
              border-radius: 50%;
              background-color: rgba(0, 0, 0, 0.2);
              position: relative;
              overflow: hidden;
              display: flex;
              align-items: center;
              justify-content: center;  
            }

            .label-code {
              flex: 1;
              display: flex;
              justify-content: space-between;

              .label {
                color: $booking_black;
                font-size: 16px;
                font-weight: 500;
              }

              .code {
                color: #5e616e;
                font-size: 12px;
              }
            }
          }
          .border-bottom {
            margin: 0 20px;
          }
        }

        .nationality-drop-down-overlay {
          position: fixed;
          inset: 0;
          background: transparent;
          z-index: z-index(dropdown)-1;
        }

        .form-row {
          width: 100%;
          display: flex;
          flex-direction: row;
          column-gap: 10px;

          @media (max-width: $breakpoint-sm) {
            flex-direction: column;
            
          }


          .wf-50 {
            width: 50%;

            @media (max-width: $breakpoint-sm) {
              width: 100%;
              
            }
          }

          .country-code-input-box {
            display: flex;
            flex-direction: row;
            align-items: center;
            column-gap: 10px;

            .country-code-input {
              padding: 12px 10px;
              outline: 1px solid rgba(0, 0, 0, 0.5);
              border-radius: 8px;
              cursor: pointer;
              position: relative;
              user-select: none;
              width: 70px;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;

              .search-input-onButton {
                width: 100%;
                outline: none;
                border: none;
                background: transparent;
              }
            }

            .input-box-phone {
              margin: 8px 0 10px 0;
              padding: 0 15px;
              outline: 1px solid rgba(0, 0, 0, 0.5);
              border-radius: 10px;
              box-sizing: border-box;
              flex: 1;

              &:hover {
                outline: 2px solid rgba(0, 0, 0, 0.5);
                outline-offset: 1px;
              }

              &:focus-within {
                outline: 2px solid $primary-color;
                outline-offset: 1px;
              }

              .input-container {
                position: relative;
                width: 100%;
                max-width: 400px;
                flex: 1;
              }
              
              .input-container input {
                width: 100%;
                padding: 12px 0;
                outline: none;
                font-size: 16px;
                background-color: transparent;
                border: none;
              }

              .input-container input::placeholder {
                color: transparent;
              }

              .input-container label {
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                font-size: 16px;
                color: rgba(0, 0, 0, 0.7);
                background: #fff;
                padding: 0 5px;
                transition: 0.3s ease;
                pointer-events: none;
              }

              .input-container input:focus + label,
              .input-container input:not(:placeholder-shown) + label {
                top: 0;
                left: 0;
                font-size: 12px;
                font-weight: 500;
                color: $primary-color;
              }
            }
          }

          .input-box {
            margin: 8px 0 10px 0;
            padding: 0 15px;
            outline: 1px solid rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            box-sizing: border-box;

            &:hover {
              outline: 2px solid rgba(0, 0, 0, 0.5);
              outline-offset: 1px;
            }

            &:focus-within {
              outline: 2px solid $primary-color;
              outline-offset: 1px;
            }

            &:has(.input-container input:not(:placeholder-shown)) {
              outline: 2px solid $primary-color;
              outline-offset: 1px;
            }

            .input-container {
              position: relative;
              width: 100%;
              max-width: 400px;
            }

            .input-container input {
              width: 100%;
              padding: 12px 0;
              outline: none;
              border: none;
              font-size: 16px;
              background-color: transparent;
            }

            .input-container input::placeholder {
              color: transparent;
            }

            .input-container label {
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              font-size: 16px;
              color: rgba(0, 0, 0, 0.7);
              background: #fff;
              padding: 0 5px;
              transition: 0.3s ease;
              pointer-events: none;
            }

            .input-container input:focus + label,
            .input-container input:not(:placeholder-shown) + label {
              top: 0;
              left: 0;
              font-size: 12px;
              font-weight: 500;
              color: $primary-color;
            }
          }

          .input-box-select {
            margin: 8px 0 10px 0;
            padding: 0 15px;
            outline: 1px solid rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            box-sizing: border-box;
            transition: outline 0.2s ease, outline-offset 0.2s ease;
            position: relative;

            &.focused {
              outline: 2px solid $primary-color;
              outline-offset: 1px;
            }

            &:not(.focused):hover {
              outline: 2px solid rgba(0, 0, 0, 0.5);
              outline-offset: 1px;
            }

            .input-container {
              position: relative;
              width: 100%;
              max-width: 400px;

              @media (max-width: $breakpoint-sm) {
                max-width: 100%;
                
              }
            }

            .input-container .input {
              width: 100%;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              padding: 12px 0;
              font-size: 16px;

              input {
                width: 100%;
                outline: none;
                border: none;
                font-size: 16px;
                background-color: transparent;
                //z-index: 4;
              }
            }

            .input-container label {
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              font-size: 16px;
              color: rgba(0, 0, 0, 0.7);
              background: #fff;
              padding: 0 5px;
              transition: 0.3s ease;
              pointer-events: none;
              //z-index: 5;
              z-index: z-index(base);
            }

            .input-container input:focus + label,
            .input-container input:not(:placeholder-shown) + label {
              top: 0;
              left: 0;
              font-size: 12px;
              font-weight: 500;
              color: $primary-color;
            }
          }
        }
      }
    }
  }
}


// Hint text styling
.hint-text {
  font-size: 14px;
  color: #666;
  margin: 8px 0;
  font-style: italic;
  line-height: 1.4;
  
  // For the booking voucher hint
  &:first-of-type {
    margin-top: 12px;
    margin-bottom: 16px;
  }
}

// Checkbox container styling
.checkbox-container {
  margin: 20px 0;
  padding: 0;
  
  .checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    gap: 10px;
    
    input[type="checkbox"] {
      margin: 0;
      margin-top: 2px; // Align with first line of text
      width: 16px;
      height: 16px;
      cursor: pointer;
      accent-color: #007bff; // Modern checkbox color
      
      &:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
      }
    }
    
    .hint-text {
      margin: 0;
      cursor: pointer;
      user-select: none;
      font-style: normal;
      font-weight: 500;
      color: #333;
      
      &:hover {
        color: #007bff;
      }
    }
  }
}

// Error message styles
.error-message {
  color: #e53935;
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
  padding-left: 4px;
  display: flex;
  align-items: center;

  &::before {
    content: "⚠";
    margin-right: 4px;
    font-size: 10px;
  }
}

// Error message styles for outside placement
.error-message-outside {
  color: #e53935;
  font-size: 12px;
  font-weight: 500;
  padding-left: 4px;
  display: flex;
  align-items: center;

  &::before {
    content: "⚠";
    margin-right: 4px;
    font-size: 10px;
  }

  // When used in form-row-errors, maintain width
  &.wf-50 {
    width: 50%;
    box-sizing: border-box;
    padding-right: 10px;
  }

  // Responsive adjustments
  @media (max-width: $breakpoint-sm) {
    font-size: 11px;

    &.wf-50 {
      width: 100%;
      margin-bottom: 8px;
    }
  }
}

// Error row container
.form-row-errors {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-top: 8px;
  margin-bottom: 12px;

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    gap: 0;
    margin-top: 6px;
    margin-bottom: 10px;
  }
}

// Placeholder for error alignment
.error-message-placeholder {
  width: 50%;

  @media (max-width: $breakpoint-sm) {
    display: none;
  }
}

// Note: Removed red border styling for inputs as per user request
// Only error messages will be shown, input borders remain unchanged