'use client';

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import './MobileAppDownloadPopup.scss';

interface MobileAppDownloadPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileAppDownloadPopup: React.FC<MobileAppDownloadPopupProps> = ({ isOpen, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Small delay for smooth animation
      setTimeout(() => setIsVisible(true), 100);
    } else {
      setIsVisible(false);
    }
  }, [isOpen]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleDontShowAgain = () => {
    // Store in localStorage to not show again for 30 days
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 30);
    localStorage.setItem('hideAppDownloadPopup', expiryDate.toISOString());
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`app-download-popup-overlay ${isVisible ? 'visible' : ''}`}
      onClick={handleBackdropClick}
    >
      <div className={`app-download-popup ${isVisible ? 'visible' : ''}`}>
        {/* Close Button */}
        <button 
          className="popup-close-btn"
          onClick={onClose}
          aria-label="Close popup"
        >
          <X size={20} />
        </button>

        {/* App Icon */}
        <div className="app-icon">
          <div className="icon-container">
            <i className="fa-solid fa-hotel"></i>
          </div>
        </div>

        {/* Content */}
        <div className="popup-content">
          <h3 className="popup-title">Get the Kindali App</h3>
          <p className="popup-description">
            Book hotels faster with exclusive mobile deals and instant confirmations.
          </p>

          {/* Features */}
          <div className="features-list">
            <div className="feature-item">
              <i className="fa-solid fa-tag"></i>
              <span>Exclusive mobile deals</span>
            </div>
            <div className="feature-item">
              <i className="fa-solid fa-bolt"></i>
              <span>Instant booking</span>
            </div>
            <div className="feature-item">
              <i className="fa-solid fa-download"></i>
              <span>Offline access</span>
            </div>
          </div>

          {/* Download Buttons */}
          <div className="download-buttons">
            <a
              href="#"
              className="download-btn app-store"
              onClick={(e) => e.preventDefault()}
            >
              <i className="fa-brands fa-apple"></i>
              <div className="btn-text">
                <span className="small-text">Download on the</span>
                <span className="main-text">App Store</span>
              </div>
            </a>

            <a
              href="#"
              className="download-btn google-play"
              onClick={(e) => e.preventDefault()}
            >
              <i className="fa-brands fa-google-play"></i>
              <div className="btn-text">
                <span className="small-text">Get it on</span>
                <span className="main-text">Google Play</span>
              </div>
            </a>
          </div>

          {/* Don't show again option */}
          <button 
            className="dont-show-again"
            onClick={handleDontShowAgain}
          >
            Don't show this again
          </button>
        </div>
      </div>
    </div>
  );
};

export default MobileAppDownloadPopup;
