"use client";
import React, { useState, useEffect, useRef } from "react";
import "./HotelFilter.scss";
import { HotelFilterData } from "@/app/components/utilities/helpers/hotel/filterHotels";
import { useTranslation } from "@/app/hooks/useTranslation";
import BudgetSlider from "./components/BudgetSlider/BudgetSlider";

interface HotelFilterProps {
  initialFilterData: HotelFilterData | undefined;
  handleChange: (data: HotelFilterData | undefined) => void;
  isSearchList: boolean;
  priceDistribution?: number[];
}

interface FilterItem {
  key: string | number;
  count: number;
  isSelected: boolean;
}

function HotelFilter({ initialFilterData, handleChange, isSearchList = false, priceDistribution }: HotelFilterProps) {
  const { t } = useTranslation();
  const [filterData, setFilterData] = useState<HotelFilterData | undefined>(initialFilterData);
  const [isFixed, setIsFixed] = useState(false);
  const filterContainerRef = useRef<HTMLDivElement>(null);
  const [initialPosition, setInitialPosition] = useState<number | null>(null);
  
  // Add a ref to track if this is the initial mount
  const initialMountRef = useRef(true);

  // Create a ref for the placeholder element
  const placeholderRef = useRef<HTMLDivElement>(null);

  // Create state variables to track expanded sections
  const [expandedSections, setExpandedSections] = useState({
    accommodationTypes: false,
    amenities: false,
    roomTypes: false,
    starRatings: false,
    specialOffers: false,
  });

  const [bedroomCount, setBedroomCount] = useState<number>(
    initialFilterData?.bedroom?.selected || 0
  );
  const [bathroomCount, setBathroomCount] = useState<number>(
    initialFilterData?.bathroom?.selected || 0
  );

  // Update filterData when initialFilterData changes
  useEffect(() => {
    // Skip the initial mount to avoid calling handleChange during render
    if (initialMountRef.current) {
      initialMountRef.current = false;
      
      // Set initial state without calling handleChange
      if (initialFilterData && typeof initialFilterData === 'object') {
        if (initialFilterData.priceRange || initialFilterData.starRatings || initialFilterData.amenities) {
          setFilterData(initialFilterData);

          if (initialFilterData.bedroom) {
            setBedroomCount(initialFilterData.bedroom.selected || 0);
          }

          if (initialFilterData.bathroom) {
            setBathroomCount(initialFilterData.bathroom.selected || 0);
          }
        }
      }
      return;
    }

    // For subsequent updates, only update local state if the data is genuinely different
    if (initialFilterData && typeof initialFilterData === 'object') {
      // Check if initialFilterData has the expected properties
      if (initialFilterData.priceRange || initialFilterData.starRatings || initialFilterData.amenities) {
        // Only update if the data is actually different to avoid infinite loops
        if (JSON.stringify(filterData) !== JSON.stringify(initialFilterData)) {
          setFilterData(initialFilterData);

          if (initialFilterData.bedroom) {
            setBedroomCount(initialFilterData.bedroom.selected || 0);
          }

          if (initialFilterData.bathroom) {
            setBathroomCount(initialFilterData.bathroom.selected || 0);
          }
        }
      }
    }
  }, [initialFilterData]); // Remove handleChange from dependencies

  // Number of items to display initially
  const initialDisplayCount = 4;

  useEffect(() => {
    if (isSearchList) {
      // Spacing constants
      const TOP_SPACING = 10; // Space between header and filter (px)
      const BOTTOM_SPACING = 10; // Space between filter and content below it (px)

      // Function to get current header height - allows for responsive headers
      const getHeaderHeight = () => {
        const headerElement = document.querySelector('header'); // Update selector to match your header
        return headerElement ? headerElement.offsetHeight : 60; // Fallback to 60px if not found
      };

      const handleScroll = () => {
        if (filterContainerRef.current) {
          // Get the initial position of the filter container if not already set
          if (initialPosition === null) {
            const rect = filterContainerRef.current.getBoundingClientRect();
            setInitialPosition(rect.top + window.scrollY);
          }

          const currentHeaderHeight = getHeaderHeight();

          // Check if the scroll position is past the point where we should make the filter fixed
          if (initialPosition !== null) {
            const shouldBeFixed = window.scrollY > initialPosition - currentHeaderHeight - TOP_SPACING;

            // Update the fixed state
            if (shouldBeFixed !== isFixed) {
              setIsFixed(shouldBeFixed);

              // If becoming fixed, update the placeholder height and width
              if (shouldBeFixed && filterContainerRef.current && placeholderRef.current) {
                // Get filter dimensions
                const filterHeight = filterContainerRef.current.offsetHeight;
                const filterWidth = filterContainerRef.current.offsetWidth;

                // Set placeholder to match filter dimensions (including bottom spacing)
                placeholderRef.current.style.height = `${filterHeight + BOTTOM_SPACING}px`;
                placeholderRef.current.style.width = `${filterWidth}px`;
                placeholderRef.current.style.display = "block";

                // Position the filter with proper spacing
                filterContainerRef.current.style.top = `${currentHeaderHeight + TOP_SPACING}px`;
              } else if (placeholderRef.current) {
                placeholderRef.current.style.display = "none";
                // Reset styles when not fixed
                if (filterContainerRef.current) {
                  filterContainerRef.current.style.top = '';
                }
              }
            } else if (shouldBeFixed && filterContainerRef.current) {
              // Update top position even when already fixed (in case header height changes)
              filterContainerRef.current.style.top = `${currentHeaderHeight + TOP_SPACING}px`;
            }
          }
        }
      };

      // Set initial position on mount
      if (filterContainerRef.current && initialPosition === null) {
        const rect = filterContainerRef.current.getBoundingClientRect();
        setInitialPosition(rect.top + window.scrollY);
      }

      window.addEventListener("scroll", handleScroll);
      window.addEventListener("resize", handleScroll);

      // Call once to set initial state
      handleScroll();

      return () => {
        window.removeEventListener("scroll", handleScroll);
        window.removeEventListener("resize", handleScroll);
      };
    } else {
      return;
    }
  }, [initialPosition, isFixed, isSearchList]);

  // Updated handler functions for BudgetSlider
  const handleMinChange = (value: number) => {
    setFilterData((prev) => {
      if (!prev) return prev;

      const updatedFilterData = {
        ...prev,
        priceRange: {
          ...prev.priceRange,
          values: {
            ...prev.priceRange.values,
            minimum: value,
          },
        },
      };

      handleChange(updatedFilterData);
      return updatedFilterData;
    });
  };

  const handleMaxChange = (value: number) => {
    setFilterData((prev) => {
      if (!prev) return prev;

      const updatedFilterData = {
        ...prev,
        priceRange: {
          ...prev.priceRange,
          values: {
            ...prev.priceRange.values,
            maximum: value,
          },
        },
      };

      handleChange(updatedFilterData);
      return updatedFilterData;
    });
  };

  const handleBedroomIncrement = () => {
    // Add safety check for filterData?.bedroom
    if (filterData?.bedroom && bedroomCount < (filterData.bedroom.max || 10)) {
      const newBedroomCount = bedroomCount + 1;
      setBedroomCount(newBedroomCount);

      setFilterData((prev) => {
        if (!prev || !prev.bedroom) return prev;

        const updatedFilterData = {
          ...prev,
          bedroom: {
            max: prev.bedroom.max,
            selected: newBedroomCount,
          },
        };

        handleChange(updatedFilterData);
        return updatedFilterData;
      });
    }
  };

  const handleBedroomDecrement = () => {
    if (bedroomCount > 0) {
      const newBedroomCount = bedroomCount - 1;
      setBedroomCount(newBedroomCount);

      setFilterData((prev) => {
        if (!prev || !prev.bedroom) return prev;

        const updatedFilterData = {
          ...prev,
          bedroom: {
            max: prev.bedroom.max,
            selected: newBedroomCount,
          },
        };

        handleChange(updatedFilterData);
        return updatedFilterData;
      });
    }
  };

  const handleBathroomIncrement = () => {
    if (filterData?.bathroom && bathroomCount < (filterData.bathroom.max || 10)) {
      const newBathroomCount = bathroomCount + 1;
      setBathroomCount(newBathroomCount);

      setFilterData((prev) => {
        if (!prev || !prev.bathroom) return prev;

        const updatedFilterData = {
          ...prev,
          bathroom: {
            max: prev.bathroom.max,
            selected: newBathroomCount,
          },
        };

        handleChange(updatedFilterData);
        return updatedFilterData;
      });
    }
  };

  const handleBathroomDecrement = () => {
    if (bathroomCount > 0) {
      const newBathroomCount = bathroomCount - 1;
      setBathroomCount(newBathroomCount);

      setFilterData((prev) => {
        if (!prev || !prev.bathroom) return prev;

        const updatedFilterData = {
          ...prev,
          bathroom: {
            max: prev.bathroom.max,
            selected: newBathroomCount,
          },
        };

        handleChange(updatedFilterData);
        return updatedFilterData;
      });
    }
  };

  const toggleSelection = (key: keyof HotelFilterData, index: number) => {
    setFilterData((prev) => {
      if (!prev || !Array.isArray(prev[key])) return prev;

      const items = prev[key] as { isSelected: boolean }[];
      if (!items[index]) return prev;

      const updatedItems = [...items];
      updatedItems[index] = {
        ...updatedItems[index],
        isSelected: !updatedItems[index].isSelected,
      };

      const updatedFilterData = { ...prev, [key]: updatedItems };
      handleChange(updatedFilterData); // Call handleChange immediately

      return updatedFilterData;
    });
  };

  // Toggle expanded state for a specific section
  const toggleExpanded = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleReset = () => {
    const resetData: HotelFilterData = {
      ...initialFilterData,
      priceRange: initialFilterData?.priceRange
        ? {
            min: initialFilterData.priceRange.min,
            max: initialFilterData.priceRange.max,
            values: {
              minimum: initialFilterData.priceRange.min,
              maximum: initialFilterData.priceRange.max,
            },
          }
        : {
            min: 0, // Default values if initialFilterData.priceRange is undefined
            max: 100,
            values: {
              minimum: 0,
              maximum: 100,
            },
          },

      accommodationTypes:
        initialFilterData?.accommodationTypes?.map((item) => ({
          ...item,
          isSelected: false,
        })) || [],

      amenities:
        initialFilterData?.amenities?.map((item) => ({
          ...item,
          isSelected: false,
        })) || [],

      roomTypes:
        initialFilterData?.roomTypes?.map((item) => ({
          ...item,
          isSelected: false,
        })) || [],

      starRatings:
        initialFilterData?.starRatings?.map((item) => ({
          ...item,
          isSelected: false,
        })) || [],

      specialOffers:
        initialFilterData?.specialOffers?.map((item) => ({
          ...item,
          isSelected: false,
        })) || [],

      userRatingCategories:
        initialFilterData?.userRatingCategories?.map((item) => ({
          ...item,
          isSelected: false,
        })) || [],

      bedroom: {
        selected: 0,
        max: initialFilterData?.bedroom?.max ?? 5,
      },
      bathroom: {
        selected: 0,
        max: initialFilterData?.bathroom?.max ?? 5,
      },

      distanceFromLandmark: initialFilterData?.distanceFromLandmark ?? {
        min: 0,
        max: 50,
      },

      availabilityStatus: {
        limitedRooms: { isSelected: false },
        lastMinuteDeals: { isSelected: false },
      },

      fomoFilters: initialFilterData?.fomoFilters ?? [],
    };

    setBedroomCount(resetData?.bedroom?.selected || 0);
    setBathroomCount(resetData?.bathroom?.selected || 0);
    console.log("reserdata", resetData);
    setFilterData(resetData);
    handleChange(resetData);

    // Reset expanded sections
    setExpandedSections({
      accommodationTypes: false,
      amenities: false,
      roomTypes: false,
      starRatings: false,
      specialOffers: false,
    });
  };

  const isResetEnabled = () => {
    if (!filterData) return false; // ✅ Ensure filterData is defined

    return (
      filterData?.accommodationTypes?.some((item) => item.isSelected) ||
      filterData?.amenities?.some((item) => item.isSelected) ||
      filterData?.roomTypes?.some((item) => item.isSelected) ||
      filterData?.starRatings?.some((item) => item.isSelected) ||
      filterData?.specialOffers?.some((item) => item.isSelected) ||
      filterData?.userRatingCategories?.some((item) => item.isSelected) ||
      (filterData?.bedroom && filterData.bedroom.selected > 0) || // ✅ Add null check for bedroom
      (filterData?.bathroom && filterData.bathroom.selected > 0) || // ✅ Add null check for bathroom
      filterData?.distanceFromLandmark?.min !==
        initialFilterData?.distanceFromLandmark?.min ||
      filterData?.distanceFromLandmark?.max !==
        initialFilterData?.distanceFromLandmark?.max ||
      filterData?.availabilityStatus?.limitedRooms?.isSelected ||
      filterData?.availabilityStatus?.lastMinuteDeals?.isSelected ||
      filterData?.priceRange?.values?.maximum !== filterData?.priceRange?.max ||
      filterData?.priceRange?.values?.minimum !== filterData?.priceRange?.min
    );
  };

  // Helper function to render filter items with view more/less functionality
  const renderFilterItems = (
    items: FilterItem[],
    section: keyof typeof expandedSections,
    toggleFunction: (index: number) => void
  ) => {
    if (!items || items.length === 0) return null;

    const itemsToShow = expandedSections[section]
      ? items
      : items.slice(0, initialDisplayCount);
    const hasMoreItems = items.length > initialDisplayCount;

    return (
      <>
        <div className="filters">
          {itemsToShow.map((item, index) => (
            <div className="filter-item" key={index}>
              <div className="checkBox">
                <input
                  type="checkbox"
                  onChange={() => toggleFunction(index)}
                  checked={item?.isSelected}
                />
              </div>
              <div className="content">
                <span className="label">
                  {section === "starRatings"
                    ? `${item?.key} ${t("filters.stars")}`
                    : item?.key}
                </span>
                <span className="count">{item?.count}</span>
              </div>
            </div>
          ))}
        </div>
        {hasMoreItems && (
          <span className="view-more" onClick={() => toggleExpanded(section)}>
            {expandedSections[section] ? (
              <i className="fa-solid fa-chevron-up"></i>
            ) : (
              <i className="fa-solid fa-chevron-down"></i>
            )}

            {expandedSections[section]
              ? t("filters.viewLess")
              : t("filters.viewMore")}
          </span>
        )}
      </>
    );
  };

  return (
    <>
      {/* Placeholder div to maintain layout when filter is fixed */}
      <div ref={placeholderRef} style={{ display: "none" }}></div>

      {/* Actual filter container */}
      <div
        ref={filterContainerRef}
        className={`hotel-filter-container ${isFixed ? "fixed" : ""}`}
        style={
          isFixed
            ? {
                left: filterContainerRef.current
                  ? `${
                      filterContainerRef.current.getBoundingClientRect().left
                    }px`
                  : "0",
              }
            : {}
        }
      >

<div className="mobileFilterResetBtnWrapper">
  <button
            disabled={!isResetEnabled()}
            onClick={handleReset}
            className={`mobileFilterResetBtn ${isResetEnabled() ? "enabled" : ""}`}
          >
            {t("filters.clear")}
          </button>
</div>
          
        <div className="filter-head">
          <h3>{t("filters.filterBy")}</h3>{" "}
          <button
            disabled={!isResetEnabled()}
            onClick={handleReset}
            className={`resetBtn ${isResetEnabled() ? "enabled" : ""}`}
          >
            {t("filters.clear")}
          </button>
        </div>
         

        <div className="filter-body">
          {filterData?.accommodationTypes &&
          filterData?.accommodationTypes?.length > 0 ? (
            <div className="type-filter">
              <div className="head">
                <h5>{t("filters.accommodationTypes")}</h5>
              </div>
              {renderFilterItems(
                filterData?.accommodationTypes,
                "accommodationTypes",
                (index) => toggleSelection("accommodationTypes", index)
              )}
            </div>
          ) : (
            <></>
          )}

          {filterData?.amenities && filterData?.amenities.length > 0 ? (
            <div className="type-filter">
              <div className="head">
                <h5>{t("filters.amenities")}</h5>
              </div>
              {renderFilterItems(filterData?.amenities, "amenities", (index) =>
                toggleSelection("amenities", index)
              )}
            </div>
          ) : (
            <></>
          )}

          {/* Replace the old budget slider with the new BudgetSlider component */}
          {filterData?.priceRange && (
            <BudgetSlider
              priceRange={filterData.priceRange}
              onMinChange={handleMinChange}
              onMaxChange={handleMaxChange}
              currency="₹"
              histogramData={priceDistribution || []}
            />
          )}

          {filterData?.bedroom && filterData?.bathroom && (
            <div className="washroom-counter">
              <div className="head">
                <h5>{t("filters.rooms.title")}</h5>
              </div>

              <div className="counter">
                <div className="label">{t("filters.rooms.bedrooms")}</div>
                <div className="counter-buttons">
                  <div
                    className={`btn ${bedroomCount < 1 ? "inactive" : ""}`}
                    onClick={handleBedroomDecrement}
                  >
                    -
                  </div>
                  <span className="count">{bedroomCount}</span>
                  <div
                    className={`btn ${
                      bedroomCount >= (filterData?.bedroom?.max || 5) ? "inactive" : ""
                    }`}
                    onClick={handleBedroomIncrement}
                  >
                    +
                  </div>
                </div>
              </div>

              <div className="counter">
                <div className="label">{t("filters.rooms.bathrooms")}</div>
                <div className="counter-buttons">
                  <div
                    className={`btn ${bathroomCount < 1 ? "inactive" : ""}`}
                    onClick={handleBathroomDecrement}
                  >
                    -
                  </div>
                  <span className="count">{bathroomCount}</span>
                  <div
                    className={`btn ${
                      bathroomCount >= (filterData?.bathroom?.max || 5)
                        ? "inactive"
                        : ""
                    }`}
                    onClick={handleBathroomIncrement}
                  >
                    +
                  </div>
                </div>
              </div>
            </div>
          )}

          {filterData?.roomTypes && filterData?.roomTypes.length > 0 ? (
            <div className="type-filter">
              <div className="head">
                <h5>{t("filters.propertyTypes")}</h5>
              </div>
              {renderFilterItems(filterData?.roomTypes, "roomTypes", (index) =>
                toggleSelection("roomTypes", index)
              )}
            </div>
          ) : (
            <></>
          )}

          {filterData?.starRatings && filterData?.starRatings.length > 0 ? (
            <div className="type-filter">
              <div className="head">
                <h5>{t("filters.propertyRating")}</h5>
              </div>
              {renderFilterItems(
                filterData?.starRatings.sort((a, b) => a.key - b.key),
                "starRatings",
                (index) => toggleSelection("starRatings", index)
              )}
            </div>
          ) : (
            <></>
          )}

          {filterData?.specialOffers && filterData?.specialOffers.length > 0 ? (
            <div className="type-filter">
              <div className="head">
                <h5>{t("filters.facilities")}</h5>
              </div>
              {renderFilterItems(
                filterData?.specialOffers,
                "specialOffers",
                (index) => toggleSelection("specialOffers", index)
              )}
            </div>
          ) : (
            <></>
          )}
        </div>
      </div>
    </>
  );
}

export default HotelFilter;