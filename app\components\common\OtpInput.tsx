'use client';

import React, { useRef, useEffect, KeyboardEvent, ClipboardEvent, ChangeEvent } from 'react';
import './OtpInput.scss';

interface OtpInputProps {
  length: number;
  value: string[];
  onChange: (otp: string[]) => void;
  disabled?: boolean;
  autoFocus?: boolean;
  className?: string;
  inputClassName?: string;
}

const OtpInput: React.FC<OtpInputProps> = ({
  length,
  value,
  onChange,
  disabled = false,
  autoFocus = false,
  className = '',
  inputClassName = '',
}) => {
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Initialize refs array with the correct length
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
    // Focus on first input when component mounts if autoFocus is true
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [length, autoFocus]);

  // Handle input change
  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number) => {
    const newValue = e.target.value;
    
    // Only proceed if the input is a single digit or empty
    if (!/^\d*$/.test(newValue)) {
      return;
    }

    // Create a copy of the current OTP array
    const otpArray = [...value];
    
    // Update the value at the current index (take only the last character if multiple were somehow entered)
    otpArray[index] = newValue.slice(-1);
    
    // Call the onChange callback with the updated array
    onChange(otpArray);
    
    // If a digit was entered and there's a next input, focus on it
    if (newValue && index < length - 1 && inputRefs.current[index + 1]) {
      const nextInput = inputRefs.current[index + 1];
        if (nextInput) {
          nextInput.focus();
        }
    }
  };

  // Handle key down events for navigation
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    // Handle backspace
    if (e.key === 'Backspace') {
      if (value[index] === '') {
        // If current input is empty and there's a previous input, focus on it
        if (index > 0 && inputRefs.current[index - 1]) {
          const prevInput = inputRefs.current[index - 1]
          if(prevInput){
            prevInput.focus();
          }
          
          // Clear the previous input value
          const otpArray = [...value];
          otpArray[index - 1] = '';
          onChange(otpArray);
          
          // Prevent default to avoid browser navigation
          e.preventDefault();
        }
      }
    } 
    // Handle left arrow key
    else if (e.key === 'ArrowLeft') {
      if (index > 0 && inputRefs.current[index - 1]) {
        const nextInput = inputRefs.current[index - 1];
        if (nextInput) {
          nextInput.focus();
        }
        e.preventDefault();
      }
    } 
    // Handle right arrow key
    else if (e.key === 'ArrowRight') {
      if (index < length - 1 && inputRefs.current[index + 1]) {
        const nextInput = inputRefs.current[index + 1];
        if (nextInput) {
          nextInput.focus();
        }
        e.preventDefault();
      }
    }
  };

  // Handle paste event
  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();
    
    // Check if pasted data contains only digits
    if (!/^\d+$/.test(pastedData)) {
      return;
    }
    
    // Create a new array with the pasted digits
    const otpArray = [...value];
    const pastedChars = pastedData.split('').slice(0, length);
    
    pastedChars.forEach((char, idx) => {
      otpArray[idx] = char;
    });
    
    // Update the OTP value
    onChange(otpArray);
    
    // Focus on the next empty input or the last input if all are filled
    const nextEmptyIndex = otpArray.findIndex(val => val === '');
    const focusIndex = nextEmptyIndex === -1 ? length - 1 : nextEmptyIndex;
    
    if (inputRefs.current[focusIndex]) {
      inputRefs.current[focusIndex].focus();
    }
  };

  return (
    <div className={`otp-input-container ${className}`}>
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={el => {
            inputRefs.current[index] = el;
          }}

          type="text"
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={value[index] || ''}
          onChange={e => handleChange(e, index)}
          onKeyDown={e => handleKeyDown(e, index)}
          onPaste={handlePaste}
          disabled={disabled}
          className={`otp-input ${inputClassName} ${value[index] ? 'filled' : ''}`}
          aria-label={`OTP digit ${index + 1}`}
        />
      ))}
    </div>
  );
};

export default OtpInput;
