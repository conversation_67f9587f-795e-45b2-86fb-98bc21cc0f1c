"use client";
import { useState } from "react";
import "./HotelOverviewComponent.scss";
import Image from "next/image";
import PhotoGalleryModal from "../PhotoGalleryModal/PhotoGalleryModal";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import HotelSearchMap from "@/app/HotelSearchResult/components/HotelSearchMap/HotelSearchMap";

function HotelOverviewComponent() {
  const [isPhotoGalleryModalActive, setIsPhotoGalleryModalAcive] = useState(false);
  useScrollLock(isPhotoGalleryModalActive);

  return (
    <div className="hotel-overview-container">
      <div className="hotel-overview-container__left-section">
        <div className="left-section-row1">
          <div className="left-section-row1__flex-col col1">
            <Image
              src={
                "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768"
              }
              alt={"hotel-image"}
              fill
              style={{ objectFit: "cover" }}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          <div className="left-section-row1__flex-col col2">
            <div className="sub-col">
              <Image
                src={
                  "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768"
                }
                alt={"hotel-room"}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
            <div className="sub-col">
              <Image
                src={
                  "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768"
                }
                alt={"hotel-room"}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            </div>
          </div>
        </div>
        <div className="left-section-row2">
          {Array.from({ length: 5 }).map((_, index) => (
            <div className="left-section-row2__col" key={index}>
              <Image
                src={
                  "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768"
                }
                alt={`hotel-room-${index}`}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              {index === 4 && <div className="image-overlay" onClick={() => setIsPhotoGalleryModalAcive(true)}></div>}
            </div>
          ))}

          <div className="morePhotos">
            <i className="fa-solid fa-images"></i>
            <span className="label" onClick={() => setIsPhotoGalleryModalAcive(true)}>+5 Photos</span>
          </div>
        </div>
      </div>
      <div className="hotel-overview-container__right-section">
        {/* review floater */}
        <div className="reviewFloater">
          <div className="hp-gallery-score-card">
            <div className="review">
              <div className="rating-detail">
                <p className="detail detail1">Good</p>
                <p className="detail detail2">392 reviews</p>
              </div>
              <div className="rating">7.2</div>
            </div>
          </div>
          <div className="best-review-score-card">
            <div className="best-review-score-card__label">Free Wifi</div>
            <div className="best-review-score-card__count">10</div>
          </div>
        </div>

        {/* map floater */}
        <div className="hotel-search-map-container" style={{height:'75%'}}>
          <HotelSearchMap 
            isDetail={true}
            detailData={{
              name: "Hotel Example",
              rating: 8.5,
              stars: "★★★★",
              position: [9.9482882, 76.3194938],
              description: "Excellent",
              reviews: "1,234 reviews",
              locationScore: "Great location - 9.2",
              address: "123 Main St, City, Country",
              attractions: [
                { name: "Beach", distance: "0.5 km" },
                { name: "City Center", distance: "1.2 km" }
              ]
            }}
            onDetailFavoriteClick={() => console.log('Favorite clicked')}
            onDetailCloseClick={() => console.log('Close clicked')}
            onDetailShowPrices={() => console.log('Show prices clicked')}
          />
        </div>
      </div>

      <PhotoGalleryModal 
        hotelName="Santana Beach Resort" 
        isOpen={isPhotoGalleryModalActive} 
        onClose={() => setIsPhotoGalleryModalAcive(false)} 
      />
    </div>
  );
}

export default HotelOverviewComponent;