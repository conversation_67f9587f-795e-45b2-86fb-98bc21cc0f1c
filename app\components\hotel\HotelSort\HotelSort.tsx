import React, { useState } from 'react'
import './HotelSort.scss'

interface HotelSortProps {
  sortOptions: string[]
  selectedSort: string
  onSortChange: (sortOption: string) => void
  isMobile?: boolean
  defaultSort?: string
}

function HotelSort({
  sortOptions,
  selectedSort,
  onSortChange,
  isMobile = false,
  defaultSort = "Top picks for long stays"
}: HotelSortProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false)

  const toggleDropdown = () => {
    setIsDropdownOpen((prev) => !prev)
  }

  const handleSortSelection = (option: string): void => {
    onSortChange(option)
    setIsDropdownOpen(false)
  }

  // Mobile view - render options directly (full width)
  if (isMobile) {
    return (
      <div className="hotel-sort-mobile">
        {sortOptions?.map((option, index) => (
          <div
            className={`sort-filter ${
              selectedSort === option ? "active" : ""
            }`}
            key={index}
            onClick={() => handleSortSelection(option)}
          >
            <span className="sort-option-text">{option}</span>
            {selectedSort === option && (
              <span className="material-icons check-icon">check</span>
            )}
          </div>
        ))}
      </div>
    )
  }

  // Desktop view - render dropdown with box styling
  return (
    <div className="hotel-sort-desktop">
      <div className="sort-button-container">
        <div
          className={`sort-button ${
            selectedSort !== defaultSort ? "active" : ""
          }`}
          onClick={toggleDropdown}
        >
          <span className="material-icons swapIcon">swap_vert</span>
          <span className="sort-text">Sort by: {selectedSort}</span>
          <i className="fa-solid fa-sort"></i>
        </div>

        {isDropdownOpen && (
          <>
            <div className="sort-top-popup">
              <div className="top-picks-filter-container">
                {sortOptions?.map((option, index) => (
                  <div
                    className={`sort-filter ${
                      selectedSort === option ? "active" : ""
                    }`}
                    key={index}
                    onClick={() => handleSortSelection(option)}
                  >
                    <span className="sort-option-text">{option}</span>
                    {selectedSort === option && (
                      <span className="material-icons check-icon">check</span>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div
              className="sort-top-popup-overlay"
              onClick={() => setIsDropdownOpen(false)}
            ></div>
          </>
        )}
      </div>
    </div>
  )
}

export default HotelSort