@use '/styles/variable' as *;

.hotel-sort-desktop {
  .sort-button-container {
    margin-bottom: 3px;
    position: relative;

    .sort-button {
      width: fit-content;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      padding: 8px 12px;
      border: 1px solid rgba(0, 0, 0, 0.2);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      background-color: #fff;

      &:hover {
        border-color: $primary_color;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.active {
        border-color: $primary_color;
        color: $primary_color;
        background-color: rgba(0, 59, 149, 0.05);
        box-shadow: 0 2px 8px rgba(0, 59, 149, 0.15);
      }

      .sort-icon {
        color: $primary_color;
        flex-shrink: 0;
      }

      .sort-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }
    }
  }

  .sort-top-popup {
    position: absolute;
    top: 45px;
    left: 0;
    background: white;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    z-index: z-index(dropdown);
    min-width: 280px;
    max-height: 400px;
    overflow-y: auto;
    animation: fadeInUp 0.2s ease-out;

    .top-picks-filter-container {
      display: flex;
      flex-direction: column;
      padding: 8px 0;

      .sort-filter {
        width: 100%;
        padding: 12px 16px;
        font-size: 14px;
        transition: all 0.2s ease;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 0;

        &:hover {
          background-color: rgba(0, 59, 149, 0.04);
        }

        &.active {
          color: $primary_color;
          background-color: rgba(0, 59, 149, 0.08);
          font-weight: 500;
        }

        .sort-option-text {
          flex: 1;
          font-size: 14px;
          line-height: 1.4;
        }

        .check-icon {
          color: $primary_color;
          flex-shrink: 0;
          margin-left: 8px;
        }
      }
    }
  }

  .sort-top-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: z-index(overlay);
  }
}

.hotel-sort-mobile {
  width: 100%;
  max-height: calc(100vh - 235px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;

  .sort-filter {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      background-color: rgba(0, 59, 149, 0.04);
    }

    &.active {
      color: $primary_color;
      background-color: rgba(0, 59, 149, 0.08);
      font-weight: 500;
    }

    .sort-option-text {
      flex: 1;
      font-size: 14px;
      line-height: 1.4;
    }

    .check-icon {
      color: $primary_color;
      flex-shrink: 0;
      margin-left: 8px;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
