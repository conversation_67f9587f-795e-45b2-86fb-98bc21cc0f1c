"use client";
import React, { useState } from 'react';
import './Wishlist.scss';

// Define an interface for hotel data
interface Hotel {
  id: number;
  name: string;
  location: string;
  image: string;
  price: number;
  rating: number;
  reviewCount: number;
  facilities: string[];
  cancellationPolicy: string;
  discountPercent?: number;
}

const Wishlist: React.FC = () => {
  // This would typically come from your state management or API
  const [wishlistedHotels, setWishlistedHotels] = useState<Hotel[]>([
    {
      id: 1,
      name: "Grand Hotel Plaza",
      location: "Rome, Italy",
      image: "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
      price: 250,
      rating: 4.7,
      reviewCount: 432,
      facilities: ["Free WiFi", "Pool", "Spa"],
      cancellationPolicy: "Free cancellation",
      discountPercent: 15
    },
    {
      id: 2,
      name: "Seaside Resort",
      location: "Bali, Indonesia",
      image: "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
      price: 180,
      rating: 4.5,
      reviewCount: 215,
      facilities: ["Beachfront", "Breakfast"],
      cancellationPolicy: "Non-refundable"
    }
  ]);

  // Function to remove hotel from wishlist
  const removeFromWishlist = (hotelId: number) => {
    setWishlistedHotels(wishlistedHotels.filter(hotel => hotel.id !== hotelId));
  };

  return (
    <div>
 
        <h2 className="wishlist-title">My Favorites</h2>
        
        {wishlistedHotels.length > 0 ? (
          <div className="wishlist-items">
            {wishlistedHotels.map(hotel => (
              <div className="hotel-card" key={hotel.id}>
                <div className="hotel-image">
                  <img src={hotel.image} alt={hotel.name} />
                  {hotel.discountPercent && (
                    <div className="discount-badge">
                      {hotel.discountPercent}% OFF
                    </div>
                  )}
                </div>
                
                <div className="hotel-details">
                  <h3>{hotel.name}</h3>
                  
                  <div className="hotel-location">
                    <i className="fa-solid fa-location-dot"></i>
                    <span>{hotel.location}</span>
                  </div>
                  
                  <div className="hotel-rating">
                    <i className="fa-solid fa-star"></i>
                    <span>{hotel.rating}</span>
                    <span className="review-count">({hotel.reviewCount} reviews)</span>
                  </div>
                  
                  <div className="hotel-facilities">
                    {hotel.facilities.map((facility, index) => (
                      <span className="facility-badge" key={index}>
                        {facility}
                      </span>
                    ))}
                  </div>
                  
                  <div className="cancellation-policy">
                    {hotel.cancellationPolicy}
                  </div>
                </div>
                
                <div className="hotel-actions">
                  <div className="hotel-price">
                    <span>${hotel.price}</span> per night
                  </div>
                  
                  {/* <button className="btn-view">View Details</button> */}
                  <button 
                    className="btn-remove"
                    onClick={() => removeFromWishlist(hotel.id)}
                  >
                    <i className="fa-solid fa-heart"></i>
                    <span>Remove</span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-wishlist-found">
            <i className="fa-solid fa-heart"></i>
            <h5>No favourites found.</h5>
            <p>Items added to your wishlist will appear here</p>
          </div>
        )}
 
    </div>
  );
};

export default Wishlist;