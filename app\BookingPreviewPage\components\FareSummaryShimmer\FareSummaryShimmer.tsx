"use client";
import React from "react";
import "./FareSummaryShimmer.scss";

function FareSummaryShimmer() {
  return (
    <div className="fare-summary-shimmer-container">
      <h6 className="fare-summary-shimmer-container__header"></h6>

      <div className="fare-summary-shimmer-container__label-price">
        <div className="label"></div>
        <div className="price"></div>
      </div>

      <div className="fare-summary-shimmer-container__label-price">
        <div className="label"></div>
        <div className="price"></div>
      </div>

      <hr />

      <div className="fare-summary-shimmer-container__label-price">
        <div className="label discounts"></div>
        <div className="price discounts"></div>
      </div>

      <hr />

      <div className="fare-summary-shimmer-container__label-price">
        <div className="label netAmt"></div>
        <div className="price netAmt">
          <span className="shimmer sub"></span>
        </div>
      </div>

      <div className="fare-summary-shimmer-container__label-price">
        <div className="label netAmt"></div>
        <div className="price netAmt">
          <span className="shimmer sub"></span>
        </div>
      </div>
    </div>
  );
}

export default FareSummaryShimmer;
