@use "tailwindcss/base";
@use "tailwindcss/components";
@use "tailwindcss/utilities";
@use './mixins' as *;
@use "sass:color";
@use '@styles/variable' as *;
@use './button';
@use './_rtl';
@use './zIndex' as *;

@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&family=Noto+Naskh+Arabic:wght@400;500;600;700&display=swap');
:root {
  --background: #ffffff;
  --foreground: #171717;
}

// Dark mode (uncomment if needed)
/*
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/


body {
  color: var(--foreground);
  background: var(--background);
  font-family: "Open Sans", sans-serif;

  &:lang(ar) {
    font-family: "Noto Naskh Arabic", "Open Sans", sans-serif;
  }
}


.scroll-bar-1{
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, $black_color2, $black_color3);
    border-radius: 10px;
    border: 2px solid #f0f0f0;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, $black_color, $black_color2);
  }
}

// .container {
//   width: 100%;
//   max-width: 1300px;
//   padding: 0 70px;
//   margin: 0 auto;
//   box-sizing: border-box;

//   @media screen and (max-width: 1400px) {
//     .container {
//         max-width: 1320px;
//     }
//   }

//   @media screen and (max-width: 1200px) {
//     .container {
//         max-width: 1140px;
//     }
//   }

//   @media screen and (max-width: 992px) {
//     .container {
//         max-width: 960px;
//         padding: 0 40px;
//     }
//   }
//   @media screen and (max-width: 962px) {
//     .container {
//         max-width: 950px;
//     }
//   }
//   @media screen and (max-width: 768px) {
//     .container {
//         max-width: 720px;
//         padding: 0 10px;
//     }
//   }

//   @media screen and (max-width: 576px) {
//     .container {
//         max-width: 540px;
//         padding: 0 10px;
//     }
//   }
// }


// .container2 {
//   width: 100%;
//   max-width: 1500px;
//   padding: 0 90px;
//   margin: 0 auto;
//   box-sizing: border-box;

//   @media screen and (max-width: 1600px) {
//     .container2 {
//       max-width: 1520px;
//     }
//   }

//   @media screen and (max-width: 1400px) {
//     .container2 {
//       max-width: 1340px;
//     }
//   }

//   @media screen and (max-width: 1200px) {
//     .container2 {
//       max-width: 1120px;
//       padding: 0 60px;
//     }
//   }

//   @media screen and (max-width: 992px) {
//     .container2 {
//       max-width: 1060px;
//     }
//   }

//   @media screen and (max-width: 820px) {
//     .container2 {
//       max-width: 800px;
//       padding: 0 30px;
//     }
//   }

//   @media screen and (max-width: 620px) {
//     .container2 {
//       max-width: 620px;
//       padding: 0 20px;
//     }
//   }
// }


// .container3 {
//   width: 100%;
//   max-width: 1700px;
//   padding: 0 100px;
//   margin: 0 auto;
//   box-sizing: border-box;

//   @media screen and (max-width: 1800px) {
//     .container3 {
//       max-width: 1720px;
//     }
//   }

//   @media screen and (max-width: 1600px) {
//     .container3 {
//       max-width: 1540px;
//     }
//   }

//   @media screen and (max-width: 1400px) {
//     .container3 {
//       max-width: 1360px;
//       padding: 0 80px;
//     }
//   }

//   @media screen and (max-width: 1200px) {
//     .container3 {
//       max-width: 1240px;
//       padding: 0 60px;
//     }
//   }

//   @media screen and (max-width: 992px) {
//     .container3 {
//       max-width: 1100px;
//       padding: 0 50px;
//     }
//   }

//   @media screen and (max-width: 820px) {
//     .container3 {
//       max-width: 880px;
//       padding: 0 30px;
//     }
//   }

//   @media screen and (max-width: 620px) {
//     .container3 {
//       max-width: 660px;
//       padding: 0 20px;
//     }
//   }
// }


.primary-background{
  background-color: $primary_color;
  color: white;
}

.page-container{
  width: 100dvw;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .header-container{
    width: 100%;
    height: 80px;
    //overflow: hidden;
  }
  .content-container{
    width: 100%;
    height: calc(100% - 80px);
    overflow-y: auto;
  }
}


.landing-page-container{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.common-container {
  width: 100%;
  max-width: 1300px;
  padding: 0 70px;
  margin: 0 auto;
  box-sizing: border-box;

  @media screen and (max-width: 1400px) {
    max-width: 1320px;
  }

  @media screen and (max-width: 1200px) {
    max-width: 1140px;
  }

  @media screen and (max-width: 992px) {
    max-width: 960px;
    padding: 0 40px;
  }

  @media screen and (max-width: 962px) {
    max-width: 950px;
  }

  @media screen and (max-width: 768px) {
    max-width: 720px;
    padding: 0 10px;
  }

  @media screen and (max-width: 576px) {
    max-width: 540px;
    padding: 0 10px;
  }
}

.wf-50{
  width: 50%;
}

.profile-form-error{
  font-size: 12px;
  color: red;
}

.mobile-bottom-to-top-modal-container{
  position: fixed;
  left: 0;
  right: 0;
  top: 90px;
  bottom: 0;
  width: 100%;
  height: 100dvh;
  display: flex;
  justify-content: center;
  //align-items: flex-end;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transform: translateY(100%);
  transition: opacity 0.3s ease, visibility 0.3s ease;
  background-color: #fff;
  max-height: 100vh;
  overflow-y: auto;

  &.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    z-index: z-index(modal)+1;
  }


  .mobile-bottom-to-top-modal-content{
    width: 100%;
    height: calc(100% - 100px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow-y: auto;


    



    .mobile-bottom-to-top-modal-content-head{
      padding: 8px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      h3 {
        font-size: 16px;
        font-weight: 700;
      }

      .resetBtn{
        font-size: 12px;
        color:  rgb(239.4, 233, 227.6);
        font-weight: 600;
        cursor: not-allowed;
        transition: color 0.2s ease-in-out;

        &.enabled {
          color: $primary-color;
          cursor: pointer;

        }


      }
    }}


    .top-picks-mobile-filter-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      &__sort-filter {
        width: 100%;
        padding: 12px 16px;
        font-size: 14px;
        transition: background-color 0.3s ease;
        cursor: pointer;

        &:hover {
          background-color: color.adjust(white, $lightness: -5%);
        }
      }
    }


}

.mobile-bottom-to-top-modal-header {
  position: fixed;
    height: 50px;
    width: 100%;
    background-color: #fff;
    top: 0;
    z-index: z-index(base);
    display: none;
    align-items: center;
    justify-content: end;
    padding: 25px 10px;

    @media (max-width: $isMobile){
      display: flex;
      justify-content: center;
    }

    &__close-bttn {
      position: absolute;
      left: 10px;
      height: 30px;
      width: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 50%;
      border: 1px solid $primary-color;
      color: $primary-color;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: color.adjust(#fff, $lightness: -5%);
      }
    }

    .mobile-bottom-to-top-modal-heading {
      font-weight: 600;
      color: black;
      font-size: 16px;
    }
}

/* Map Tooltip Styles */
.hotel-marker-tooltip-container {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 4px 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  max-width: 200px;
  z-index: z-index(modal);
  pointer-events: none;

  .leaflet-tooltip-content {
    margin: 0;
    padding: 0;
  }

  &::before {
    display: none;
  }
}

.hotel-marker-tooltip {
  text-align: center;
  white-space: nowrap;

  &__name {
    font-weight: 600;
    font-size: 12px;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__details {
    display: flex;
    justify-content: center;
    gap: 8px;
  }

  &__rating {
    color: #FFB800;
    font-size: 11px;
  }

  &__price {
    color: $primary_color;
    font-weight: 600;
    font-size: 11px;
  }
}




