export interface Root {
  isCompleted: boolean
  locationInfo: LocationInfo
  results: string[]
  searchKey: string
}

export interface LocationInfo {
  hotels: Hotel[]
  location_id: string
  search_parameters: SearchParameters
  status: string
  total_found: number
  total_hotels: number
}

export interface Hotel {
  about: any
  additional_information: any[]
  address: string
  attributes: Attribute[]
  attributes_json: AttributesJson[]
  available_suppliers: string[]
  category: string
  chain_code: string
  chain_name: string
  city: string
  country: string
  country_code: string
  data_source_provider: string
  data_source_updated_at: any
  descriptions: any[]
  facilities: any[]
  faxes_json?: string[]
  hero_image: string
  hotel_id: string
  id: number
  image_count: string
  images: Image[]
  language: string
  latitude: number
  license_number: string
  longitude: number
  name: string
  phones_json: string[]
  policies: any[]
  postal_code: string
  provider_hotel_id: string
  provider_id: string
  provider_name: string
  rating_score: number
  recent_pricing: any[]
  relevance_score: string
  reviews: any[]
  rooms: any[]
  star_rating: string
  state_name: string
  type: string
}

export interface Attribute {
  attribute_category: string
  attribute_key: string
  attribute_value: string
  id: number
}

export interface AttributesJson {
  key: string
  value: string
}

export interface Image {
  alt_text: string
  id: number
  image_category_type: string
  image_height: number
  image_path: string
  image_width: number
  is_hero_image: boolean
  sort_order: number
}

export interface SearchParameters {
  boundaries: any
  coordinates: Coordinates
  limit_applied: any
  radius_degrees: number
  search_type: string
}

export interface Coordinates {
  lat: number
  long: number
}
