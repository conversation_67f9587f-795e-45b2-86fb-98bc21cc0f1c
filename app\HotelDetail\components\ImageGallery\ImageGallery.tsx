"use client";
import React, { useEffect, useState } from "react";
import "./ImageGallery.scss";
import Image from "next/image";
import SlideFromRightModal from "../SlideFromRightModal/SlideFromRightModal";
import ImageLightBox from "../ImageLightBox/ImageLightBox";
import useScrollLock from "@/app/components/utilities/ScrollLock/useScrollLock";
import { useTranslation } from "@/app/hooks/useTranslation";
import { CarouselImage, NewImageInfo } from "../../hotel-detail-result.model";

interface ImageGalleryProps {
  images: CarouselImage[];
  newImageInfo?: NewImageInfo;
}

function ImageGallery({ images }: ImageGalleryProps) {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isImgLightBoxOpen, setIsImgLightBoxOpen] = useState<boolean>(false);
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    const getCategoriesFromImage = () => {
      
        const categoryArray = images.map((image) => image.imageCategory);
        const uniqueCategories = Array.from(new Set(categoryArray));
        const categoriesWithAllImagesFirst = ['All Photos', ...uniqueCategories];

        // Step 4: Update state with the categories
        setCategories(categoriesWithAllImagesFirst);
       
    };

    getCategoriesFromImage();
  }, [images]);

  // const categories = [
  //   t('hotel.detail.allPhotos'),
  //   t('hotel.detail.roomsPhotos'),
  //   t('hotel.detail.exteriorPhotos'),
  //   t('hotel.detail.publicAreasPhotos'),
  //   t('hotel.detail.bathroomPhotos'),
  //   t('hotel.detail.diningAreasPhotos'),
  //   t('hotel.detail.foodBeveragesPhotos'),
  //   t('hotel.detail.facilitiesPhotos'),
  // ];
  const mainImage = images[0];
  const subImages = images.slice(1, 5);
  const remainingImages = images.length - 5;

  //stated for modal
  const [activeCategory, setActiveCategory] = useState<string>("All Photos");
  const filteredImages =
    activeCategory === "All Photos"
      ? images
      : images.filter(
          (image) =>
            image.imageCategory.toLowerCase() === activeCategory.toLowerCase()
        );

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  useScrollLock(isModalOpen);

  return (
    <div className="image-gallery-container">
      <div className="flex-row">
        <div className="flex-col col1">
          {mainImage ? (
            <Image
              src={mainImage?.url}
              alt={mainImage?.caption}
              fill
              style={{ objectFit: "cover" }}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            ></Image>
          ) : (
            <div className="placeholder">No Image</div>
          )}
        </div>
        <div className="flex-col col2">
          {subImages?.map((image, index) => (
            <div className="sub-col" key={index}>
              <Image
                src={image?.url}
                alt={image?.caption}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              ></Image>
            </div>
          ))}

          {remainingImages > 0 && (
            <div className="morePhotos" onClick={() => setIsModalOpen(true)}>
              <i className="fa-solid fa-images"></i>

              <span className="label">{remainingImages}+ Photos</span>
            </div>
          )}
        </div>
        {/* <ImageGalleryModal
          images={images}
          isOpen={isModalOpen}
          handleCloseModal={handleCloseModal}
        /> */}

        <SlideFromRightModal
          isOpen={isModalOpen}
          handleClose={handleCloseModal}
          title="Hotel Pearl"
        >
          <div className="categories">
            {categories.map((category) => (
              <div
                key={category}
                className={`category ${
                  activeCategory == category ? "active" : ""
                }`}
                onClick={() => setActiveCategory(category)}
              >
                {category}
              </div>
            ))}
          </div>

          <div
            className="image-flex"
            onClick={() => setIsImgLightBoxOpen(true)}
          >
            {filteredImages.length > 0 ? (
              filteredImages.map((image, index) => (
                <div key={index} className="image-wrapper">
                  <Image
                    src={image?.url}
                    alt={image?.caption}
                    fill
                    style={{ objectFit: "cover" }}
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  ></Image>
                </div>
              ))
            ) : (
              <p className="no-images">{t("hotel.detail.noImagesAvailable")}</p>
            )}
          </div>
        </SlideFromRightModal>
        {isImgLightBoxOpen && (
          <ImageLightBox
            onClose={() => setIsImgLightBoxOpen(false)}
            images={images}
          />
        )}
      </div>
    </div>
  );
}

export default ImageGallery;
