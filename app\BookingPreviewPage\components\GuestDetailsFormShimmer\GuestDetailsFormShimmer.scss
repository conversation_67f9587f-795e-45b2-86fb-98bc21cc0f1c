@use "/styles/variable" as *;
@use "sass:color";

.guest-details-form-shimmer-container {
  @media (max-width: $breakpoint-md) {
    display: none;
  }
  .guest-details-form-shimmer {
    width: 100%;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 15px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

    //shimmer animation for all blocks
    .shimmer {
      background: linear-gradient(90deg, #f5f5f5 25%, #f5f5f5 50%, #eaeaea 75%);
      background-size: 200% 100%;
      border-radius: 4px;
      animation: shimmer 1.5s ease infinite;
    }

    &__header {
      width: 35%;
      height: 25px;
      margin-bottom: 15px;
      @extend .shimmer;
    }

    &__input-field-container {
      width: 100%;

      form {
        display: flex;
        flex-direction: column;
        row-gap: 20px;

        .radio-group {
          width: 100%;
          display: flex;
          flex-direction: row;
          column-gap: 30px;

          .radio-button-label {
            display: flex;
            flex-direction: row;
            column-gap: 2px;

            .radio-button {
              width: 20px;
              height: 20px;
              border-radius: 50% !important;
              @extend .shimmer;
            }

            .label {
              width: 60px;
              height: 20px;
              @extend .shimmer;
            }
          }
        }

        .form-row {
          width: 100%;
          display: flex;
          flex-direction: row;
          column-gap: 10px;

          .input-box {
            .input-container {
              height: 35px;
              width: 200px;
              padding: 0 15px;
              border-radius: 8px !important;
              box-sizing: border-box;
              @extend .shimmer;
            }
          }

          .country-code-input-box {
            display: flex;
            flex-direction: row;
            align-items: center;
            column-gap: 10px;

            .country-code-input {
              width: 70px;
              height: 30px;
              padding: 12px 10px;
              border-radius: 8px !important;

              @extend .shimmer;
            }

            .input-box-phone {
              border-radius: 10px;
              box-sizing: border-box;
              flex: 1;

              .input-container {
                height: 35px;
                width: 200px;
                padding: 0 15px;
                border-radius: 8px !important;
                box-sizing: border-box;
                @extend .shimmer;
              }
            }
          }

          .input-box-select {
            .input-container {
              height: 35px;
              width: 200px;
              margin: 8px 0 10px 0;
              padding: 0 15px;
              border-radius: 8px !important;
              box-sizing: border-box;
              @extend .shimmer;
            }
          }
        }
      }
    }
  }
}

//animation for shimmer
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
