{"name": "kindali-b2c-hotel-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "axios": "^1.8.4", "dompurify": "^3.2.5", "i18next-resources-to-backend": "^1.2.1", "leaflet": "^1.9.4", "lucide-react": "^0.503.0", "next": "15.1.7", "next-i18next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-i18next": "^15.5.1", "react-leaflet": "^5.0.0", "sass": "^1.87.0", "sonner": "^2.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}