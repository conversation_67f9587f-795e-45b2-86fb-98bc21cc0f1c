"use client";
import React from "react";
import "./GuestDetailsFormShimmer.scss";

function GuestDetailsFormShimmer() {
  return (
    <div className="guest-details-form-shimmer-container">
      <div className="guest-details-form-shimmer">
        <div className="guest-details-form-shimmer__header"></div>

        <div className="guest-details-form-shimmer__input-field-container">
          <form>
            <div className="radio-group">
              <div className="radio-button-label">
                <div className="radio-button"></div>

                <span className="label"></span>
              </div>

              <div className="radio-button-label">
                <div className="radio-button"></div>

                <span className="label"></span>
              </div>

              <div className="radio-button-label">
                <div className="radio-button"></div>

                <span className="label"></span>
              </div>
            </div>

            <div className="form-row">
              <div className="input-box wf-50">
                <div className="input-container"></div>
              </div>

              <div className="input-box wf-50">
                <div className="input-container"></div>
              </div>
            </div>
            <div className="form-row">
              <div className="input-box wf-50">
                <div className="input-container"></div>
              </div>

              <div className="country-code-input-box wf-50">
                <div className="country-code-input"></div>

                <div className="input-box-phone">
                  <div className="input-container"></div>
                </div>
              </div>
            </div>

            <div className="form-row">
              <div
                className={`input-box-select wf-50 
                    }`}
              >
                <div className="input-container">
                  <div className="input"></div>
                </div>
              </div>

              <div
                className={`input-box-select wf-50 
                    }`}
              >
                <div className="input-container">
                  <div className="input"></div>
                </div>
              </div>
              
            </div>

           
          </form>
        </div>
      </div>
    </div>
  );
}

export default GuestDetailsFormShimmer;
