"use client";
import React, { ChangeEvent, useState } from "react";
import "./SpecialRequest.scss";

function SpecialRequest() {
  const [request, setRequest] = useState<string>("");
  const [showRequstForm, setShowRequestForm] = useState<boolean>(false);
  const maxLength = 100;
  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    if (e.target.value.length <= maxLength) {
      setRequest(e.target.value);
    }
  };

  const handleDismiss = () => {
    setShowRequestForm(false);
  };

  return (
    <div className="special-request">
      <h6 className="special-request__header">Special Request</h6>

      <p className="special-request__description">
        We will forward your request to the hotel. Please note that this is
        subject to availability and based on the hotel policies
      </p>

      <form
        className={`special-request__input-charCount-buttons ${
          showRequstForm ? "show" : ""
        }`}
      >
        <div className="input-charCount">
          <textarea
            className="input"
            onChange={handleInputChange}
            value={request}
            placeholder="Enter Request"
            maxLength={maxLength}
            rows={3}
          />

          <p className="charCount">
            <span className="count">{`${request === "" ? "0" : request.length}/${maxLength}`}</span>
          </p>
        </div>

        <div className="buttons">
          <button type="button" className="special-request__button">
            Submit Request
          </button>
          <button
            type="button"
            onClick={handleDismiss}
            className="special-request__button"
          >
            Dismiss
          </button>
        </div>
      </form>

      {!showRequstForm && (
        <button
          className="special-request__button"
          onClick={() => setShowRequestForm(true)}
        >
          Add Request
        </button>
      )}
    </div>
  );
}

export default SpecialRequest;
