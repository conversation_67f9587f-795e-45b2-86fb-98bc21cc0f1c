"use client";
import React from 'react';
import { Ship } from 'lucide-react';

const CruisePage = () => {
  return (
    <div className="flex items-center justify-center overflow-hidden" style={{height: '90vh', background: 'linear-gradient(to bottom right, #f8fafc, #e2e8f0)', paddingTop: '80px'}}>
      <div className="text-center px-4">
        {/* Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 rounded-full flex items-center justify-center mx-auto shadow-lg" style={{background: 'linear-gradient(to right, #003b95, #006ce4)'}}>
            <Ship className="w-12 h-12 text-white" />
          </div>
        </div>

        {/* Main Heading */}
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          <span className="bg-clip-text text-transparent" style={{background: 'linear-gradient(to right, #003b95, #006ce4)', WebkitBackgroundClip: 'text'}}>
            Cruise
          </span>
        </h1>

        {/* Coming Soon Message */}
        <p className="text-xl md:text-2xl text-gray-600 mb-8">
          Coming Soon
        </p>

        {/* Back to Home */}
        <a
          href="/"
          className="inline-flex items-center font-semibold transition-colors duration-300 text-lg"
          style={{color: '#003b95'}}
        >
          ← Back to Home
        </a>
      </div>
    </div>
  );
};

export default CruisePage;
