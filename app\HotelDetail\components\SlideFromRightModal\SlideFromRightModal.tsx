"use client";
import React from "react";
import './SlideFromRightModal.scss'

interface SlideFromRightModalProps {
  isOpen: boolean;
  handleClose: () => void;
  children: React.ReactNode;
  title?: string;
  component_name? : string;
}

const SlideFromRightModal: React.FC<SlideFromRightModalProps> = ({ isOpen, handleClose, children, title, component_name }) => {
  return (
    <div onClick={handleClose} className={`slide-from-right-modal ${isOpen ? "show" : ""}`}>
        
      <div className={`modal-content ${component_name === "CancellationPolicy" ? 'cancellation-policy-modal' : ''}`} onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
           <div className="close-btn" onClick={handleClose}>
            <i className="fa-solid fa-xmark"></i>
          </div>
          {title && <h3>{title}</h3>}
       
        </div>

        <div className="content">{children}</div>
      </div>
    </div>
  );
};

export default SlideFromRightModal;
