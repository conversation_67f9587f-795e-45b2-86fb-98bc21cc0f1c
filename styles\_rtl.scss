// RTL-specific styles
@use './_variable.scss' as *;

// Apply RTL-specific styles when the html dir is set to rtl
html[dir="rtl"] {
  // Text alignment
  text-align: right;

  .text-left {
    text-align: right !important;
  }

  .text-right {
    text-align: left !important;
  }

  // Margins and paddings
  .ml-1, .ml-2, .ml-3, .ml-4, .ml-5, .ml-6, .ml-8, .ml-10, .ml-12 {
    margin-left: 0 !important;
  }

  .mr-1, .mr-2, .mr-3, .mr-4, .mr-5, .mr-6, .mr-8, .mr-10, .mr-12 {
    margin-right: 0 !important;
  }

  .pl-1, .pl-2, .pl-3, .pl-4, .pl-5, .pl-6, .pl-8, .pl-10, .pl-12 {
    padding-left: 0 !important;
  }

  .pr-1, .pr-2, .pr-3, .pr-4, .pr-5, .pr-6, .pr-8, .pr-10, .pr-12 {
    padding-right: 0 !important;
  }

  // Flexbox direction
  .flex-row {
    flex-direction: row-reverse !important;
  }

  // Border radius adjustments
  .rounded-l {
    border-radius: 0 !important;
    border-top-right-radius: 0.25rem !important;
    border-bottom-right-radius: 0.25rem !important;
  }

  .rounded-r {
    border-radius: 0 !important;
    border-top-left-radius: 0.25rem !important;
    border-bottom-left-radius: 0.25rem !important;
  }

  // Font adjustments for Arabic text
  body {
    letter-spacing: 0;
    line-height: 1.6;
  }

  // Directional properties
  .header .top-bar .right-section {
    flex-direction: row-reverse;
  }

  // Margins and paddings
  .header .top-bar .dropdown .dropdown-button i {
    margin-left: 0;
    margin-right: 4px;
  }

  // Floats
  .float-left {
    float: right !important;
  }

  .float-right {
    float: left !important;
  }

  // Borders
  .border-left {
    border-left: none;
    border-right: 1px solid;
  }

  .border-right {
    border-right: none;
    border-left: 1px solid;
  }

  // Icons and arrows
  .fa-angle-right:before {
    content: "\f104"; // Left arrow
  }

  .fa-angle-left:before {
    content: "\f105"; // Right arrow
  }

  // Form elements
  input, textarea {
    text-align: right;
  }

  // Navigation
  .navigation .nav-container {
    flex-direction: row-reverse;
  }

  // Override dropdown last-item positioning in RTL mode
  .header .top-bar .dropdown .dropdown-content.last-item {
    right: auto !important;
    left: 0 !important;
  }

  // Current location icon positioning
  .relative input + button.absolute,
  .relative input + button[class*="absolute"],
  .rtl-location-icon {
    right: auto !important;
    left: 0 !important;
  }

  // Material icons and other icon adjustments
  .material-icons.mr-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  // Fix for location icon in search bar
  button[disabled] .material-icons.mr-2,
  button:not([disabled]) .material-icons.mr-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  // Specific fix for the location icon button
  .rtl-location-icon {
    padding-right: 0 !important;
    padding-left: 0.25rem !important;
  }

  // Traveler selector container RTL fixes
  .traveler-selector-container {
    border: 1px solid $border-color !important;

    .traveler-control {
      .label {
        text-align: right;
      }

      .control-buttons {
        button:first-child {
          margin-right: 0;
          margin-left: 0.5rem;
        }

        button:last-child {
          margin-left: 0;
          margin-right: 0.5rem;
        }
      }
    }
  }

  // Ensure dropdown has proper border in RTL mode
  .travelerDropdown {
    border: 1px solid $border-color !important;
  }

  /* We're now handling the RTL border in the module.scss file */
}
