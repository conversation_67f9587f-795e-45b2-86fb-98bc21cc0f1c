@use "/styles/variable" as *;
@use "/styles/zIndex" as *;
@use "sass:color";

.photo-gallery-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: z-index(modal);

  &__container {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 1400px;
    //height: 90%;
    height: 90dvh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  &__header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    justify-content: space-between;
  }

  &__go-to-prevpage-btn {
     background: none;
    border: none;
    color: #1a1a1a;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;

    .fa-arrow-left {
      font-size: 1.3rem;
      margin-right: 5px;
    }
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    //flex-grow: 1;
    margin-right: 10px;
  }

  &__reserve-btn {
    background-color: $primary-color;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin-right: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;

    &:hover {
      background-color: color.adjust($primary-color, $lightness: -15%);
    }
  }

  &__close-btn {
    background: none;
    border: none;
    color: #1a1a1a;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;

    .fa-xmark {
      font-size: 1.4rem;
      font-weight: 600;
      margin-left: 5px;
    }
  }

  &__tabs {
    display: flex;
    overflow-x: auto;
    padding-bottom: 16px;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }
  }

  &__tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 0 0 16px;
    min-width: 100px;
    cursor: pointer;
    border-bottom: 3px solid transparent;

    &-image {
      width: 112px;
      height: 80px;
      overflow: hidden;
      border-radius: 4px;
      position: relative;
      border: 2px solid transparent;
      transition: border-color 0.2s ease;

      &.active {
        border-color: $primary-color;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    &-name {
      margin-top: 5px;
      font-size: 0.85rem;
      text-align: center;
      transition: color 0.2s ease;

      &.active {
        color: $primary-color;
      }
    }
  }

  &__content {
    display: flex;
    //flex-grow: 1;
    overflow: hidden;
    height: 100%;

    .image-showcase-main-container {
      width: calc(100% - 280px);
      margin-top: 16px;
      height: calc(100% - 16px);
  
    }
  }

  &__gallery {
    flex: 3;

    &-row {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 10px;
      height: 416px;
      padding: 20px;
      overflow-y: auto;

      // Webkit-based browsers (Chrome, Edge, Safari)
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent; // or a subtle color if needed
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2); // light thumb
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: rgba(0, 0, 0, 0.4); // darker on hover
      }

      // Firefox support
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    }

    &-item {
      flex: 1 1 calc((100% - 15px) / 4); // 3 gaps of 5px = 15px
      min-height: 200px;
      position: relative;
      //border-radius: 8px;
      overflow: hidden;

      .image-container {
        position: relative;
        width: 100%;
        height: 100%;

        // img {
        //   border-radius: 8px;
        // }
      }

      .image-info-btn {
        position: absolute;
        bottom: 10px;
        right: 10px;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.8);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 1rem;
        color: #444;
      }
    }
  }

  &__sidebar {
    //flex: 1;
    width: 280px;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
    background-color: #f8f9fa;

    // Webkit-based browsers (Chrome, Edge, Safari)
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent; // or a subtle color if needed
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2); // light thumb
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(0, 0, 0, 0.4); // darker on hover
    }

    // Firefox support
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
  }

  &__rating {
    display: flex;
    align-items: center;
    //background-color: $primary-color;
    color: white;
    padding: 16px;
    //border-radius: 8px;
    border-bottom: 1px solid #e0e0e0;

    .rating-score {
      width: 32px;
      height: 32px;
      background-color: $primary-color;
      border-radius: 6px 6px 6px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 700;
      margin-right: 8px;
    }

    .rating-text,
    .rating-reviews {
      line-height: 1.2;
    }

    .rating-text {
      .rating-label {
        font-size: 1.1rem;
        font-weight: 500;
        color: #000;
        font-size: 16px;
      }

      .rating-reviews {
        font-size: 12px;
        opacity: 0.9;
        color: #595959;
      }
    }
  }

  &__categories {
    padding: 16px;
    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 14px;
      font-weight: 500;
    }

    .category-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      flex-direction: column;
      align-items: stretch;
      row-gap: 5px;

      .category-name-score-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;

        .category-name {
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 14px;

          .category-icon {
            color: #008234;
            margin-left: 5px;
          }
        }

        .category-score {
          width: 30px;
          text-align: right;
          font-weight: 500;
          font-size: 14px;
        }
      }

      .category-score-container {
        width: 100%;
        height: 8px;
        background-color: #e0e0e0;
        border-radius: 4px;
        overflow: hidden;

        .category-score-bar {
          height: 100%;
          background-color: $primary-color;
          border-radius: 4px;

          &.highscore {
            background-color: #008234;
          }

          &.lowscore {
            background-color: #d4111e;
          }
        }
      }
    }

    .score-note-container {
      display: flex;
      flex-direction: column;
      align-items: start;
      gap: 5px;

      .score-note {
        //margin-top: 15px;
        font-size: 12px;
        color: #666;
        text-align: right;

        .highscore {
          color: #008234;
        }

        .lowscore {
          color: #d4111e;
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .photo-gallery-modal {
    &__content {
      flex-direction: column;
    }

    &__sidebar {
      border-left: none;
      border-top: 1px solid #e0e0e0;
    }

    &__nav {
      right: 20px;
      top: auto;
      bottom: 20px;
      transform: none;
      flex-direction: row;
    }

    &__gallery {
      &-row {
        flex-wrap: wrap;
      }

      &-item {
        flex: 0 0 calc(50% - 10px);

        &.large {
          flex: 0 0 calc(50% - 10px);
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .photo-gallery-modal {
    &__gallery {
      &-item {
        flex: 0 0 100%;

        &.large {
          flex: 0 0 100%;
        }
      }
    }

    &__header {
      flex-wrap: wrap;

      .photo-gallery-modal__title {
        width: 100%;
        margin-bottom: 10px;
      }
    }
  }
}
