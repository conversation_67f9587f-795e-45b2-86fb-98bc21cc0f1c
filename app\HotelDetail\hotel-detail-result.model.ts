export interface Root {
    data: Data
  }
  
  export interface Data {
    hotelDetailResponse: HotelDetailResponse
  }
  
  export interface HotelDetailResponse {
    locationId: number
    hotelId: number
    name: string
    locality: string
    city: string
    address: string
    userRating: string
    userRatingCategory: string
    starRating: number
    userRatingCount: number
    distanceFromSearchedEntity: string
    locationDetail: LocationDetail
    imageInfo: ImageInfo[]
    newImageInfo: NewImageInfo
    carouselImages: CarouselImage[]
    travelDetails: TravelDetails
    landmarks: Landmark[]
    aboutSection: AboutSection
    houseRulesAndPolicies: HouseRulesAndPolicies
    amenityDetails: AmenityDetails
    ratingView: RatingView
    wishlistInfo: WishlistInfo | null
    searchToken: string | null
    summary: string | null
    accommodationType: string
    providerId: number
    staticProvider: number
    hotelInfoToken: string
    hourlyStayInfo: HourlyStayInfo | null
    bestPriceGuarantee: boolean
    spotlightInfo: SpotlightInfo
    detailInsight: string
    guestImpressions: GuestImpression[]
    highlightInfo: HighlightInfo[]
    innvisionEnabled: boolean
    reviewCategories: ReviewCategories
    fareDetail: FareDetail
  }
  
  export interface LocationDetail {
    lat: number
    lon: number
  }
  
  export interface ImageInfo {
    category: Category
    imageList: ImageList[]
  }
  
  export interface Category {
    name: string
    imageCount: number
    categoryRank: number
  }
  
  export interface ImageList {
    pictureId: string
    url: string
    urlHigh: string | null
    urlMedium: string | null
    urlLow: string | null
    caption: string
    imageCategory: string
    rank: number
    srpRank: number | null
    detailRank: number | null
    categoryRank: number | null
    withinCategoryRank: number | null
  }
  
  export interface NewImageInfo {
    categories: string[]
    imageList: ImageList2[]
    aiImageData: boolean
  }
  
  export interface ImageList2 {
    pictureId: string
    url: string
    urlHigh: string
    urlMedium: string
    urlLow: string
    caption: string
    imageCategory: string
    rank: number | null
    srpRank: number
    detailRank: number
    categoryRank: number
    withinCategoryRank: number
  }
  
  export interface CarouselImage {
    pictureId: string
    url: string
    urlHigh: string
    urlMedium: string
    urlLow: string
    caption: string
    imageCategory: string
    rank: number | null
    srpRank: number
    detailRank: number
    categoryRank: number
    withinCategoryRank: number
  }
  
  export interface TravelDetails {
    travelDates: TravelDates
    roomCount: number
    adultCount: number
    childCount: number
    childAges: number[]
  }
  
  export interface TravelDates {
    checkinDate: string
    checkoutDate: string
    formattedCheckinDate: string
    formattedCheckoutDate: string
    epochCheckinDate: number
    epochCheckoutDate: number
    checkinTime: string | null
    checkoutTime: string | null
  }
  
  export interface Landmark {
    name: string
    distance: number
    unit: string
  }
  
  export interface AboutSection {
    data: Record<string, unknown> | null
    descriptionInfo: DescriptionInfo[]
  }
  
  export interface DescriptionInfo {
    title: string
    images: string[]
    description: string[]
  }
  
  export interface HouseRulesAndPolicies {
    checkinTime: string | null
    checkoutTime: string | null
    rules: Rule[]
  }
  
  export interface Rule {
    policyType: string
    heading: string
    subRules: SubRule[]
  }
  
  export interface SubRule {
    details: string[]
    subHeading?: string
  }
  
  export interface AmenityDetails {
    amenityCount: number
    amenities: Amenity[]
  }
  
  export interface Amenity {
    name: string
    amenityUrl: string
    amenityRank: number
    categoryName: string
    categoryUrl: string
    categoryRank: number
  }
  
  export interface RatingView {
    averageRating: string
    ratingCount: number
    staticProvider: number
    hotelFeaturesRating: HotelFeaturesRating
    hotelFeaturesRatingV2: HotelFeaturesRatingV2[]
    totalReviewCount: number
  }
  
  export interface HotelFeaturesRating {
    Cleanliness: string
    "Value for Money": string
    Staff: string
    "Room Comfort": string
    Location: string
    Food: string
  }
  
  export interface HotelFeaturesRatingV2 {
    title: string
    icon: string
    rating: string
    summary: string
    ratingInDoubleFormat: number
  }
  
  export interface SpotlightInfo {
    data: SpotlightData[]
    spotlightNameList: string[] | null
  }
  
  export interface SpotlightData {
    title: string
    description: string
    icon: string
  }
  
  export interface GuestImpression {
    title: string
    data: ImpressionData[]
  }
  
  export interface ImpressionData {
    icon: string
    impression: string
  }
  
  export interface HighlightInfo {
    pictureId: string
    url: string
    urlHigh: string
    urlMedium: string
    urlLow: string
    title: string
    caption: string
  }
  
  export interface ReviewCategories {
    categoryMap: CategoryMap
    subCategoryMap: SubCategoryMap[]
  }
  
  export interface CategoryMap {
    "All Reviews": number
    Group: number
    Couple: number
    Family: number
    Business: number
    "Solo Traveller": number
  }
  
  export interface SubCategoryMap {
    title: string
    values: string[]
  }
  
  export interface FareDetail {
    displayedBaseFare: number
    baseFare: number
    markUpFare: number
    markupDiscountPercent: string
    taxesAndFees: number
    totalPrice: number
    burnMoneyInfo: BurnMoneyInfo | null
    couponCode: string
    cashback: Cashback | null
    cashbackText: string | null
    instantDiscount: number
    totalDiscount: number
    totalPGAmount: number | null
    totalBookingAmount: number | null
    bankOfferTextIncluded: boolean
    offerText: OfferText
    taxAndChargeMap: TaxAndChargeMap
    discountMap: DiscountMap
    excludedChargeMap: ExcludedChargeMap
    discountViewType: string
    method: string
    totalBaseFare: number
    totalTaxesAndFees: number
    convenienceFee: number
    paymentType: string
    payAtHotelAmount: number | null
    offset: number
  }
  
  // New interfaces to replace 'any' types
  export interface WishlistInfo {
    id?: string
    name?: string
    count?: number
  }
  
  export interface HourlyStayInfo {
    available?: boolean
    minHours?: number
    maxHours?: number
    ratePerHour?: number
  }
  
  export interface BurnMoneyInfo {
    amount?: number
    currency?: string
  }
  
  export interface Cashback {
    amount: number
    currency: string
    description?: string
  }
  
  export interface OfferText {
    [key: string]: string
  }
  
  export interface TaxAndChargeMap {
    [key: string]: number
  }
  
  export interface DiscountMap {
    "Coupon Discount": number
    "Supplier Discount": number
    [key: string]: number
  }
  
  export interface ExcludedChargeMap {
    [key: string]: number
  }





  

  