@use "/styles/variable" as *;

.footer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding-bottom:15px;
  border-top: 3px solid $primary-color;

  .footer-language-switcher {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid rgba(177, 145, 118, 0.2);

    .language-options {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 10px;

      .language-option {
        background: none;
        border: 1px solid $primary-color;
        border-radius: 4px;
        padding: 5px 12px;
        font-size: 14px;
        color: #4c4c4c;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(177, 145, 118, 0.1);
        }

        &.active {
          background-color: $primary-color;
          color: white;
        }
      }
    }
  }

  .footer-items-list {
    width: 100%;
    padding: 15px 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;

    .footer-item {
      margin-right: 20px;
      margin-bottom: 25px;
      min-width: 95px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: $primary-color;
        margin: 10px 0px;
        padding-bottom: 8px;
        border-bottom: 2px solid rgba(177, 145, 118, 0.3);
      }

      ul {
        list-style: none;
        padding-left: 0;

        li {
          font-size: 14px;
          font-weight: 400;
          color: #4c4c4c;
          margin: 10px 0px;
          transition: all 0.3s ease;

          a {
            text-decoration: none;
            color: #4c4c4c;
            transition: all 0.3s ease;

            &:hover {
              // color: #76B1B1;
              // padding-left: 5px;
            }
          }
        }
      }
    }
  }

  .footer-partners-list {
    width: 100%;
    border-top: 1px solid rgba(177, 145, 118, 0.2);
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    padding: 20px 10px;
    margin-top: 10px;

    .listType2 {
      margin: 0 25px 15px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .listType1 {
      margin: 0 25px 0 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        border-radius: 5px;
        box-shadow: 0 2px 8px rgba(121, 35, 35, 0.1);
      }
    }

    .listType2 {
      p {
        font-size: 15px;
        font-weight: 500;
        color: $primary-color;
        margin: 15px 0;
        text-align: center;
      }

      .logos {
        display: flex;
        flex-direction: row;
        align-items: center;
        column-gap: 15px;
        flex-wrap: wrap;
        justify-content: center;

        img {
          filter: grayscale(30%);
          transition: all 0.3s ease;

          &:hover {
            filter: grayscale(0%);
            transform: scale(1.05);
          }
        }
      }
    }
  }

  // RTL support
  &.rtl {
    .footer-items-list {
      .footer-item {
        margin-right: 0;
        margin-left: 20px;

        ul {
          padding-right: 0;

          li {
            a {
              &:hover {
                padding-left: 0;
                padding-right: 5px;
              }
            }
          }
        }
      }
    }

    .footer-language-switcher {
      .language-options {
        .language-option {
          font-family: 'Manrope', sans-serif;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .footer-items-list {
      flex-direction: column;

      .footer-item {
        margin-right: 0;
        margin-left: 0;
        width: 100%;
      }
    }

    .footer-partners-list {
      flex-direction: column;
      align-items: center;
    }
  }
}