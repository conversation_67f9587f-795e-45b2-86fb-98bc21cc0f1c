@use "/styles/variable" as *;

.trip-planner-grid {
  padding-top: 16px;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 15px;

  &__header {
    font-size: 19px;
    font-weight: 700;
    padding: 0 16px;
  }
  &__cards-container {
    padding: 0 16px;
    display: flex;
    flex-wrap: wrap;

    @media (max-width: $breakpoint-xs) {
      flex-direction: column;
    }
  }

  &__card {
    max-width: 100px;
    margin: 12px 8px 16px 0;
    padding: 12px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    background: linear-gradient(to top, #ffffff, #f5eee7);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    cursor: pointer;

    @media (max-width: $breakpoint-xs) {
      max-width: 100%;
    }

    // Hover effect
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      border-color: rgba(0, 0, 0, 0.3);

      // Subtle shine effect
      &:after {
        content: "";
        position: absolute;
        top: -50%;
        left: -60%;
        width: 200%;
        height: 200%;
        background: rgba(255, 255, 255, 0.2);
        transform: rotate(30deg);
        transition: all 0.7s ease-in-out;
      }

      .fa-solid {
        transform: scale(1.1);
        color: darken($primary_color, 10%);
      }
    }

    p {
      font-size: 14px;
      font-weight: 700;
      margin-bottom: 0;
      transition: all 0.3s ease;
    }
  }

  &__card-image {
    margin-bottom: 10px;

    .fa-solid {
      color: $primary_color;
      font-size: 22px;
      transition: all 0.3s ease;
    }
  }
}
