import React, { useCallback, useEffect, useState } from "react";
import "./OffersSection.scss";
import Link from "next/link";
import axios from "axios";
import { Data } from "../../hotel-booking-offers.model";

const API_URL = "/data/hotelbookingoffers.json";

function OffersSection() {
  const [selectedPromo, setSelectedPromo] = useState<string>("IXISTAY");
  const [moreOffer, setMoreOffer] = useState<boolean>(false);
  const [hotelOffersResponse, setHotelOffersResponse] = useState<Data | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (couponCode: string) => {
    setSelectedPromo(couponCode);
  };

  const getOffers = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(API_URL);
      setHotelOffersResponse(response.data.data);
      setError(null);
    } catch (err) {
      console.error(err);
      setError("Failed to load offers");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    getOffers();
  }, [getOffers]);

  // Format currency in Indian Rupees
  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined) return "";
    return `₹${amount.toLocaleString("en-IN")}`;
  };

  // Get the offers to display based on moreOffer state
  const offersToDisplay = hotelOffersResponse?.offers 
    ? moreOffer 
      ? hotelOffersResponse.offers 
      : hotelOffersResponse.offers.slice(0, 3)
    : [];

  // Remove duplicate coupon codes to show unique offers
  const uniqueOffers = offersToDisplay.filter((offer, index, self) => 
    index === self.findIndex((o) => o.couponCode === offer.couponCode)
  );

  return (
    <div className="offers-section-container">
      <h6 className="offers-section-container__header">Offers For You</h6>

      <div className="offers-section-container__promo-input">
        <div className="input-container">
          <input type="text" id="promo-code" placeholder=" " required />
          <label htmlFor="promo-code">Have a promo code? Redeem here</label>
        </div>
      </div>

      <div className="offers-section-container__promo-selector">
        {isLoading && <p>Loading offers...</p>}
        {error && <p className="error-message">{error}</p>}
        
        {!isLoading && !error && uniqueOffers.map((promo, index) => (
          <div key={index} className="promo-selector-option">
            <div className="radio-button">
              <input
                type="radio"
                name="promo"
                value={promo.couponCode}
                onChange={() => handleChange(promo.couponCode)}
                checked={selectedPromo === promo.couponCode}
                className="promo-selector__input"
              />
              <span className="radio"></span>
            </div>
            <div className="promo-selector__content">
              <div className="promo-selector__header">
                <span className="promo-selector__title">{promo.couponCode}</span>
                <span className="promo-selector__discount">
                  {promo.displayedInstantDiscount 
                    ? formatCurrency(promo.displayedInstantDiscount)
                    : promo.displayedCashback 
                    ? formatCurrency(promo.displayedCashback)
                    : ""}
                </span>
              </div>
              <p
                className={`promo-selector__description ${
                  selectedPromo === promo.couponCode ? "checked" : ""
                }`}
              >
                {promo.applyMessage}
              </p>
              <Link href="#" className="promo-selector__link">
                Know More
              </Link>
            </div>
          </div>
        ))}

        {!isLoading && !error && hotelOffersResponse?.offers && hotelOffersResponse.offers.length > 3 && (
          <div className="promo-selector-button-field">
            <div
              className="promo-selector-button"
              onClick={() => setMoreOffer((prev) => !prev)}
            >
              <span className="label">
                View {moreOffer ? "Less" : "All"} Offers
              </span>
              <span className="icon">
                <span className="material-icons arrowDwn">
                  {moreOffer ? "keyboard_arrow_up" : "keyboard_arrow_down"}
                </span>
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default OffersSection;