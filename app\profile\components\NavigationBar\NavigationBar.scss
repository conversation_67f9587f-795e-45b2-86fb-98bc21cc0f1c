@use "/styles/variable" as *;

.navigation-bar {
  width: 100%;
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);

  &__nav-item {
    width: calc(100% / 3);
    padding: 8px 16px;
    border-bottom: 2px solid transparent;
    transition: border-color 0.1s ease-out;

    span {
      display: block;
      text-align: center;
      font-size: 14px;
      font-weight: 700;
      cursor: pointer;
      border-radius: 9999px;
      transition: color 0.2s ease, background-color 0.2s ease;

      @media (max-width: $breakpoint-xs) {
        font-size: 13px;
        
      }
      &:hover {
        background-color: #F5EEE7;
        color: $primary_color;
      }
    }

    &.active {
      border-color: $primary_color;
    }
  }
}
