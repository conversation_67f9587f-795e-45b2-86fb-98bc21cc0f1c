import { 
    SearchInitApiResponse, 
    SearchInitHotel, 
    Hotel, 
    HotelListResponse,
    Amenity,
    ImageInfo,
    RoomInfo,
    FareDetail,
    Offer
} from "../hotel-search-result.model";

/**
 * Helper functions for converting Search Init API response data to frontend Hotel model format
 */

// Helper function to ensure image URL is absolute
const ensureAbsoluteImageUrl = (url: string): string => {
    if (!url) return url;

    // If URL is already absolute, return as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // If URL starts with //, add https:
    if (url.startsWith('//')) {
        return `https:${url}`;
    }

    // If URL is relative, you might need to add a base URL here
    // For now, return as is and let the browser handle it
    return url;
};

/**
 * Convert a single SearchInitHotel to Hotel format
 * @param searchInitHotel - The hotel data from Search Init API
 * @returns Converted Hotel object for frontend use
 */
export const convertSearchInitHotelToHotel = (searchInitHotel: SearchInitHotel): Hotel => {
    // Convert amenities array to Amenity objects
    const amenities: Amenity[] = searchInitHotel.amenities.map(amenityName => ({
        name: amenityName,
        amenityUrl: "" // Default empty URL since not provided in API
    }));

    // Convert heroImage to ImageInfo array (only use heroImage field)
    const imageInfoList: ImageInfo[] = searchInitHotel.heroImage ? [{
        pictureId: "1",
        url: ensureAbsoluteImageUrl(searchInitHotel.heroImage),
        caption: searchInitHotel.name,
        imageCategory: "hero",
        rank: 1
    }] : [];

    // Convert roomDetails
    const roomDetails: RoomInfo[] = searchInitHotel.roomDetails ? [{
        type: searchInitHotel.roomDetails.type || "Standard",
        bedroom: parseInt(searchInitHotel.roomDetails.bedroom || "1"),
        livingRoom: parseInt(searchInitHotel.roomDetails.livingRoom || "0"),
        bathroom: parseInt(searchInitHotel.roomDetails.bathroom || "1"),
        size: searchInitHotel.roomDetails.size || "Standard",
        bed: searchInitHotel.roomDetails.bed || "Double"
    }] : [];

    // Convert fareDetail - take the first one if multiple exist
    const fareDetail: FareDetail = searchInitHotel.fareDetail.length > 0 ? {
        displayedBaseFare: searchInitHotel.fareDetail[0].displayedBaseRate || 0,
        totalPrice: searchInitHotel.fareDetail[0].totalPrice || 0,
        board_basis: searchInitHotel.fareDetail[0].board_basis,
        currency_code: searchInitHotel.fareDetail[0].currency_code,
        displayedBaseRate: searchInitHotel.fareDetail[0].displayedBaseRate,
        offer_description: searchInitHotel.fareDetail[0].offer_description,
        offer_title: searchInitHotel.fareDetail[0].offer_title,
        refundable: searchInitHotel.fareDetail[0].refundable,
        taxes: searchInitHotel.fareDetail[0].taxes
    } : {
        displayedBaseFare: 0,
        totalPrice: 0,
        board_basis: "nomeal",
        currency_code: "INR",
        displayedBaseRate: 0,
        offer_description: null,
        offer_title: null,
        refundable: false,
        taxes: 0
    };

    // Create offer from fareDetail if available
    const offer: Offer = {
        couponCode: searchInitHotel.fareDetail[0]?.offer_title || "",
        instantDiscount: 0, // Not available in current API structure
        displayedInstantDiscount: 0, // Not available in current API structure
        cashback: 0, // Not available in current API structure
        displayedCashback: 0, // Not available in current API structure
        applyMessage: searchInitHotel.fareDetail[0]?.offer_description || ""
    };

    // Derive user rating category from rating value
    const getUserRatingCategory = (rating: number): string => {
        if (rating >= 4.5) return "Excellent";
        if (rating >= 4.0) return "Very Good";
        if (rating >= 3.5) return "Good";
        if (rating >= 3.0) return "Fair";
        return "Poor";
    };

    // Calculate taxes and charges as difference between total price and base fare
    const baseFare = searchInitHotel.fareDetail[0]?.displayedBaseRate || 0;
    const totalPrice = searchInitHotel.fareDetail[0]?.totalPrice || 0;
    const calculatedTaxesAndCharges = totalPrice - baseFare;

    // Create special offerings based ONLY on booking-related benefits from API
    const getSpecialOfferings = (): string[] => {
        const offeringsSet = new Set<string>(); // Use Set to prevent duplicates

        console.log('🔍 DEBUG: searchInitHotel.fareDetail:', searchInitHotel.fareDetail);
        console.log('🔍 DEBUG: searchInitHotel.amenities:', searchInitHotel.amenities);

        // Check for free cancellation from fareDetail
        if (searchInitHotel.fareDetail[0]?.refundable === true) {
            console.log('✅ Adding Free Cancellation from refundable flag');
            offeringsSet.add("Free Cancellation");
        }

        // Check amenities for booking-related benefits only
        const amenityList = searchInitHotel.amenities.map(a => a.toLowerCase());
        console.log('🔍 DEBUG: amenityList (lowercase):', amenityList);

        // Check for cancellation in amenities (but don't duplicate if already added from refundable)
        const hasCancellationInAmenities = amenityList.some(amenity =>
            amenity.includes("free cancellation") ||
            amenity.includes("cancellation") ||
            amenity.includes("refundable")
        );
        if (hasCancellationInAmenities) {
            console.log('⚠️ Found cancellation in amenities, but checking if already added...');
            if (!offeringsSet.has("Free Cancellation")) {
                console.log('✅ Adding Free Cancellation from amenities');
                offeringsSet.add("Free Cancellation");
            } else {
                console.log('❌ Skipping Free Cancellation - already added from refundable flag');
            }
        }

        // WiFi - only if it's explicitly "free"
        const hasWifi = amenityList.some(amenity =>
            amenity.includes("free wi-fi") ||
            amenity.includes("free wifi") ||
            amenity.includes("complimentary wifi")
        );
        if (hasWifi) {
            console.log('✅ Adding Free WiFi');
            offeringsSet.add("Free WiFi");
        }

        // Breakfast - only if it's explicitly "free" or "included"
        const hasBreakfast = amenityList.some(amenity =>
            amenity.includes("breakfast included") ||
            amenity.includes("free breakfast") ||
            amenity.includes("complimentary breakfast")
        );
        if (hasBreakfast) {
            console.log('✅ Adding Breakfast Included');
            offeringsSet.add("Breakfast Included");
        }

        // Parking - only if it's explicitly "free"
        const hasParking = amenityList.some(amenity =>
            amenity.includes("free parking") ||
            amenity.includes("complimentary parking")
        );
        if (hasParking) {
            console.log('✅ Adding Free Parking');
            offeringsSet.add("Free Parking");
        }

        const finalOfferings = Array.from(offeringsSet);
        console.log('🎯 Final special offerings:', finalOfferings);
        return finalOfferings;
    };

    return {
        specialAmenities: [] as [string], // Keep empty to avoid duplication
        locationId: searchInitHotel.id,
        hotelId: parseInt(searchInitHotel.hotelId),
        offer: offer,
        name: searchInitHotel.name,
        address: searchInitHotel.address,
        city: searchInitHotel.city,
        userRating: searchInitHotel.userRating.toString(),
        userRatingCategory: searchInitHotel.userRatingCategoty || getUserRatingCategory(searchInitHotel.userRating),
        starRating: parseInt(searchInitHotel.starRating),
        userRatingCount: 0, // Not provided by API
        imageInfoList: imageInfoList,
        roomDetails: roomDetails,
        comfortRating: searchInitHotel.comfortRating || 0,
        taxesAndCharges: calculatedTaxesAndCharges,
        category: searchInitHotel.category,
        hotelType: searchInitHotel.HotelType,
        accommodationType: searchInitHotel.HotelType, // Map HotelType to accommodationType
        topOfferings: getSpecialOfferings(),
        roomsCountLeft: searchInitHotel.roomCountLeft || 0,
        distanceFromSearchedEntity: "", // Not provided by API
        fomoTags: [], // Not provided by API
        amenities: amenities,
        geoLocationInfo: searchInitHotel.geoLocationInfo,
        fareDetail: fareDetail,
        isVisible: searchInitHotel.isVisible,
        about: searchInitHotel.about || ""
    };
};

/**
 * Convert SearchInitApiResponse to HotelListResponse format
 * @param searchInitResponse - The complete API response from Search Init
 * @returns Converted HotelListResponse for frontend use
 */
export const convertSearchInitResponseToHotelListResponse = (searchInitResponse: SearchInitApiResponse): HotelListResponse => {
    // Extract all hotels from all batches
    const allHotels: SearchInitHotel[] = [];

    searchInitResponse.batches.forEach(batch => {
        allHotels.push(...batch.hotels);
    });

    const convertedHotels = allHotels.map((hotel: SearchInitHotel) =>
        convertSearchInitHotelToHotel(hotel)
    );

    return {
        data: {
            result: {
                inventoryInfoList: convertedHotels
            }
        }
    };
};

/**
 * Convert array of SearchInitHotel to array of Hotel
 * @param searchInitHotels - Array of hotel data from Search Init API
 * @returns Array of converted Hotel objects
 */
export const convertHotelArray = (searchInitHotels: SearchInitHotel[]): Hotel[] => {
    return searchInitHotels.map(hotel => convertSearchInitHotelToHotel(hotel));
};

/**
 * Extract hotels from SearchInitApiResponse and convert to Hotel array
 * @param searchInitResponse - The complete API response from Search Init
 * @returns Array of converted Hotel objects
 */
export const extractAndConvertHotels = (searchInitResponse: SearchInitApiResponse): Hotel[] => {
    // Extract all hotels from all batches
    const allHotels: SearchInitHotel[] = [];

    searchInitResponse.batches.forEach(batch => {
        allHotels.push(...batch.hotels);
    });

    return convertHotelArray(allHotels);
};

/**
 * Get batch information from the API response
 * @param searchInitResponse - The API response from Search Init
 * @returns Batch information object
 */
export const getBatchInfo = (searchInitResponse: SearchInitApiResponse) => {
    const latestBatch = searchInitResponse.batches[searchInitResponse.batches.length - 1];

    return {
        batchNumber: latestBatch?.batch_info.batch_number || 1,
        batchSize: latestBatch?.batch_info.batch_size || 0,
        totalBatches: searchInitResponse.total_batches,
        isLastBatch: latestBatch?.batch_info.is_last_batch || false,
        batchStatus: latestBatch?.batch_info.batch_status || 'Processing',
        totalHotelsFound: latestBatch?.total_hotels_found || 0
    };
};

/**
 * Get search progress percentage based on batch information
 * @param searchInitResponse - The API response from Search Init
 * @returns Progress percentage (0-100)
 */
export const getSearchProgress = (searchInitResponse: SearchInitApiResponse): number => {
    const batchInfo = getBatchInfo(searchInitResponse);
    if (!batchInfo || batchInfo.totalBatches === 0) return 0;

    return Math.round((batchInfo.batchNumber / batchInfo.totalBatches) * 100);
};

/**
 * Check if there are more batches to load
 * @param searchInitResponse - The API response from Search Init
 * @returns boolean indicating if more batches are available
 */
export const hasMoreBatches = (searchInitResponse: SearchInitApiResponse): boolean => {
    const latestBatch = searchInitResponse.batches[searchInitResponse.batches.length - 1];
    return !latestBatch?.batch_info.is_last_batch;
};

/**
 * Get the next skip value for pagination
 * @param currentSkip - Current skip value
 * @returns Next skip value
 */
export const getNextSkip = (currentSkip: number): number => {
    return currentSkip + 1;
};
