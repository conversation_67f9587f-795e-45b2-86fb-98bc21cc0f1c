# OtpInput Component

A reusable OTP (One-Time Password) input component for React applications.

## Features

- Configurable number of input fields
- Auto-focus on the next input after entering a digit
- Keyboard navigation with arrow keys
- Backspace support for deleting digits
- Paste support for pasting the entire OTP
- Responsive design
- RTL support
- Customizable styling
- Accessibility support

## Usage

```tsx
import OtpInput from '@/app/components/common/OtpInput';

// In your component
const [otp, setOtp] = useState(['', '', '', '']);

// Render the OtpInput component
<OtpInput
  length={4}
  value={otp}
  onChange={setOtp}
  autoFocus={true}
  className="custom-container-class"
  inputClassName="custom-input-class"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `length` | `number` | Required | Number of OTP input fields |
| `value` | `string[]` | Required | Array of OTP values |
| `onChange` | `(otp: string[]) => void` | Required | Callback function when OTP changes |
| `disabled` | `boolean` | `false` | Whether the inputs are disabled |
| `autoFocus` | `boolean` | `false` | Whether to auto-focus the first input on mount |
| `className` | `string` | `''` | Additional class name for the container |
| `inputClassName` | `string` | `''` | Additional class name for each input |

## Styling

The component comes with default styling in `OtpInput.scss`. You can customize the appearance by:

1. Overriding the default styles with your own CSS
2. Passing custom class names via `className` and `inputClassName` props

## Keyboard Navigation

- **Arrow Left/Right**: Navigate between inputs
- **Backspace**: Delete the current digit or move to the previous input if the current one is empty
- **Paste**: Paste a complete OTP code into the inputs

## Accessibility

- Each input has an appropriate `aria-label`
- Keyboard navigation is fully supported
- Focus states are clearly visible

## Example

```tsx
import React, { useState } from 'react';
import OtpInput from '@/app/components/common/OtpInput';

const VerificationForm = () => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  
  const handleVerify = () => {
    const otpString = otp.join('');
    // Verify the OTP
    console.log('Verifying OTP:', otpString);
  };
  
  return (
    <div className="verification-form">
      <h2>Enter Verification Code</h2>
      <p>We've sent a 6-digit code to your phone</p>
      
      <OtpInput
        length={6}
        value={otp}
        onChange={setOtp}
        autoFocus={true}
      />
      
      <button 
        onClick={handleVerify}
        disabled={otp.join('').length !== 6}
      >
        Verify
      </button>
    </div>
  );
};

export default VerificationForm;
```
