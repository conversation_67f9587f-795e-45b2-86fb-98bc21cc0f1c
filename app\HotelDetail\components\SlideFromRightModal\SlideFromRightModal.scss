@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as *;

.slide-from-right-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: z-index(modal);

  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease; // Background fades in

  @media (max-width: $isMobile) {
      top: 90px;
      background: none;
      
    }

  &.show {
    opacity: 1;
    pointer-events: auto; // Can be clicked
  }
  .modal-content {
    background: white;
    width: 55%;
    height: 100vh;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;

    transform: translateX(100%); // Start off-screen (right)
    transition: transform 0.5s ease-in-out; // Slide-in effect

    @media(max-width: $isMobile) {
      width: 100%;
      transform:  translateY(100%);
      
    }
    &.cancellation-policy-modal{
      max-width: 440px;
      width: 100%;
    }

    .modal-header {
     height: 50px;
    width: 100%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
   padding: 25px 10px;

     border-bottom: 1px solid rgba(0, 0, 0, 0.1);
 

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: black;


        @media (max-width: $isMobile) {
          font-size: 16px;
          
        }
      }

     .close-btn {
      position: absolute;
      left: 10px;
      height: 30px;
      width: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border-radius: 50%;
      border: 1px solid $primary-color;
      color: $primary-color;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: color.adjust(#fff, $lightness: -5%);
      }
    }
    }

    .content{
      height: 100vh;
          overflow-y: auto;
    }

 
  }

  &.show .modal-content {
    transform: translateX(0); // Slides into view when modal opens
  
    @media (max-width: $isMobile) {
      transform: translateY(0);
      
    }
  }
}
