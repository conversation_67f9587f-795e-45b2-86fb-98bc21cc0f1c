@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.image-light-box-container {
  position: fixed;
  inset: 0;
  z-index: z-index(modal);
  background-color: rgba(0, 0, 0, 0.9); /* Darker background */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 70px 0 0 0;
  
  .close-btn {
    position: absolute;
    top: 15px;
    left: 25px;
    cursor: pointer;
    
    .fa-xmark {
      font-size: 18px;
      color: white;
    }
  }
  
  .image-light-box {
    width: 80%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .image-counter {
      position: absolute;
      bottom: -30px;
      color: white;
      font-size: 14px;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 5px 12px;
      border-radius: 15px;
      z-index: z-index(base);

        display: none;

      @media (max-width: $isMobile) {
        display: block;
      }
    }
    
    .image {
      width: 90vw;
      max-width: 750px;
      height: 70vh;
      max-height: 450px;
      position: relative;
      margin-bottom: 20px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      
      transition: transform 1.2s cubic-bezier(0.25, 1, 0.3, 1), opacity 1.2s ease-in-out;
      opacity: 1;
      
      &.no-transition {
        transition: none !important;
        animation: none !important;
      }
    }
    
    .image.next {
      animation: slideInFromRight 1.2s cubic-bezier(0.25, 1, 0.3, 1) forwards;
    }
    
    .image.prev {
      animation: slideInFromLeft 1.2s cubic-bezier(0.25, 1, 0.3, 1) forwards;
    }
    
    @keyframes slideInFromRight {
      from {
        transform: translateX(100%);
        opacity: 0.3;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    @keyframes slideInFromLeft {
      from {
        transform: translateX(-100%);
        opacity: 0.3;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: white;
      color: #0770e4;
      border-radius: 50%;
      border: none;
      height: 40px;
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      cursor: pointer;
      transition: opacity 0.3s, background 0.3s;
      z-index: z-index(base);
      
      &:hover {
        background: #ddd;
      }
      
      &.left {
        left: 10px;
      }
      
      &.right {
        right: 10px;
      }
    }
  }
  
  .thumbnail-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 20px;
    align-items: center;
    
    .thumbnail {
      width: 85px;
      height: 65px;
      border-radius: 5px;
      position: relative;
      transition: transform 0.3s ease;
      
      img {
        border-radius: 5px;
      }
      
      &:hover {
        transform: scale(1.1);
      }
      
      &.active {
        border: 3px solid white;
        box-shadow: 0px 0px 4px white;
      }
    }
  }
  
  /* Mobile styles */
  .image-light-box.mobile {
    width: 100%;
    height: 100%;
    padding: 0;
    
    .mobile-scroll-container {
      display: flex;
      overflow-x: auto;
      width: 100%;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none; /* For Firefox */
      scroll-snap-type: x mandatory;
      height: 75vh;
      
      &::-webkit-scrollbar {
        display: none; /* For Chrome, Safari, and Opera */
      }
      
      .mobile-image-item {
        flex: 0 0 100%;
        width: 100vw;
        height: 100%;
        position: relative;
        scroll-snap-align: center;
        scroll-snap-stop: always;
      }
    }
    
    .image-counter {
      bottom: 20px;
    
    }
  }
}

/* Responsive Media Queries */
@media (max-width: 950px) {
  .image-light-box-container {
    padding: 60px 0 0 0;
    
    .image-light-box {
      width: 100%;
    }
  }
}