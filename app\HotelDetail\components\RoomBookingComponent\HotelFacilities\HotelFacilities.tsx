"use client";
import React, { forwardRef } from 'react';
import './HotelFacilities.scss';
import { useTranslation } from '@/app/hooks/useTranslation';
import { AmenityDetails } from '@/app/HotelDetail/hotel-detail-result.model';


type Amenity = {
  name: string;
  amenityUrl: string;
  amenityRank: number;
  categoryName: string;
  categoryUrl: string;
  categoryRank: number;
};

interface FacilityItem {
  icon: string;
  title: string;
  items: string[];
}

interface HotelFacilitiesProps {
  facilities?: FacilityItem[][];
  amenityDetails?: AmenityDetails;
}

const HotelFacilities = forwardRef<HTMLDivElement, HotelFacilitiesProps>(({amenityDetails}, ref) => {
  const { t } = useTranslation();
  // Use provided facilities or fallback to default data
  // const facilityCategories = props.facilities || [
  //   [
  //     { icon: "fa-bed", title: "Room amenities", items: ["Air-conditioned rooms", "Daily housekeeping", "Fan", "Free drinking water", "Hair dryer", "Ironing facilities", "Study table", "Tea/Coffee maker", "Safety Deposit Box"] },
  //     { icon: "fa-bath", title: "Bathroom", items: ["Shower"] },
  //     { icon: "fa-car", title: "Transfers and transport", items: ["Onsite car parking", "Airport transfer", "Car rental service", "Shuttle service", "Taxi/Cab service"] },
  //     { icon: "fa-key", title: "Access", items: ["24*7 Front Desk", "Non-smoking rooms", "Private Check-in/out"] },
  //     { icon: "fa-shield-halved", title: "Safety and security", items: ["24*7 Security", "CCTV in common areas", "Fire extinguisher"] }
  //   ],
  //   [
  //     { icon: "fa-wifi", title: "Internet Access", items: ["Internet services", "Paid Wi-Fi"] },
  //     { icon: "fa-utensils", title: "Kitchen", items: ["Tea/Coffee maker"] },
  //     { icon: "fa-wine-glass-empty", title: "Food and drinks", items: ["Continental Breakfast", "24*7 Room Service", "Bar", "Coffee Shop", "In-Room Breakfast"] },
  //     { icon: "fa-futbol", title: "Activities and sports", items: ["Indoor Swimming Pool", "Fitness Centre", "Spa/sauna", "Bicycle Rental", "Ticket Services", "Tours"] },
  //     { icon: "fa-wheelchair", title: "Accessibility", items: ["Elevator access"] }
  //   ],
  //   [
  //     { icon: "fa-bell-concierge", title: "Services and conveniences", items: ["Facilities for Differently-Abled Guests", "Luggage Storage", "Laundry Service", "Chapel", "Doorman", "Dry-Cleaning", "Library", "Postal Service"] },
  //     { icon: "fa-shield-virus", title: "Safety and cleanliness", items: ["Cashless Payment Service", "Common Area Disinfection (Daily)", "Doctor/Nurse on Call", "Free Face Masks", "No Shared Stationery"] },
  //     { icon: "fa-language", title: "Languages spoken", items: ["English", "Hindi"] }
  //   ]
  // ];

  function convertAmenitiesToFacilityCategories(amenities: Amenity[]): FacilityItem[][] {
  const categoryMap = new Map<string, { icon: string; title: string; items: string[]; rank: number }>();

  amenities.forEach((amenity) => {
    if (!categoryMap.has(amenity.categoryName)) {
      categoryMap.set(amenity.categoryName, {
        icon: amenity.categoryUrl,
        title: amenity.categoryName,
        items: [],
        rank: amenity.categoryRank
      });
    }
    categoryMap.get(amenity.categoryName)!.items.push(amenity.name);
  });

  const sortedCategories = Array.from(categoryMap.values()).sort((a, b) => a.rank - b.rank);
  const chunkSize = Math.ceil(sortedCategories.length / 3);
  const facilityCategories: FacilityItem[][] = [];

  for (let i = 0; i < sortedCategories.length; i += chunkSize) {
    const chunk = sortedCategories.slice(i, i + chunkSize).map(({ icon, title, items }) => ({
      icon,
      title,
      items
    }));
    facilityCategories.push(chunk);
  }

  return facilityCategories;
}

 const facilityCategories = amenityDetails?.amenities
    ? convertAmenitiesToFacilityCategories(amenityDetails.amenities)
    : [];


  

  return (
    <div ref={ref} className="hotel-facilities-section">
      <h2 className="section-title">{t('hotel.detail.hotelFacilities')}</h2>

      <div className="facilities-container">
        {facilityCategories.map((column, columnIndex) => (
          <div key={columnIndex} className="facilities-column">
            {column.map((facility, facilityIndex) => (
              <div key={facilityIndex} className="facility-box">
                <div className="facility-header">
                  <i className={`fa-solid ${facility.icon}`}></i>
                  <h3>{facility.title}</h3>
                </div>
                <div className="facility-content">
                  {facility.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="facility-item">{item}</div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
});

HotelFacilities.displayName = "HotelFacilities";

export default HotelFacilities;