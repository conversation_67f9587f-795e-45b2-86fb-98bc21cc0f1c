'use client'
import './NavigationBar.scss'

type Props = {
  activeStatus: string
  onTabChange: (tab: string) => void
}

function NavigationBar({ activeStatus, onTabChange } : Props) {

    const statusOptions = ["Upcoming", "Completed", "Cancelled"];
  return (
    
  <div className="navigation-bar">
  {
    statusOptions?.map((status) => (
      <div key={status} className={`navigation-bar__nav-item ${activeStatus == status ? 'active' : ''}`} >
      <span onClick={() => onTabChange(status)}>
      {status}
      </span>
    </div>

    ))
  }
 
</div>

  )
}

export default NavigationBar