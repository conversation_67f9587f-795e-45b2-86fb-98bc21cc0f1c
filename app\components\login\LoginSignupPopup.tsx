// // LoginSignupPopup.tsx
// "use client";
// import { useState, useEffect } from 'react';
// import Image from 'next/image';
// import Logo2 from "../../../public/assets/img/logo2.jpg";
// import { X, Check, AlertCircle } from 'lucide-react';
// import FacebookIcon from '@/app/components/icons/FacebookIcon';
// import GoogleIcon from '@/app/components/icons/GoogleIcon';
// import AppleIcon from '@/app/components/icons/AppleIcon';
// import { useCommonContext } from '@/app/contexts/commonContext';
// import './LoginSignupPopup.scss';

// interface LoginSignupPopupProps {
//   isOpen: boolean;
//   onClose: () => void;
// }

// const LoginSignupPopup: React.FC<LoginSignupPopupProps> = ({ isOpen, onClose }) => {
//   const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');
//   // Login states
//   const [username, setUsername] = useState('');
//   const [password, setPassword] = useState('');
//   // Signup states
//   const [fullName, setFullName] = useState('');
//   const [email, setEmail] = useState('');
//   const [signupPassword, setSignupPassword] = useState('');
//   const [confirmPassword, setConfirmPassword] = useState('');
//   const [loginMessage, setLoginMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
//   const {isLoading, setIsLoading, setUserData, setIsLoggedIn} = useCommonContext();
//   const [showSuccessBeforeClose, setShowSuccessBeforeClose] = useState(false);

//   // Reset form when switching tabs
//   useEffect(() => {
//     setLoginMessage(null);
//   }, [activeTab]);

//   // Handle success messages with delay
//   useEffect(() => {
//     if (showSuccessBeforeClose) {
//       const timer = setTimeout(() => {
//         onClose();
//         setShowSuccessBeforeClose(false);
//       }, 2000);

//       return () => clearTimeout(timer);
//     }
//   }, [showSuccessBeforeClose, onClose]);

//   if (!isOpen) return null;

//   const handleLogin = (e: React.FormEvent) => {
//     e.preventDefault();
//     setIsLoading(true);
//     setLoginMessage(null);

//     // Validate login form
//     if (!username || !password) {
//       setLoginMessage({
//         type: 'error',
//         text: 'Please enter both username and password.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Simulate API call with setTimeout
//     setTimeout(() => {
//       if (username === 'jinshad' && password === '123456') {
//         setLoginMessage({
//           type: 'success',
//           text: 'Login successful! Welcome back, Jinshad.'
//         });

//         // Set user data and logged in state
//         const userData = { first_name: 'jinshad', second_name:'',  email: '<EMAIL>', phone: '**********' };
//         localStorage.setItem('userData', JSON.stringify(userData));
//         setUserData(userData);
//         setIsLoggedIn(true);

//         // Show success message before closing
//         setShowSuccessBeforeClose(true);
//       } else {
//         setLoginMessage({
//           type: 'error',
//           text: 'Invalid username or password. Please try again.'
//         });
//       }
//       setIsLoading(false);
//     }, 1000);
//   };

//   const handleSignup = (e: React.FormEvent) => {
//     e.preventDefault();
//     setIsLoading(true);
//     setLoginMessage(null);

//     // Form validation
//     if (!fullName || !email || !signupPassword || !confirmPassword) {
//       setLoginMessage({
//         type: 'error',
//         text: 'Please fill in all fields to create an account.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     if (signupPassword !== confirmPassword) {
//       setLoginMessage({
//         type: 'error',
//         text: 'Passwords do not match. Please try again.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Simple email validation
//     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//     if (!emailRegex.test(email)) {
//       setLoginMessage({
//         type: 'error',
//         text: 'Please enter a valid email address.'
//       });
//       setIsLoading(false);
//       return;
//     }

//     // Simulate API call for signup
//     setTimeout(() => {
//       setLoginMessage({
//         type: 'success',
//         text: 'Account created successfully! Please check your email to verify your account.'
//       });

//       // Create user data and update state
//       const userData = { first_name: fullName , second_name:'', email: email, phone: '**********' };
//       localStorage.setItem('userData', JSON.stringify(userData));
//       setUserData(userData);
//       setIsLoggedIn(true);

//       // Show success message before closing
//       setShowSuccessBeforeClose(true);
//       setIsLoading(false);
//     }, 1000);
//   };

//   return (
//     <div className="login-signup-container fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm p-4">
//       <div className="relative w-full max-w-4xl bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col md:flex-row">
//         {/* Mobile Logo (visible only on small screens) */}
//         <div className="md:hidden flex justify-center py-6">
//           <div className="w-32 h-12 relative">
//             <Image
//               src={Logo2}
//               alt="LuxStay Logo"
//               className="object-contain"
//               fill
//               priority
//             />
//           </div>
//         </div>

//         {/* Left side - Logo (hidden on mobile) */}
//         <div className="hidden md:block w-full md:w-1/2 relative bg-gray-50">
//           <div className="absolute inset-0 flex items-center justify-center">
//             <div className="w-3/4 h-3/4 relative">
//               <Image
//                 src={Logo2}
//                 alt="LuxStay Logo"
//                 className="object-contain"
//                 fill
//                 priority
//               />
//             </div>
//           </div>
//           <div className="absolute bottom-0 left-0 right-0 p-4 md:p-8">
//             <p className="text-xs sm:text-sm mt-2 text-center">Find and Book Exclusive Hotels Effortlessly.</p>
//           </div>
//         </div>

//         {/* Right side - Login/Signup Forms */}
//         <div className="w-full md:w-1/2 p-4 sm:p-6">
//           <div className="flex justify-end mb-4 sm:mb-6">
//             <button
//               onClick={onClose}
//               className="text-gray-500 hover:text-gray-700 transition-colors"
//               aria-label="Close popup"
//             >
//               <X size={24} />
//             </button>
//           </div>

//           {/* Tabs */}
//           <div className="flex mb-4 sm:mb-6 border-b border-gray-200">
//             <button
//               className={`px-3 sm:px-4 py-2 font-medium text-xs sm:text-sm ${
//                 activeTab === 'login'
//                   ? 'text-[#76B1B1] border-b-2 border-[#76B1B1]'
//                   : 'text-gray-500'
//               }`}
//               onClick={() => setActiveTab('login')}
//             >
//               Log In
//             </button>
//             <button
//               className={`px-3 sm:px-4 py-2 font-medium text-xs sm:text-sm ${
//                 activeTab === 'signup'
//                   ? 'text-[#76B1B1] border-b-2 border-[#76B1B1]'
//                   : 'text-gray-500'
//               }`}
//               onClick={() => setActiveTab('signup')}
//             >
//               Sign Up
//             </button>
//           </div>

//           {/* Status Message */}
//           {loginMessage && (
//             <div className={`p-2 sm:p-3 mb-3 sm:mb-4 rounded-md flex items-start gap-2 text-xs sm:text-sm ${
//               loginMessage.type === 'success'
//                 ? 'bg-green-50 text-green-800 border border-green-200'
//                 : 'bg-red-50 text-red-800 border border-red-200'
//             }`}>
//               {loginMessage.type === 'success'
//                 ? <Check size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
//                 : <AlertCircle size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
//               }
//               <span>{loginMessage.text}</span>
//             </div>
//           )}

//           {/* Login Form */}
//           {activeTab === 'login' && (
//             <form onSubmit={handleLogin} className="space-y-3 sm:space-y-4">
//               <div>
//                 <label htmlFor="username" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
//                   Username
//                 </label>
//                 <input
//                   type="text"
//                   id="username"
//                   value={username}
//                   onChange={(e) => setUsername(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#76B1B1] focus:border-[#76B1B1] text-sm"
//                   placeholder="jinshad"
//                 />
//               </div>
//               <div>
//                 <label htmlFor="password" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
//                   Password
//                 </label>
//                 <input
//                   type="password"
//                   id="password"
//                   value={password}
//                   onChange={(e) => setPassword(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#76B1B1] focus:border-[#76B1B1] text-sm"
//                   placeholder="123456"
//                 />
//               </div>
//               <div className="flex justify-end">
//                 <a href="#" className="text-xs sm:text-sm text-[#76B1B1] hover:underline">
//                   Forgot password?
//                 </a>
//               </div>
//               <button
//                 type="submit"
//                 disabled={isLoading}
//                 className="w-full bg-[#76B1B1] text-white py-2 rounded-md hover:bg-[#5d8e8e] transition-colors font-medium flex justify-center items-center text-sm"
//               >
//                 {isLoading ? 'Logging in...' : 'Log In'}
//               </button>

//               {/* Social Login */}
//               <div className="mt-4 sm:mt-6">
//                 <div className="relative">
//                   <div className="absolute inset-0 flex items-center">
//                     <div className="w-full border-t border-gray-300"></div>
//                   </div>
//                   <div className="relative flex justify-center text-xs sm:text-sm">
//                     <span className="px-2 bg-white text-gray-500">Or continue with</span>
//                   </div>
//                 </div>

//                 <div className="mt-4 sm:mt-6 grid grid-cols-3 gap-2 sm:gap-3">
//                   <button type="button" className="flex justify-center items-center py-1.5 sm:py-2 px-3 sm:px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50">
//                     <FacebookIcon className="w-4 h-4 sm:w-5 sm:h-5" />
//                   </button>
//                   <button type="button" className="flex justify-center items-center py-1.5 sm:py-2 px-3 sm:px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50">
//                     <GoogleIcon className="w-4 h-4 sm:w-5 sm:h-5" />
//                   </button>
//                   <button type="button" className="flex justify-center items-center py-1.5 sm:py-2 px-3 sm:px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50">
//                     <AppleIcon className="w-4 h-4 sm:w-5 sm:h-5" />
//                   </button>
//                 </div>
//               </div>

//               <p className="text-center text-xs sm:text-sm text-gray-600 mt-4 sm:mt-6">
//                 Don't have an account?{' '}
//                 <button
//                   type="button"
//                   style={{ color: "var(--primary-color)" }} 
//                   className="hover:underline font-medium"
//                   onClick={() => setActiveTab('signup')}
//                 >
//                   Sign up
//                 </button>
//               </p>
//             </form>
//           )}

//           {/* Signup Form */}
//           {activeTab === 'signup' && (
//             <form onSubmit={handleSignup} className="space-y-3 sm:space-y-4">
//               <div>
//                 <label htmlFor="fullName" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
//                   Full Name
//                 </label>
//                 <input
//                   type="text"
//                   id="fullName"
//                   value={fullName}
//                   onChange={(e) => setFullName(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#76B1B1] focus:border-[#76B1B1] text-sm"
//                   placeholder="John Doe"
//                 />
//               </div>
//               <div>
//                 <label htmlFor="signupEmail" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
//                   Email Address
//                 </label>
//                 <input
//                   type="email"
//                   id="signupEmail"
//                   value={email}
//                   onChange={(e) => setEmail(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#76B1B1] focus:border-[#76B1B1] text-sm"
//                   placeholder="<EMAIL>"
//                 />
//               </div>
//               <div>
//                 <label htmlFor="signupPassword" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
//                   Password
//                 </label>
//                 <input
//                   type="password"
//                   id="signupPassword"
//                   value={signupPassword}
//                   onChange={(e) => setSignupPassword(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#76B1B1] focus:border-[#76B1B1] text-sm"
//                   placeholder="••••••••"
//                 />
//               </div>
//               <div>
//                 <label htmlFor="confirmPassword" className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
//                   Confirm Password
//                 </label>
//                 <input
//                   type="password"
//                   id="confirmPassword"
//                   value={confirmPassword}
//                   onChange={(e) => setConfirmPassword(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-[#76B1B1] focus:border-[#76B1B1] text-sm"
//                   placeholder="••••••••"
//                 />
//               </div>
//               <button
//                 type="submit"
//                 disabled={isLoading}
//                 className="w-full bg-[#76B1B1] text-white py-2 rounded-md hover:bg-[#5d8e8e] transition-colors font-medium flex justify-center items-center text-sm"
//               >
//                 {isLoading ? 'Creating account...' : 'Create Account'}
//               </button>

//               {/* Social Login */}
//               <div className="mt-4 sm:mt-6">
//                 <div className="relative">
//                   <div className="absolute inset-0 flex items-center">
//                     <div className="w-full border-t border-gray-300"></div>
//                   </div>
//                   <div className="relative flex justify-center text-xs sm:text-sm">
//                     <span className="px-2 bg-white text-gray-500">Or sign up with</span>
//                   </div>
//                 </div>

//                 <div className="mt-4 sm:mt-6 grid grid-cols-3 gap-2 sm:gap-3">
//                   <button type="button" className="flex justify-center items-center py-1.5 sm:py-2 px-3 sm:px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50">
//                     <FacebookIcon className="w-4 h-4 sm:w-5 sm:h-5" />
//                   </button>
//                   <button type="button" className="flex justify-center items-center py-1.5 sm:py-2 px-3 sm:px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50">
//                     <GoogleIcon className="w-4 h-4 sm:w-5 sm:h-5" />
//                   </button>
//                   <button type="button" className="flex justify-center items-center py-1.5 sm:py-2 px-3 sm:px-4 border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50">
//                     <AppleIcon className="w-4 h-4 sm:w-5 sm:h-5" />
//                   </button>
//                 </div>
//               </div>

//               <p className="text-center text-xs sm:text-sm text-gray-600 mt-4 sm:mt-6">
//                 Already have an account?{' '}
//                 <button
//                   type="button"
//                   style={{ color: "var(--primary-color)" }} 
//                   className="hover:underline font-bold"
//                   onClick={() => setActiveTab('login')}
//                 >
//                   Log in
//                 </button>
//               </p>
//             </form>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginSignupPopup;