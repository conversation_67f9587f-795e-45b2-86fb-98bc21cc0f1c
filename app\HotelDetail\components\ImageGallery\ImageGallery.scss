@use "/styles/variable" as *;
@use "sass:color";

.image-gallery-container {
  width: 100%;
  margin-bottom: 20px;
  padding: 5px 0 0 0;

  .flex-row {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 20px;
    //justify-content: center;
    flex-wrap: nowrap;

    @media (max-width: $breakpoint-lg) {
      flex-wrap: wrap;
      
    }


    .flex-col {
      width: 100%;
    }

    .col1 {
      width: 50%;
      height: 400px;
      border-radius: 10px;
      position: relative;

      img {
        max-width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px;
      }

      @media (max-width: 1024px) {
        width: 100%;
        height: 300px;
      }

      @media (max-width: 768px) {
        height: 250px;
      }

      @media (max-width: 480px) {
        height: 200px;
      }
    }

    .col2 {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 20px;
      position: relative;
      overflow: hidden;
      max-width: 48%;

      @media (max-width: $breakpoint-md) {
        max-width: 730px;
        
      }

      @media (max-width: $breakpoint-lg) {
        max-width: 100%;
        
      }

      .sub-col {
        width: calc(50% - 10px);
        height: 190px;
        border-radius: 10px;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          border-radius: 10px;
          object-fit: cover;
        }

        @media (max-width: 1024px) {
          height: 160px;
        }

        @media (max-width: 768px) {
          height: 130px;
        }

        @media (max-width: 480px) {
          height: 100px;
        }
      }
    }

    .morePhotos {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 2px 5px;
      background-color: #2a2b30;
      position: absolute;
      bottom: 10px;
      right: 10px;
      border-radius: 12px;
      transition: background-color 0.5s ease;
      cursor: pointer;

      .fa-images {
        color: #ffffff;
      }

      span {
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        padding: 0 5px;
      }

      &:hover {
        background-color: color.adjust(#2a2b30, $lightness: -20%);
      }
    }
  }
}

// Modal Styles
.slide-from-right-modal {
  .modal-content {
    .content {
      .categories {
        display: flex;
        flex-direction: row;
        overflow-x: auto;
        width: 100%;
        border-bottom: 1px solid rgba(0, 0, 0, 0.2);
        white-space: nowrap;

        &::-webkit-scrollbar {
          display: none;
        }

        scrollbar-width: none;
        -ms-overflow-style: none;

        .category {
          padding: 10px 15px;
          font-size: 15px;
          font-weight: 500;
          position: relative;
          transition: all 0.3s ease;
          cursor: pointer;
          user-select: none;

          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }

          &.active {
            color: #0770e4;
          }

          &.active::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 3px;
            background-color: #0770e4;
            border-radius: 5px;
          }
        }
      }

      .image-flex {
        padding: 20px 30px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 20px;

        .image-wrapper {
          object-fit: cover;
          width: 240px;
          height: 150px;
          border-radius: 20px;
          background-color: rgba(0, 0, 0, 0.2);
          position: relative;

          img {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            object-fit: cover;
          }

          @media (max-width: 768px) {
            width: 45%;
            height: 130px;
          }

          @media (max-width: 480px) {
            width: 100%;
            height: 120px;
          }
        }
      }
    }
  }
}
