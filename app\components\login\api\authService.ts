// Mock API service for authentication

// Types
export interface User {
  id: number;
  name: string;
  email: string;
  whatsapp: string;
  isRegistered: boolean;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Mock user database
const MOCK_USERS: User[] = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', whatsapp: '9876543210', isRegistered: true },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', whatsapp: '9876543211', isRegistered: true },
];

// Simulate API delay
const apiDelay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Check if user exists by email or WhatsApp
export const checkUserExists = async (identifier: string): Promise<AuthResponse> => {
  await apiDelay(1000); // Simulate network delay
  
  const userExists = MOCK_USERS.some(
    user => user.email === identifier || user.whatsapp === identifier
  );
  
  return {
    success: true,
    message: userExists ? 'User found' : 'User not found',
    data: { exists: userExists }
  };
};

// Send OTP to email or WhatsApp
export const sendOtp = async (type: 'email' | 'whatsapp', value: string): Promise<AuthResponse> => {
  await apiDelay(1000); // Simulate network delay
  
  // In a real implementation, this would send an actual OTP via email or SMS
  return {
    success: true,
    message: `OTP sent to ${type} ${value}`,
    data: { otpSent: true }
  };
};

// Verify OTP
export const verifyOtp = async (type: 'email' | 'whatsapp', value: string, otp: string): Promise<AuthResponse> => {
  await apiDelay(1000); // Simulate network delay
  
  // For demo purposes, any OTP of '1234' is considered valid
  const isValid = otp === '1234';
  
  return {
    success: isValid,
    message: isValid ? `${type} verified successfully` : 'Invalid OTP',
    data: { verified: isValid }
  };
};

// Login user
export const loginUser = async (identifier: string): Promise<AuthResponse> => {
  await apiDelay(1000); // Simulate network delay
  
  const user = MOCK_USERS.find(
    user => user.email === identifier || user.whatsapp === identifier
  );
  
  if (!user) {
    return {
      success: false,
      message: 'User not found'
    };
  }
  
  return {
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        whatsapp: user.whatsapp
      },
      token: 'mock-jwt-token-' + user.id
    }
  };
};

// Register new user
export const registerUser = async (userData: {
  name: string;
  email: string;
  whatsapp: string;
}): Promise<AuthResponse> => {
  await apiDelay(1500); // Simulate network delay
  
  // Check if user already exists
  const userExists = MOCK_USERS.some(
    user => user.email === userData.email || user.whatsapp === userData.whatsapp
  );
  
  if (userExists) {
    return {
      success: false,
      message: 'User with this email or WhatsApp already exists'
    };
  }
  
  // In a real implementation, this would create a new user in the database
  const newUser = {
    id: MOCK_USERS.length + 1,
    ...userData,
    isRegistered: true
  };
  
  // Add to mock database (this is just for demonstration)
  MOCK_USERS.push(newUser);
  
  return {
    success: true,
    message: 'Registration successful',
    data: {
      user: {
        id: newUser.id,
        name: newUser.name,
        email: newUser.email,
        whatsapp: newUser.whatsapp
      },
      token: 'mock-jwt-token-' + newUser.id
    }
  };
};
