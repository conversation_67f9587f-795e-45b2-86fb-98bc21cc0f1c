@use "sass:color";
@use "/styles/variable" as *;
@use "/styles/zIndex" as * ;

$accent: #F9A825;
$background-light: #F8F9FC;
$background-input: #FFFFFF;
$text-dark: #2C3E50;
$text-medium: #5D6D7E;
$text-light: #8395A7;
$border-color: rgba($text-medium, 0.15);
$shadow-color: rgba(44, 62, 80, 0.1);
$range-background: rgba($primary-color, 0.15);
$range-border: rgba($primary-color, 0.3);
$today-color: #E74C3C;
$past-date-color: #DFE4E8;
$past-date-text: #B2BABB;

// Standard spacing - slightly reduced
$spacing-xs: 3px;
$spacing-sm: 6px;
$spacing-md: 10px;
$spacing-lg: 14px;
$spacing-xl: 18px;
$border-radius-sm: 5px;
$border-radius-md: 8px;
$border-radius-lg: 10px;

// Added a wrapper to control width
.calendarWrapper {
  width: 100%;
  height: 100%; // Full height
  display: flex; // Changed to flex
  flex-direction: column;
  position: relative;
  z-index: z-index(modal);
  overflow: hidden; // Prevent wrapper from creating scroll
}

.calendarContainer {
  width: 100%; // Full width of parent
  background-color: $background-input;
  border-radius: $border-radius-lg;
  box-shadow: 0 8px 24px rgba($text-dark, 0.15);
  overflow: hidden; // Prevent container from creating scroll
  border: 1px solid $border-color;
  z-index: z-index(base);
  margin-top: $spacing-md;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px; // Minimum height for sticky to work
  max-height: 80vh; // Limit height for mobile
  position: relative; // Important for sticky children

  // Improved styling for better appearance
  .twoMonthsLayout {
    @media (min-width: 769px) {
      min-width: 600px; // Ensure minimum width for desktop
    }
  }

  @media (max-width: 768px) {
    .twoMonthsLayout {
      flex-direction: column !important;
    }
  }

  .calendarContent {
    padding: $spacing-md;
  }

  .calendarHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // Remove padding and margin since we're using fixed-header class
    background-color: var(--primary-color);
    color: white;

    h2 {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;

      &::before {
        content: "\f073";
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        margin-right: $spacing-sm;
        background: rgba(255, 255, 255, 0.2);
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
      }
    }

    .priceToggle {
      display: flex;
      align-items: center;

      label {
        display: flex;
        align-items: center;
        cursor: pointer;

        span {
          margin-right: $spacing-sm;
          font-weight: 500;
          font-size: 12px;
        }

        .toggle {
          width: 38px;
          height: 20px;
          background-color: rgba(255, 255, 255, 0.3);
          border-radius: 20px;
          position: relative;
          transition: all 0.3s;

          &.active {
            background-color: rgba(255, 255, 255, 0.8);
          }

          .toggleKnob {
            position: absolute;
            width: 16px;
            height: 16px;
            background-color: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
          }

          &.active .toggleKnob {
            left: calc(100% - 18px);
            background-color: $secondary-color;
          }
        }
      }
    }
  }

  .selectedDaysSummary {
    margin: $spacing-sm 0 $spacing-md;
    padding: $spacing-md;
    background-color: rgba($primary-color, 0.08);
    border-radius: $border-radius-md;
    border-left: 3px solid $primary-color;

    .summaryContent {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      flex-wrap: wrap;

      .summaryLabel {
        font-size: 12px;
        font-weight: 600;
        color: $text-medium;
      }

      .summaryDates {
        font-size: 12px;
        font-weight: 600;
        color: $primary-color;
      }

      .summaryNights {
        font-size: 12px;
        color: $text-medium;
        background: rgba($primary-color, 0.1);
        padding: 2px 6px;
        border-radius: 12px;
      }
    }
  }

  .monthNavigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
    padding: 0 $spacing-sm;

    .navButton {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      border: 1px solid $border-color;
      background-color: $background-input;
      color: $primary-color;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(.disabled) {
        background-color: rgba($primary-color, 0.1);
        border-color: $primary-color;
      }

      &:active:not(.disabled) {
        transform: scale(0.95);
      }

      i {
        font-size: 12px;
      }

      &.disabled {
        opacity: 0.4;
        cursor: not-allowed;
        background-color: $background-light;
      }
    }

    .monthLabels {
      display: flex;
      flex: 1;
      justify-content: center;

      span {
        font-size: 13px;
        font-weight: 600;
        color: $text-dark;
        padding: $spacing-xs $spacing-sm;
        border-radius: $border-radius-sm;
        background-color: $background-light;
      }
    }
  }

  .calendarsWrapper {
    background-color: $background-light;
    border-radius: $border-radius-md;
    padding: $spacing-sm;

    .twoMonthsLayout {
      display: flex;
      gap: $spacing-md;

      @media (max-width: 768px) {
        flex-direction: column;
      }
    }

    .calendarMonth {
      width: 100%;
      padding: $spacing-sm;
      background-color: $background-input;
      border-radius: $border-radius-sm;

      .monthLabel {
        text-align: center;
        padding: $spacing-xs;
        font-size: 12px;
        font-weight: 600;
        color: $primary-color;
        background-color: rgba($primary-color, 0.05);
        border-radius: $border-radius-sm;
        margin-bottom: $spacing-xs;
      }

      .dayNames {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        margin-bottom: $spacing-xs;

        .dayName {
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          font-weight: 600;
          color: $text-medium;
        }
      }

      .days {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;

        .dayCell {
          height: 36px; // Reduced size
          position: relative;

          &.empty {
            height: 36px;
          }

          .day {
            cursor: pointer;
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;

            &.inRange {
              background-color: rgba($primary-color, 0.15);
              border-radius: 0;
            }

            &.start, &.end {
              .dayNumber {
                background-color: $primary-color;
                color: white;
                box-shadow: 0 2px 6px rgba($primary-color, 0.3);
                transform: scale(1.05);
              }
            }

            &.past {
              cursor: pointer; // Make past dates clickable
              background-color: rgba($past-date-color, 0.3);
              opacity: 0.7;
              position: relative;

              .dayNumber {
                color: $past-date-text;
                border: 1px dashed rgba($past-date-text, 0.5);
              }

              // Simple hover effect without tooltip
              &:hover {
                background-color: rgba($primary-color, 0.1);
                opacity: 0.9;
                transform: scale(1.05);

                .dayNumber {
                  color: $primary-color;
                  border-color: $primary-color;
                }
              }
            }

            &.today {
              .dayNumber {
                background-color: $secondary-color;
                color: white;
                font-weight: 700;
                box-shadow: 0 2px 6px rgba($secondary-color, 0.3);
              }

              &.start .dayNumber {
                background-color: $primary-color;
              }
            }

            .dayNumber {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 26px;
              height: 26px;
              border-radius: 50%;
              font-size: 12px;
              font-weight: 500;
              transition: all 0.2s ease;

              &:hover:not(.day.past .dayNumber) {
                background-color: rgba($primary-color, 0.2);
                transform: scale(1.05);
              }
            }

            .dayPrice {
              font-size: 9px;
              margin-top: 2px;
              color: $secondary-color;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .calendarFooter {
    margin-top: $spacing-md;
    padding-top: $spacing-sm;
    border-top: 1px solid $border-color;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .legend {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;

      .legendItem {
        display: flex;
        align-items: center;
        font-size: 10px;
        color: $text-medium;

        .legendColor {
          width: 10px;
          height: 10px;
          margin-right: $spacing-xs;

          &.circle {
            background-color: $primary-color;
            border-radius: 50%;

            &.today {
              background-color: $today-color;
            }

            &.past {
              background-color: $past-date-color;
              border: 1px dashed rgba($past-date-text, 0.5);
            }
          }

          &.inRange {
            background-color: $range-background;
            border-radius: 3px;
          }
        }
      }
    }

    .applyButton {
      white-space: nowrap;
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 600;
      color: white;
      background-color: $primary-color;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 6px rgba($primary-color, 0.25);
      display: flex;
      align-items: center;
      gap: 5px;

      &::after {
        content: "\f00c";
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        font-size: 12px;
      }

      &:hover {
        background-color: darken($primary-color, 5%);
        box-shadow: 0 3px 8px rgba($primary-color, 0.4);
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 1px 3px rgba($primary-color, 0.25);
      }

      &:disabled {
        background-color: lighten($primary-color, 20%);
        cursor: not-allowed;
        box-shadow: none;
        opacity: 0.7;
      }
    }
  }
  // Fixed header styles
  .fixed-header {
    position: -webkit-sticky; // Safari support
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: var(--primary-color);
    color: white;
    border-bottom: 1px solid $border-color;
    flex-shrink: 0;
    padding: 16px 20px; // Added proper padding
    width: 100%;
    box-sizing: border-box;

    h2 {
      color: white;
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    // Ensure price toggle is visible
    .priceToggle {
      color: white;

      label {
        color: white;

        span {
          color: white;
        }
      }
    }
  }

  // Fixed summary styles
  .fixed-summary {
    position: -webkit-sticky; // Safari support
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 99;
    background-color: $background-light;
    border-bottom: 1px solid $border-color;
    flex-shrink: 0;
    padding: 16px 20px; // Increased padding
    width: 100%;
    box-sizing: border-box;

    // Override existing selectedDaysSummary styles when used as fixed
    margin: 0; // Remove margin for fixed positioning
    border-radius: 0; // Remove border radius for fixed positioning
    border-left: none; // Remove left border for fixed positioning
  }

  // Fixed navigation styles
  .fixed-navigation {
    position: -webkit-sticky; // Safari support
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 98;
    background-color: $background-input;
    border-bottom: 1px solid $border-color;
    flex-shrink: 0;
    padding: 16px 20px; // Increased padding
    width: 100%;
    box-sizing: border-box;
  }

  // Scrollable content area
  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0; // Important for flex child to be scrollable
    padding-bottom: 20px; // Add space at bottom to prevent overlap with footer

    // Custom scrollbar for better UX
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // Apply button container inside scrollable content
  .apply-button-container {
    padding: 20px;
    margin-top: 20px;
    border-top: 1px solid $border-color;
    background-color: $background-light;

    .mobile-apply-button {
      width: 100%;
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover:not(:disabled) {
        background-color: var(--primary-color-dark);
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }
  }

  // Desktop-only footer
  .desktop-only {
    display: block;

    @media (max-width: 768px) {
      display: none;
    }
  }

  // Fixed footer styles
  .fixed-footer {
    position: -webkit-sticky; // Safari support
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background-color: $background-input;
    border-top: 1px solid $border-color;
    flex-shrink: 0;
    padding: 16px 20px; // Added padding
    width: 100%;
    box-sizing: border-box;
    margin-top: auto; // Push to bottom

    // Ensure footer is always visible
    min-height: 80px; // Minimum height to ensure visibility
    display: flex;
    flex-direction: column;
    justify-content: center;

    .applyButton {
      margin-top: 8px; // Space above button
      min-height: 44px; // Touch-friendly height
    }
  }

  @media screen and (max-width: 768px) {
    margin-top: 0px;
    border-radius: 0px;
    max-height: 100vh; // Full height on mobile

    // Ensure footer is always visible on mobile
    .fixed-footer {
      position: sticky; // Keep sticky but ensure it works
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000; // Higher z-index for mobile
      background-color: $background-input;
      border-top: 2px solid $border-color; // Stronger border for visibility
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); // Add shadow for better visibility
    }

    .fixed-header {
      position: sticky;
      background-color: var(--primary-color);
      color: white;
    }

    .fixed-summary,
    .fixed-navigation,
    .fixed-footer {
      position: sticky;
      background-color: $background-input;
    }
  }

  // Mobile popup specific styles - apply when inside popup body
  .body-div & {
    border: none;
    box-shadow: none;
    margin-top: 0;
    border-radius: 0;
    width: 100%;
    height: 100%;
    max-height: 100vh;
    min-height: 100vh; // Ensure full height for sticky to work
    overflow: hidden; // Prevent this container from creating scroll

    // Fixed header in mobile popup
    .fixed-header {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      padding: 16px 12px;
      background-color: var(--primary-color);
      color: white;
      width: 100%;
      box-sizing: border-box;

      h2 {
        font-size: 16px;
        margin: 0;
        color: white;
      }

      .priceToggle {
        color: white;

        label {
          color: white;

          span {
            color: white;
          }
        }
      }
    }

    // Fixed summary in mobile popup
    .fixed-summary {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      left: 0;
      right: 0;
      z-index: 99;
      padding: 16px 12px; // Increased padding
      width: 100%;
      box-sizing: border-box;
      background-color: $background-light;
      border-bottom: 1px solid $border-color;
      margin: 0; // Remove margin for fixed positioning
      border-radius: 0; // Remove border radius for fixed positioning
      border-left: none; // Remove left border for fixed positioning
    }

    // Fixed navigation in mobile popup
    .fixed-navigation {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
      z-index: 98;
      padding: 12px;
    }

    // Scrollable content in mobile popup
    .scrollable-content {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      min-height: 0; // Important for flex child scrolling
      padding: 8px; // Minimal padding
      padding-bottom: 20px; // Normal padding since apply button is inside scroll area

      .twoMonthsLayout {
        min-width: auto;
        width: 100%;
        flex-direction: column;
        gap: 8px; // Reduced gap
      }

      .calendarMonth {
        width: 100%;
        padding: 4px; // Reduced padding

        .dayCell {
          height: 40px !important; // Larger touch targets for mobile popup

          .day {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .monthLabel {
          font-size: 14px;
          padding: 6px 4px; // Reduced padding
          margin-bottom: 6px;
        }

        .dayNames {
          margin-bottom: 4px; // Reduced margin

          .dayName {
            height: 28px;
            font-size: 11px;
          }
        }

        .days {
          gap: 1px; // Minimal gap between days
        }
      }
    }

    // Apply button container in mobile popup
    .apply-button-container {
      padding: 16px 12px;
      margin-top: 16px;
      background-color: white;
      border-top: 1px solid $border-color;

      .mobile-apply-button {
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: 600;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 8px;

        &:disabled {
          background-color: #ccc;
          color: #666;
        }
      }
    }

    // Hide desktop footer in mobile popup
    .desktop-only {
      display: none;
    }
  }

}

// Styles for date field container can be removed if not needed
.dateFieldContainer {
  width: 100%;
  min-height: 62px;
  display: flex;
  align-items: center;
  position: relative;

  .dateField {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 15px;
    font-weight: 500;
    width: 100%;
    height: 100%;
    padding: 12px 18px;
    cursor: pointer;
    position: relative;
    background-color: $background-input;
    border-radius: $border-radius-md;
    border: 1px solid $border-color;
    box-shadow: 0 2px 6px $shadow-color;
    transition: all 0.2s ease;

    &:hover, &:focus, &.active {
      border-color: rgba($primary-color, 0.5);
      box-shadow: 0 3px 10px rgba($primary-color, 0.15);
    }

    .fa-calendar-days {
      font-size: 20px;
      color: $primary-color;
      background: rgba($primary-color, 0.1);
      height: 38px;
      width: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }

    .label-inputField {
      display: flex;
      flex-direction: column;
      flex: 1;

      .label {
        font-size: 13px;
        font-weight: 600;
        color: $primary-color;
        margin: 0 0 4px 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .inputField {
        font-size: 15px;
        font-weight: 500;
        color: $text-dark;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}