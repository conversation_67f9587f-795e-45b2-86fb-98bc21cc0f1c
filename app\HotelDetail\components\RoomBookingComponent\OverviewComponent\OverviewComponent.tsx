"use client";
import React, { Dispatch, SetStateAction, useState } from "react";
import "./OverviewComponent.scss";
import { useTranslation } from "@/app/hooks/useTranslation";
import { AboutSection, AmenityDetails } from "@/app/HotelDetail/hotel-detail-result.model";


  

  type OverviewComponentProps = {
    scrollToFacilities: () => void;
    setIsMobileFacilitiesActive: Dispatch<SetStateAction<boolean>>;
    amenityDetails?: AmenityDetails;
    aboutSection?: AboutSection;
    ratingView?: any;
    userRatingCategory?: string;
    address?: string;
  };

function OverviewComponent({scrollToFacilities,  setIsMobileFacilitiesActive, amenityDetails, aboutSection, ratingView, userRatingCategory, address } : OverviewComponentProps) {
  const { t } = useTranslation();
  const [isDescExpanded, setIsDescExapanded] = useState<boolean>(false);
  const [isMobileView, setIsMobileView] = useState<boolean>(
    typeof window !== "undefined" ? window.innerWidth <= 950 : false
  );

  // Handle window resize
  React.useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth <= 950);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const fullText = aboutSection?.descriptionInfo?.[0].description[0] || "";
  const slicedfacilities = amenityDetails?.amenities.slice(0, 6) || [];
  const facilities = amenityDetails?.amenities || [];

  

  return (
    <div className="overview-container">
      <div className="about-section">
        <div className="overview-header">{t('hotel.detail.about')}</div>

        <div className="overview-description">
          {
            isDescExpanded ? fullText : `${fullText.substring(0, 297)}...`
          }
        </div>

        <div className="overviewBtn" onClick={() => setIsDescExapanded(!isDescExpanded)}>
          {
            isDescExpanded ? t('hotel.detail.readLess') : t('hotel.detail.readMore')
          }
         {
          isDescExpanded ? ( <span className="material-icons">keyboard_arrow_up</span>) : ( <span className="material-icons">keyboard_arrow_down</span>)
         }
        </div>
      </div>

      {/* Rating and Location Section - Only show on mobile after about-section */}
      {isMobileView && (
        <div className="mobile-rating-location-section">
          <div className="mobile-review">
            <div className="rating">
              {ratingView?.averageRating}
            </div>
            <div className="rating-detail">
              <p className="detail detail1">
                {userRatingCategory}
              </p>
              <p className="detail detail2">
                {ratingView?.ratingCount} {t("hotel.detail.ratings")}
              </p>
            </div>
          </div>

          <div className="mobile-location">
            <div className="icon">
              <i className="fa-solid fa-location-dot"></i>
            </div>
            <div className="details">
              <div className="detail detail1">
                {address}
              </div>
              <div className="detail detail2">
                {t("hotel.detail.viewOnMap")}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="facilities-section">

        <div className="overview-header">
            Popular Facilities
        </div>

        <div className="popular-facilties">

            {
                slicedfacilities.map((facility, index) => (


            <div className="facility" key={index}>

            <div className="icon">
            <i className={`fa-solid ${facility.categoryUrl}`}></i>
            </div>
            <div className="label">
                { facility.name }
            </div>


    </div>


                ))
            }


        </div>

        {/* <div className="overviewBtn" onClick={scrollToFacilities}>
        View 61+ More <span className="material-icons">keyboard_arrow_down</span>
      </div> */}

      <div className="overviewBtn" onClick={() => {
        if (window.innerWidth <= 950) {
          setIsMobileFacilitiesActive(true);

        }else{

          scrollToFacilities();
        }
      }}>
        {`View ${facilities.length - slicedfacilities.length}+ more`} <span className="material-icons">keyboard_arrow_down</span>
      </div>





      </div>


    </div>
  );
}

export default OverviewComponent;
