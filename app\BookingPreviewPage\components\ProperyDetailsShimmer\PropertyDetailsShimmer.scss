@use "/styles/variable" as *;

.property-details-shimmer-container {
  width: 100%;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  row-gap: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

  
  

  .shimmer {
    background: linear-gradient(90deg, #f5f5f5 25%, #f5f5f5 50%, #eaeaea 75%);
    background-size: 200% 100%;
    border-radius: 4px;
    animation: shimmer 1.5s ease infinite;

    @media (max-width: $breakpoint-md) {
      background: linear-gradient(90deg, #e0e0e0 25%, #e0e0e0 50%, #cfcfcf 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s ease infinite;
      
    }
  }

  .propery-header-shimmer {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
    padding-bottom: 20px;

    @media (max-width: $breakpoint-md) {
      row-gap: 10px;
      padding-bottom: 0;
    }

    .property-info-shimmer {
      display: flex;
      flex-direction: row;
      column-gap: 15px;

      &__image {
        width: 100px;
        height: 100px;
        border-radius: 10px !important;
        @extend .shimmer;
      }

      &__info {
        width: calc(100% - 115px);
      }

      &__title-star-rating {
        margin-bottom: 18px;

        .title {
          width: 280px;
          height: 20px;
          @extend .shimmer;

          @media (max-width: $breakpoint-md) {
            width: 130px;
          }
        }
      }

      &__location {
        width: 330px;
        height: 15px;
        margin-bottom: 23px;
        @extend .shimmer;

        @media (max-width: $breakpoint-md) {
          width: 130px;
        }
      }

      &__room-info {
        display: flex;
        flex-direction: row;
        gap: 20px;
        align-items: center;

        .info {
          width: 120px;
          height: 18px;
          margin-bottom: 15px;
          @extend .shimmer;
        }
      }
    }

    .checkin-checkout-container {
      max-width: 400px;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      @media (max-width: $breakpoint-md) {
        flex-direction: column;
        align-items: start;
        justify-content: start;
        row-gap: 10px;
      }

      .checkin-out-section {
        @media (max-width: $breakpoint-md) {
          display: none;
        }
        .date-label {
          width: 80px;
          height: 15px;
          margin-bottom: 12px;
          @extend .shimmer;
        }

        .date {
          width: 120px;
          height: 20px;
          @extend .shimmer;
          margin-bottom: 8px;
        }

        .time {
          width: 60px;
          height: 15px;

          @extend .shimmer;
        }
      }

      .day-count {
        width: 65px;
        height: 20px;
        border-radius: 9999px !important;
        @extend .shimmer;

        @media (max-width: $breakpoint-md) {
          width: 180px;
        }
      }

      
      .day-count2 {
        width: 100px;
        height: 20px;
        border-radius: 9999px !important;
        @extend .shimmer;

      
      }
    }
  }

  .room-details-shimmer-container {
    @media (max-width: $breakpoint-md) {
      display: none;
    }
    .room-header {
      width: 120px;
      height: 20px;
      @extend .shimmer;
      margin-bottom: 15px;
    }

    .room-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 15px;
      margin-bottom: 20px;

     

      &__image {
        width: 80px;
        height: 80px;
        border-radius: 10px !important;
        @extend .shimmer;
      }

      &__details {
        .room-name {
          width: 125px;
          height: 18px;
          @extend .shimmer;
          margin-bottom: 15px;
        }

        .room-details {
          display: flex;
          flex-direction: row;
          column-gap: 10px;
          align-items: center;

          .detail {
            width: 125px;
            height: 18px;
            @extend .shimmer;
          }
        }
      }
    }

    .room-benefits {
      @media (max-width: $breakpoint-md) {
        display: none;
      }
      &__header {
        width: 120px;
        height: 18px;
        margin-bottom: 10px;
        @extend .shimmer;
      }

      &__list {
        display: flex;
        flex-direction: row;
        gap: 15px;
        flex-wrap: wrap;

        .list-item {
          width: 140px;
          height: 15px;
          margin-bottom: 10px;
          list-style: none;
          @extend .shimmer;
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
