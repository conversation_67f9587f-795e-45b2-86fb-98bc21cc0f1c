import { HotelDetailResponse } from "@/app/HotelDetail/hotel-detail-result.model"
import apiService from "../api-service"

export const getHotelDetails = async (hotelId: string): Promise<{ data: { hotelDetailResponse: HotelDetailResponse } }> => {
    console.log('Calling hotel details API with hotel ID:', hotelId);
    console.log('API endpoint:', `hotels/${hotelId}/details`);

    const response = await apiService.get<{ data: { hotelDetailResponse: HotelDetailResponse } }>(`hotels/${hotelId}/details`);
    console.log('Hotel details API raw response:', response);

    return response;
}
