'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import i18n from '@/i18n';
import { languages } from '@/i18n';

interface LanguageContextType {
  currentLanguage: string;
  changeLanguage: (lang: string) => void;
  isRTL: boolean;
  fontFamily: string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const router = useRouter();
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');
  const [isRTL, setIsRTL] = useState<boolean>(false);
  const [fontFamily, setFontFamily] = useState<string>('"Open Sans", sans-serif');

  useEffect(() => {
    // Get language from localStorage or use default
    const savedLanguage = typeof window !== 'undefined'
      ? localStorage.getItem('language') || 'en'
      : 'en';

    if (languages.includes(savedLanguage)) {
      setCurrentLanguage(savedLanguage);
      i18n.changeLanguage(savedLanguage);
      const isArabic = savedLanguage === 'ar';
      setIsRTL(isArabic);
      if (isArabic) {
        setFontFamily('"Noto Naskh Arabic", "Open Sans", sans-serif');
      } else if (savedLanguage === 'hi') {
        setFontFamily('"Noto Sans Devanagari", "Open Sans", sans-serif');
      } else {
        setFontFamily('"Open Sans", sans-serif');
      }
    }
  }, []);

  const changeLanguage = (lang: string) => {
    if (languages.includes(lang)) {
      setCurrentLanguage(lang);
      i18n.changeLanguage(lang);
      localStorage.setItem('language', lang);
      const isArabic = lang === 'ar';
      setIsRTL(isArabic);
      if (isArabic) {
        setFontFamily('"Noto Naskh Arabic", "Open Sans", sans-serif');
      } else if (lang === 'hi') {
        setFontFamily('"Noto Sans Devanagari", "Open Sans", sans-serif');
      } else {
        setFontFamily('"Open Sans", sans-serif');
      }

      // Refresh the page to apply the language change
      router.refresh();
    }
  };

  return (
    <LanguageContext.Provider value={{ currentLanguage, changeLanguage, isRTL, fontFamily }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
