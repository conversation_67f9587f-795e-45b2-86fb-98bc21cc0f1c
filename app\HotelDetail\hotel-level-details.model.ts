export interface Root {
  data: Data
}

export interface Data {
  hotelLevelDetails: HotelLevelDetails
  rooms: Room[]
  metadata: Metadata
  benefitMap: BenefitMap[]
  hotelInfoToken: string
  providerId: number
}

export interface HotelLevelDetails {
  hotelDetail: HotelDetail
  amenities: Amenity[] // Changed from any to an array of Amenity objects
  countryId: number
  city: string
  isInternational: boolean
  accommodationType: string
  bestPriceGuarantee: boolean
}

// New interface for amenities
export interface Amenity {
  name: string
  code: string
  category?: string
  icon?: string
}

export interface HotelDetail {
  hotelName: string
  thumbnailImageURL: string
}

export interface Room {
  roomsLeft: number
  id: string
  masterRoomId: string
  name: string
  extraBedText: string | null // Changed from any to string | null
  properties: Property[]
  roomLevelAmenities: string[]
  roomOptions: RoomOption[]
  imageList: ImageList[]
  providerId: number
  fareDetail: FareDetail2
}

export interface Property {
  code: string
  data: string
}

export interface RoomOption {
  blockId: string
  name: string
  mealBenefits?: string[]
  cancellationBenefits: CancellationBenefits
  extraBenefitsCount: number
  otherBenefits: string[]
  priceIncrease: number
  fomoTag?: string
  isRecommended: boolean
  paymentType: string
  roomIndex: number
  fareDetail: FareDetail
}

export interface CancellationBenefits {
  code: string
  data: string
  policy: Policy[]
  remarks: Remarks
}

export interface Policy {
  code: string
  title: string
  subTitle: string
  cancellationDate: string
  checkInDate: string
  startDate: string
  endDate: string
  cancellationAmount: number
}

export interface Remarks {
  title: string
  text: string | null // Changed from any to string | null
  subText: string
}

export interface FareDetail {
  numberOfPersons:number;
  displayedBaseFare: number
  baseFare: number
  markUpFare: number
  markupDiscountPercent: string
  taxesAndFees: number
  totalPrice: number
  burnMoneyInfo: BurnMoneyInfo | null // Changed from any to specific interface or null
  couponCode: string
  cashback: CashbackInfo | null // Changed from any to specific interface or null
  cashbackText: string | null // Changed from any to string | null
  instantDiscount: number
  totalDiscount: number
  totalPGAmount: number
  totalBookingAmount: number
  bankOfferTextIncluded: boolean
  offerText: OfferText
  offerMessage: string | null // Changed from any to string | null
  taxAndChargeMap: TaxAndChargeMap
  discountMap: DiscountMap
  excludedChargeMap: ExcludedChargeMap
  discountViewType: string
  method: string
  totalBaseFare: number
  totalTaxesAndFees: number
  convenienceFee: number
  totalConvenienceFee: number
  paymentType: string
  payAtHotelAmount: number | null // Changed from any to number | null
  offset: number
}

// New interface for burn money info
export interface BurnMoneyInfo {
  pointsUsed?: number
  pointsValue?: number
  currency?: string
}

// New interface for cashback info
export interface CashbackInfo {
  amount: number
  percentage?: number
  type: string
}

export interface OfferText {
  [key: string]: string // Changed from empty object to string dictionary
}

export interface TaxAndChargeMap {
  "Hotel Tax": number
  "Convenience Fee": number
  [key: string]: number // Added index signature for other potential charges
}

export interface DiscountMap {
  "Reversal of Convenience fee": number
  "Coupon Discount": number
  "Supplier Discount": number
  [key: string]: number // Added index signature for other potential discounts
}

export interface ExcludedChargeMap {
  [key: string]: number // Added index signature for excluded charges
}

export interface ImageList {
  url: string
  caption: string
}

export interface FareDetail2 {
  displayedBaseFare: number
  baseFare: number
  markUpFare: number
  markupDiscountPercent: string
  taxesAndFees: number
  totalPrice: number
  burnMoneyInfo: BurnMoneyInfo | null // Changed from any to specific interface or null
  couponCode: string
  cashback: CashbackInfo | null // Changed from any to specific interface or null
  cashbackText: string | null // Changed from any to string | null
  instantDiscount: number
  totalDiscount: number
  totalPGAmount: number
  totalBookingAmount: number
  bankOfferTextIncluded: boolean
  offerText: OfferText2
  offerMessage: string | null // Changed from any to string | null
  taxAndChargeMap: TaxAndChargeMap2
  discountMap: DiscountMap2
  excludedChargeMap: ExcludedChargeMap2
  discountViewType: string
  method: string
  totalBaseFare: number
  totalTaxesAndFees: number
  convenienceFee: number
  totalConvenienceFee: number
  paymentType: string
  payAtHotelAmount: number | null // Changed from any to number | null
  offset: number
}

export interface OfferText2 {
  [key: string]: string // Changed from empty object to string dictionary
}

export interface TaxAndChargeMap2 {
  "Hotel Tax": number
  "Convenience Fee": number
  [key: string]: number // Added index signature for other potential charges
}

export interface DiscountMap2 {
  "Reversal of Convenience fee": number
  "Coupon Discount": number
  "Supplier Discount": number
  [key: string]: number // Added index signature for other potential discounts
}

export interface ExcludedChargeMap2 {
  [key: string]: number // Added index signature for excluded charges
}

export interface Metadata {
  rateMethod: string
  roomTypeMapping: RoomTypeMapping
}

export interface RoomTypeMapping {
  "Standard Room": string
  "Deluxe Room": string
  Suite: string
  [key: string]: string // Added index signature for other room types
}

export interface BenefitMap {
  name: string
  code: string
  blockIds: string[]
  count: number
}