"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { HotelSearchFormData } from "@/app/HotelSearchResult/components/HotelSearchBar/HotelSearchBar";
import { useCommonContext } from "@/app/contexts/commonContext";

interface RecentSearch {
  id: string;
  destination: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  rooms: number;
}

// No dummy data - only show actual user searches

function RecentSearches() {
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const router = useRouter();
  const { setHotelSearchFormData, setIsLoading } = useCommonContext();

  // Load recent searches from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedRecentSearches = localStorage.getItem('recentSearches');
      if (savedRecentSearches) {
        try {
          const parsedSearches = JSON.parse(savedRecentSearches);
          setRecentSearches(parsedSearches);
        } catch (error) {
          console.error('Error parsing recent searches:', error);
          // If parsing fails, start with empty array
          setRecentSearches([]);
          localStorage.removeItem('recentSearches');
        }
      } else {
        // If no saved searches, start with empty array
        setRecentSearches([]);
      }
    }
  }, []);

  const handleSearchClick = (search: RecentSearch) => {
    console.log("Clicked recent search:", search);

    // Convert recent search data to HotelSearchFormData format
    const searchFormData: HotelSearchFormData = {
      searchQuery: search.destination,
      checkInDate: convertToFullDate(search.checkIn),
      checkOutDate: convertToFullDate(search.checkOut),
      travelers: {
        adults: search.guests,
        rooms: search.rooms,
        children: 0, // Default to 0 children for recent searches
      },
      // Add roomsData array to match the interface
      roomsData: [
        {
          id: 1,
          adults: search.guests,
          children: 0,
          childrenAges: [],
        },
      ],
    };

    // Store the search data in localStorage for the search results page
    localStorage.setItem("hotelSearchFormData", JSON.stringify(searchFormData));

    // Also set the context data so the search bar gets populated immediately
    setHotelSearchFormData(searchFormData);

    // Show loading state
    setIsLoading(true);

    // Navigate to search results page
    router.push("/HotelSearchResult");
  };

  // Helper function to convert short date format to full date
  const convertToFullDate = (shortDate: string): string => {
    const currentYear = new Date().getFullYear();
    const [day, month] = shortDate.split(' ');

    // Convert month name to month number
    const monthMap: { [key: string]: string } = {
      'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
      'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
      'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
    };

    const monthNumber = monthMap[month] || '01';
    const dayPadded = day.padStart(2, '0');

    return `${currentYear}-${monthNumber}-${dayPadded}`;
  };

  const handleRemoveSearch = (e: React.MouseEvent, searchId: string) => {
    e.stopPropagation();
    const updatedSearches = recentSearches.filter(search => search.id !== searchId);
    setRecentSearches(updatedSearches);

    // Update localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
    }
  };

  const handleClearAll = () => {
    setRecentSearches([]);

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('recentSearches');
    }
  };

  if (recentSearches.length === 0) {
    return null;
  }

  return (
    <div className="w-full  mt-[30px] md:mt-10 ">
      <div className="flex flex-row justify-between items-center mb-6 md:mb-8  sm:items-start sm:gap-2">
        <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 md:text-2xl">Recent searches</h3>
        <button className="bg-transparent border border-gray-300 text-gray-600 text-[13px] font-medium px-3 py-[6px] rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-50 hover:border-gray-400 hover:text-gray-700 sm:self-end sm:text-xs sm:px-[10px] sm:py-[5px] md:text-sm md:px-4 md:py-2" onClick={handleClearAll}>
          Clear all
        </button>
      </div>

      <div className="relative">
        <div className="flex gap-3 overflow-x-auto pb-2 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden md:gap-[10px]">
          {recentSearches.map((search) => (
            <div
              key={search.id}
              className="flex-[0_0_auto] min-w-[240px] max-w-[280px] bg-white border border-gray-300 rounded-lg px-4 py-3 cursor-pointer transition-all duration-200 flex justify-between items-center hover:border-[#003b95] hover:shadow-[0_2px_8px_rgba(0,59,149,0.1)] hover:bg-blue-50 active:scale-[0.98] md:min-w-[220px] md:max-w-[260px] md:px-[14px] md:py-[10px] sm:min-w-[200px] sm:max-w-[240px] sm:px-3 sm:py-[10px]"
              onClick={() => handleSearchClick(search)}
              title={`Search hotels in ${search.destination}`}
            >
              <div className="flex-1 min-w-0">
                <div className="text-sm font-semibold text-[#1a1a1a] mb-1 whitespace-nowrap overflow-hidden text-ellipsis sm:text-[13px]">{search.destination}</div>
                <div className="flex items-center text-xs text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis sm:text-[11px]">
                  <span className="font-medium">{search.checkIn} - {search.checkOut}</span>
                  <span className="mx-[6px] text-gray-500 sm:mx-1">•</span>
                  <span>{search.guests} guests</span>
                  {search.rooms > 1 && (
                    <>
                      <span className="mx-[6px] text-gray-500 sm:mx-1">•</span>
                      <span>{search.rooms} rooms</span>
                    </>
                  )}
                </div>
              </div>
              <button
                className="bg-transparent border-0 text-gray-500 text-xs cursor-pointer p-1 rounded flex-shrink-0 ml-2 transition-all duration-200 hover:bg-gray-100 hover:text-gray-600 sm:text-[11px] sm:p-[3px]"
                onClick={(e) => handleRemoveSearch(e, search.id)}
                aria-label="Remove search"
              >
                <i className="fa-solid fa-xmark"></i>
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default RecentSearches;
