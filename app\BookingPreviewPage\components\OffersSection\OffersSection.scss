@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as *;

.offers-section-container {
  width: 100%;
  padding: 20px 20px 10px;
  background-color: #ffffff;
  border-radius: 15px;

  @media (max-width: $breakpoint-md) {
    border-radius: 0%;
    
  }

  &__header {
    font-size: 20px;
    color: $booking_black;
    margin-bottom: 10px;
    font-weight: 700;
  }

  &__promo-input {
    margin: 8px 0 10px 0;
    padding: 0 15px;
    outline: 1px solid rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    box-sizing: border-box;

    &:hover {
      outline: 2px solid rgba(0, 0, 0, 0.5);
      outline-offset: 1px;
    }

    &:focus-within {
      //outline: 2px solid #2196f3;
      outline: 2px solid $secondary-color;
      outline-offset: 1px;
    }

    .input-container {
      position: relative;
      width: 100%;
      max-width: 400px;
    }

    .input-container input {
      width: 100%;
      padding: 12px 0;
      outline: none;
      font-size: 16px;
      background-color: transparent;
    }

    .input-container input::placeholder {
      color: transparent;
    }

    .input-container label {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      font-size: 16px;
      color: rgba(0, 0, 0, 0.7);
      background: #fff;
      padding: 0 5px;
      transition: 0.3s ease;
      pointer-events: none;
    }

    .input-container input:focus + label,
    .input-container input:not(:placeholder-shown) + label {
      top: 0;
      left: 0;
      font-size: 12px;
      font-weight: 500;
      //color: #2196f3;
      color: $secondary-color;
    }
  }

  &__promo-selector {
    .promo-selector-option {
      padding: 10px 0;
      display: flex;
      align-items: flex-start;
      column-gap: 5px;
      border-radius: 8px;
      transition: border-color 0.2s;

      .radio-button {
        position: relative;
        display: flex;
        align-items: center;
        margin-top: 2px;

        input {
          opacity: 0;
          position: absolute;
          width: 20px;
          height: 20px;
          cursor: pointer;
          z-index: z-index(base);
        }

        input:hover + .radio {
          background-color: color.adjust(#ffffff, $lightness: -4%);
        }

        input:checked + .radio {
          animation: pulse 0.3s ease-in-out;
        }

        @keyframes pulse {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.2);
          }
          100% {
            transform: scale(1);
          }
        }

        .radio {
          width: 18px;
          height: 18px;
          border: 1px solid rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: inline-block;
          //z-index: 5;
          position: relative;
          transition: border-color 0.2s ease-in-out background-color 0.2s ease;

          &::after {
            content: "";
            width: 10px;
            height: 10px;
            background-color: transparent;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: background-color 0.2s ease-in-out;
          }
        }

        input:checked + .radio {
          //border-color: #2196f3;
          border-color: $secondary-color;

          &::after {
            //background-color: #2196f3;
              background-color: $secondary-color;
          }
        }
      }

      .promo-selector__content {
        flex-grow: 1;

        .promo-selector__header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .promo-selector__title {
            font-weight: bold;
            color: $booking_black;
            font-size: 16px;
          }

          .promo-selector__discount {
            font-weight: bold;
            color: $booking_black;
            font-size: 14px;
          }
        }

        .promo-selector__description {
          color: $booking_black;
          margin: 5px 0 0 0;
          font-size: 14px;
          font-weight: 500;

          &.checked {
            color: #238c46;
          }
        }

        .promo-selector__link {
          color: $booking_black;
          font-weight: 500;
          font-size: 14px;
          text-decoration: underline;
          cursor: pointer;
          margin: 5px 0 0 0;
          text-underline-offset: 1px;

      
        }
      }
    }

    .promo-selector-button-field{
      margin-top: 15px;

      .promo-selector-button{
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: pointer;

        .label{
          font-size: 16px;
          font-weight: bold;
          color: $secondary-color;
        }

        .icon{
          display: flex;
          align-items: center;
          justify-content: center;

          .arrowDwn{
            color: $secondary-color;

          }
        }
      }
    }
  }
}
