'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { MapProps } from './Map';

// Dynamically import the Map component with no SSR
// This is crucial because Leaflet requires the window object
const MapWithNoSSR = dynamic(() => import('./Map'), {
  ssr: false,
});

// Use the same props interface as the Map component
type MapWrapperProps = MapProps & {
  onMarkerClick?: (markerId: string) => void;
};

const MapWrapper: React.FC<MapWrapperProps> = (props) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return <div style={props.style || { height: '500px', width: '100%' }}>Loading map...</div>;
  }

  return <MapWithNoSSR {...props} />;
};

export default MapWrapper;