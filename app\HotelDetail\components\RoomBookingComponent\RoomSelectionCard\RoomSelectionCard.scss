@use "sass:color";
@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

// Responsive breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

.room-selection-card-container {
  padding: 30px 0 0 0;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  @media (max-width: $breakpoint-md) {
    padding: 20px 0;
    gap: 15px;
  }

  h6 {
    font-size: 22px;
    font-weight: 700;

    @media (max-width: $breakpoint-md) {
      font-size: 18px;
    }
  }

  // Filter options section
  .room-filter-options {
    display: flex;
    flex-direction: row;
    gap: 15px;
    margin-bottom: 20px;

    .filter-label {
      font-size: 16px;
      font-weight: 600;
    }

    .filter-checkboxes {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;

      .filter-option {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;

        input[type="checkbox"] {
          margin: 0;
        }

        span {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .search-rooms {
      position: relative;
      max-width: 300px;

      input {
        width: 100%;
        padding: 10px 40px 10px 15px;
        border: 1px solid rgba(0, 0, 0, 0.3);
        border-radius: 8px;
        font-size: 14px;

        &::placeholder {
          color: #999;
        }
      }

      .search-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        font-size: 20px;
      }
    }
  }

  // Header section
  .room-selection-card-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    padding: 20px;
    background-color: #f4f5f5;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 10px 10px 0 0;
    font-size: 14px;
    font-weight: 600;

    &.sticky {
      position: sticky;
      top: 100px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 0;
      background-color: #fff;
      z-index: calc(#{z-index(sticky)} - 1);
    }

    @media (max-width: $breakpoint-md) {
      padding: 15px;
    }

    @media (max-width: $breakpoint-sm) {
      display: none; // Hide header on mobile for more space
    }

    .header {
      font-size: 14px;
      font-weight: 600;
    }

    .header1 {
      width: 33%;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 5px;
      position: relative;
      user-select: none;

      @media (max-width: $breakpoint-lg) {
        width: 40%;
      }

      .label {
        cursor: pointer;
      }

      .arrowDownIcon {
        cursor: pointer;
        z-index: z-index(base);
      }

      // Room type dropdown
      .room-type-dropdown {
        width: 300px;
        padding: 15px 0;
        position: absolute;
        top: 35px;
        left: 0;
        background-color: white;
        border-radius: 20px;
        z-index: z-index(dropdown);
        box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.2);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;

        @media (max-width: $breakpoint-md) {
          width: 250px;
        }

        @media (max-width: $breakpoint-sm) {
          width: 200px;
          left: -10px;
        }

        &.active {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
        }

        &__item {
          padding: 10px 20px;
          font-size: 14px;
          color: #17181c;
          cursor: pointer;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: color.adjust(white, $lightness: -3%);
          }
        }
      }

      // Dropdown overlay
      .room-type-dropdown-overlay {
        position: fixed;
        inset: 0;
        z-index: z-index(overlay);
        background-color: transparent;
      }
    }

    .header2 {
      width: 25%;
    }

    .header3 {
      width: 15%;
    }

    .header4 {
      width: 10%;
    }

    .header5 {
      width: auto;
    }
  }

  // Main content wrapper
  .room-selection-main-content {
    display: flex;
    flex-direction: row;

    @media (max-width: $breakpoint-lg) {
      flex-direction: column;
    }

    // Rooms section
    .rooms-section {
      width: calc(100% - 210px);

      @media (max-width: $breakpoint-lg) {
        width: 100%;
      }

      // Individual room card
      .room-selection-card {
        width: 100%;
        display: flex;
        flex-direction: column;

        .room-selection-card-content {
          width: 100%;
          display: flex;
          flex-direction: row;
          padding: 0 0 0 10px;
          border-left: 1px solid rgba(0, 0, 0, 0.1);

          @media (max-width: $breakpoint-lg) {
            flex-direction: column;
            padding: 0;
          }

          @media (max-width: $breakpoint-md) {
            border-radius: 10px;
          }

          // Item 1 - Room Details
          .item1 {
            width: 40%;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            padding: 20px 20px 0 20px;

            @media (max-width: $breakpoint-lg) {
              width: 100%;
              border-right: none;
              border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }

            @media (max-width: $breakpoint-md) {
              padding: 15px 15px 0 15px;
            }

            h5 {
              font-size: 20px;
              font-weight: 600;
              margin-bottom: 5px;

              @media (max-width: $breakpoint-md) {
                font-size: 18px;
              }
            }

            .img-carousal {
              max-width: 100%;
              height: 200px;
              cursor: pointer;

              @media (max-width: $breakpoint-md) {
                height: 180px;
              }

              @media (max-width: $breakpoint-sm) {
                height: 150px;
              }
            }

            .rooms-left {
              font-size: 13px;
              color: red;
              font-weight: 600;
              padding: 10px 0;
            }

            .details {
              margin: 15px 0 5px 0;
              border-bottom: 1px solid rgba(0, 0, 0, 0.1);

              h5 {
                font-size: 20px;
                font-weight: 600;

                @media (max-width: $breakpoint-md) {
                  font-size: 18px;
                }
              }

              ul {
                text-decoration: none;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                gap: 5px;
                margin-bottom: 10px;

                @media (max-width: $breakpoint-sm) {
                  gap: 2px;
                }

                li {
                  padding: 0 5px;
                  font-size: 14px;
                  font-weight: 500;
                  display: flex;
                  flex-direction: row;
                  align-items: center;

                  @media (max-width: $breakpoint-md) {
                    font-size: 12px;
                  }

                  .material-icons {
                    font-size: 18px;

                    @media (max-width: $breakpoint-md) {
                      font-size: 16px;
                    }
                  }

                  span {
                    padding: 0 5px;
                  }
                }
              }
            }

            .amenities {
              ul {
                text-decoration: none;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                gap: 5px;
                margin: 15px 0 10px 0;

                @media (max-width: $breakpoint-md) {
                  gap: 2px;
                  margin: 10px 0 10px 0;
                }

                li {
                  padding: 0 5px;
                  font-size: 14px;
                  font-weight: 500;
                  display: flex;
                  flex-direction: row;
                  align-items: center;

                  @media (max-width: $breakpoint-md) {
                    font-size: 12px;
                    padding: 0 3px;
                  }

                  .material-icons {
                    font-size: 18px;

                    @media (max-width: $breakpoint-md) {
                      font-size: 16px;
                    }
                  }

                  span {
                    padding: 0 5px;

                    @media (max-width: $breakpoint-md) {
                      padding: 0 3px;
                    }
                  }
                }
              }

              .more-amenities {
                font-size: 13px;
                color: $secondary-color;
                font-weight: 600;
                cursor: pointer;
              }
            }

            .viewDetails {
              font-size: 14px;
              font-weight: 700;
              color: $primary_color;
              margin: 0 0 20px 0;
              cursor: pointer;
            }
          }

          // Item 2 - Options and Pricing
          .item2 {
            width: 60%;
            display: flex;
            flex-direction: column;

            @media (max-width: $breakpoint-lg) {
              width: 100%;
            }

            &__row {
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: row;
              border-bottom: 1px solid rgba(0, 0, 0, 0.1);

              &:last-child {
                border-bottom: none;
              }

              @media (max-width: $breakpoint-md) {
                flex-direction: column;
              }

              // Option details
              .option {
                width: 60%;
                padding: 20px;
                border-right: 1px solid rgba(0, 0, 0, 0.1);

                @media (max-width: $breakpoint-lg) {
                  padding: 15px;
                }

                @media (max-width: $breakpoint-md) {
                  width: 100%;
                  border-right: none;
                  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                  padding: 15px;
                }

                &:last-child {
                  border-bottom: none;
                }

                .heading {
                  font-size: 18px;
                  font-weight: 600;
                  margin: 0 0 15px 0;

                  @media (max-width: $breakpoint-md) {
                    font-size: 16px;
                    margin: 0 0 10px 0;
                  }
                }

                ul {
                  list-style: none;
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  flex-wrap: wrap;
                  column-gap: 15px;
                  row-gap: 10px;

                  @media (max-width: $breakpoint-lg) {
                    column-gap: 10px;
                    row-gap: 8px;
                  }

                  li {
                    display: flex;
                    flex-direction: row;
                    align-items: start;
                    gap: 5px;
                    font-size: 16px;
                    font-weight: 500;

                    .fa-solid {
                      margin-top: 3px;
                    }

                    @media (max-width: $breakpoint-lg) {
                      font-size: 14px;
                    }

                    @media (max-width: $breakpoint-sm) {
                      font-size: 12px;
                    }

                    &.non-refundable {
                      color: #dc3532;
                    }

                    &.free-cancellation {
                      color: #238c46;
                    }
                  }
                }
              }

              // Total guests
              .totalguests {
                padding: 20px;
                width: 20%;
                border-right: 1px solid rgba(0, 0, 0, 0.1);

                @media (max-width: $breakpoint-md) {
                  width: 100%;
                  border-right: none;
                  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                  padding: 15px;
                }

                .fa-user {
                  color: #17181c;
                  font-size: 13px;
                }
              }

              // Price section
              .price {
                width: 38%;
                padding: 20px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                border-right: 1px solid rgba(0, 0, 0, 0.1);
                display: flex;
                flex-direction: column;

                @media (max-width: $breakpoint-lg) {
                  padding: 15px;
                }

                @media (max-width: $breakpoint-md) {
                  width: 100%;
                  padding: 15px;
                  border-right: none;
                }

                &:last-child {
                  border-bottom: none;
                }

                .pricing {
                  margin-bottom: 15px;

                  .rounded-indicator {
                    width: fit-content;
                    padding: 0 4px;
                    border: 1px solid $primary_color;
                    border-radius: 9999px;
                    background-color: #faf7f4;
                    margin-bottom: 10px;

                    span {
                      padding: 0 5px;
                      font-size: 14px;
                      font-weight: 500;
                      color: $primary_color;

                      @media (max-width: $breakpoint-sm) {
                        font-size: 12px;
                      }
                    }
                  }

                  .discountAmt-amt {
                    display: flex;
                    flex-direction: row;
                    align-items: center;

                    .discountAmt {
                      padding: 0 0 0 5px;
                      font-size: 12px;
                      color: #5e616e;
                      font-weight: 400;
                      text-decoration: line-through;
                    }

                    .amt {
                      padding: 0 0 0 5px;
                      font-size: 20px;
                      font-weight: 600;
                      color: #17181c;

                      @media (max-width: $breakpoint-md) {
                        font-size: 18px;
                      }
                    }
                  }

                  p {
                    font-size: 12px;
                    color: #5e616e;
                    font-weight: 400;
                  }
                }

                .roomAvailability {
                  margin-bottom: 25px;

                  @media (max-width: $breakpoint-md) {
                    margin-bottom: 15px;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: space-between;
                  }

                  @media (max-width: $breakpoint-sm) {
                    flex-direction: column;
                    align-items: flex-start;
                  }

                  p {
                    font-size: 12px;
                    font-weight: 500;
                    color: $primary_color;
                    padding: 0 5px 7px 0;

                    @media (max-width: $breakpoint-md) {
                      padding: 0 10px 0 0;
                    }
                  }

                  // Room quantity dropdown
                  .room-quantity-dropdown {
                    position: relative;
                    width: fit-content;
                    min-width: 120px;

                    .quantity-selector-button {
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      padding: 8px 12px;
                      background-color: white;
                      color: #333;
                      border: 1px solid #ddd;
                      border-radius: 4px;
                      cursor: pointer;
                      font-size: 14px;
                      font-weight: 400;
                      transition: border-color 0.2s ease;
                      min-height: 36px;

                      &:hover {
                        border-color: #999;
                      }

                      &:focus {
                        outline: none;
                        border-color: $primary_color;
                      }

                      .quantity-text {
                        font-weight: 600;
                        font-size: 16px;
                        color: #333;
                      }

                      .room-text {
                        font-size: 14px;
                        color: #666;
                        margin-left: 2px;
                      }

                      i {
                        font-size: 12px;
                        color: #666;
                        margin-left: auto;
                      }
                    }

                    .dropdown-overlay {
                      position: fixed;
                      inset: 0;
                      z-index: z-index(dropdown-1);
                      background-color: transparent;
                    }

                    .quantity-dropdown-menu {
                      position: absolute;
                      top: 100%;
                      left: 0;
                      right: 0;
                      background-color: white;
                      border: 1px solid rgba(0, 0, 0, 0.1);
                      border-radius: 8px;
                      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                      z-index: z-index(dropdown);
                      overflow: hidden;

                      .quantity-option {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 12px 16px;
                        cursor: pointer;
                        transition: background-color 0.2s ease;
                        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

                        &:last-child {
                          border-bottom: none;
                        }

                        &:hover {
                          background-color: rgba(0, 0, 0, 0.05);
                        }

                        .quantity-number {
                          font-weight: 600;
                          font-size: 16px;
                          color: #17181c;
                        }

                        .quantity-price {
                          font-size: 14px;
                          color: #5e616e;
                          font-weight: 500;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // Item 5 - Booking Summary (Sticky)
    .item5.booking-summary {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      background: #f2f5fc;
      width: 210px;
      // Add relative positioning to create a containing block for the sticky element
      position: relative;

      @media (max-width: $breakpoint-lg) {
        width: 100%;
        border-left: none;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding: 15px;
        // Remove sticky behavior on mobile/tablet
        position: static;
      }

      @media (max-width: $breakpoint-md) {
        padding: 15px;
      }

      .booking-summary-content {
        border-radius: 12px;
        padding: 15px;
        color: black;
        width: 100%;
        max-width: 280px;

        // Make it sticky within its parent container
        position: sticky;
        top: 160px; // Adjust this value based on your header height

        // Ensure it doesn't overflow the container
        align-self: flex-start;

        @media (max-width: $breakpoint-lg) {
          // Remove sticky behavior on smaller screens
          position: static;
          top: auto;
          padding: 15px;
          max-width: 100%;
        }

        @media (max-width: $breakpoint-md) {
          padding: 15px;
          max-width: 100%;
        }

        .summary-header {
          margin-bottom: 8px;

          h4 {
            font-size: 16px;
            font-weight: 600;
            margin: 0;

            @media (max-width: $breakpoint-md) {
              font-size: 15px;
            }
          }
        }

        .total-price {
          display: flex;
          align-items: baseline;
          margin-bottom: 8px;

          .currency {
            font-size: 18px;
            font-weight: 500;
            margin-right: 2px;

            @media (max-width: $breakpoint-md) {
              font-size: 16px;
            }
          }

          .amount {
            font-size: 28px;
            font-weight: 700;
            line-height: 1;

            @media (max-width: $breakpoint-md) {
              font-size: 24px;
            }
          }
        }

        .taxes-info {
          margin-bottom: 20px;
          font-size: 13px;

          @media (max-width: $breakpoint-md) {
            margin-bottom: 15px;
            font-size: 12px;
          }

          .tax-amount {
            font-weight: 600;
          }

          .tax-label {
            font-weight: 400;
          }
        }

        .reserve-button {
          width: 100%;
          background-color: $primary_color;
          color: #fff;
          border: none;
          border-radius: 8px;
          padding: 12px 16px;
          font-size: 16px;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.2s ease;
          margin-bottom: 12px;

          @media (max-width: $breakpoint-md) {
            padding: 10px 14px;
            font-size: 15px;
          }

          &:hover:not(:disabled) {
            background-color: color.adjust($primary_color, $lightness: -5%);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }

          &:disabled {
            background-color: rgba(255, 255, 255, 0.5);
            color: rgba(53, 122, 189, 0.6);
            cursor: not-allowed;
          }
        }

        .no-charge-info {
          display: flex;
          align-items: start;
          gap: 6px;
          font-size: 12px;
          color: #17181c;

          @media (max-width: $breakpoint-md) {
            font-size: 11px;
          }

          i {
            font-size: 14px;
            color: #4ade80;
            margin-top: 5px;

            @media (max-width: $breakpoint-md) {
              font-size: 12px;
            }
          }

          span {
            font-weight: 500;
          }
        }

        .max-rooms-info{
          font-size: 12px;
          margin-top: 5px;
        }
      }
    }
  }
}

// Additional utility styles
.room-selection-card-btn-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 10px;

  .room-selection-card-btn {
    margin: 0 0 20px 0;
    width: fit-content;
    padding: 2px 8px;
    border: 1px solid rgba(0, 0, 0, 0.4);
    border-radius: 9999px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }
}

// Modal Booking Summary
.modal-booking-summary {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 20px;
  margin: 0 -20px -20px -20px;
  z-index: 10;

  @media (max-width: $breakpoint-md) {
    padding: 15px;
    margin: 0 -15px -15px -15px;
  }

    &__content {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    @media (max-width: $breakpoint-md) {
      padding: 15px;
    }

    &__summary-header {
      margin-bottom: 8px;

      h4 {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        color: rgba(255, 255, 255, 0.95);

        @media (max-width: $breakpoint-md) {
          font-size: 15px;
        }
      }
    }

    &__total-price {
      display: flex;
      align-items: baseline;
      justify-content: center;
      margin-bottom: 8px;

      .currency {
        font-size: 18px;
        font-weight: 500;
        margin-right: 2px;

        @media (max-width: $breakpoint-md) {
          font-size: 16px;
        }
      }

      .amount {
        font-size: 28px;
        font-weight: 700;
        line-height: 1;

        @media (max-width: $breakpoint-md) {
          font-size: 24px;
        }
      }
    }

    &__taxes-info {
      margin-bottom: 20px;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.85);

      @media (max-width: $breakpoint-md) {
        margin-bottom: 15px;
        font-size: 12px;
      }

      .tax-amount {
        font-weight: 600;
      }

      .tax-label {
        font-weight: 400;
      }
    }

    .modal-booking-summary__reserve-button {
      width: 100%;
      background-color: rgba(255, 255, 255, 0.95);
      color: #357abd;
      border: none;
      border-radius: 8px;
      padding: 12px 16px;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 12px;

      @media (max-width: $breakpoint-md) {
        padding: 10px 14px;
        font-size: 15px;
      }

      &:hover:not(:disabled) {
        background-color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &:disabled {
        background-color: rgba(255, 255, 255, 0.5);
        color: rgba(53, 122, 189, 0.6);
        cursor: not-allowed;
      }
    }

    &__no-charge-info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.9);

      @media (max-width: $breakpoint-md) {
        font-size: 11px;
      }

      i {
        font-size: 14px;
        color: #4ade80;

        @media (max-width: $breakpoint-md) {
          font-size: 12px;
        }
      }

      span {
        font-weight: 500;
      }
    }
  }
}

.room-filter-options {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  .filter-label {
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;

    @media (max-width: $breakpoint-md) {
      font-size: 14px;
    }
  }

  .filter-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    flex-grow: 1;

    @media (max-width: $breakpoint-md) {
      gap: 8px;
      margin: 10px 0;
    }
  }

  .filter-option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 14px;

    input[type="checkbox"] {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }

    span {
      font-weight: 500;
    }
  }

  .search-rooms {
    position: relative;
    margin-left: auto;

    @media (max-width: $breakpoint-md) {
      width: 100%;
      margin-left: 0;
    }

    input {
      padding: 8px 32px 8px 12px;
      border: 1px solid rgba(0, 0, 0, 0.2);
      border-radius: 8px;
      font-size: 14px;
      width: 200px;

      @media (max-width: $breakpoint-md) {
        width: 100%;
      }

      &:focus {
        outline: none;
        border-color: $primary_color;
      }
    }

    .search-icon {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 20px;
      color: rgba(0, 0, 0, 0.5);
    }
  }
}

// Modal styles
.slide-from-right-modal {
  .modal-content {
    // @media (max-width: $breakpoint-lg) {
    //   width: 90%;
    //   max-width: 600px;
    // }

    // @media (max-width: $breakpoint-md) {
    //   width: 100%;
    //   max-width: 100%;
    //   height: 100%;
    //   border-radius: 0;
    // }

    .content {
      .room-selection-card-container__modal {
        width: 100%;
        padding: 30px;
        display: flex;
        flex-direction: row;
        gap: 20px;

        @media (max-width: $breakpoint-lg) {
          padding: 20px;
        }

        @media (max-width: $breakpoint-md) {
          flex-direction: column;
          padding: 15px;
          gap: 15px;
        }

        .content {
          height: auto;
        }

        .content1 {
          width: 50%;
          border-radius: 10px;
          padding: 20px 20px 0 20px;

          @media (max-width: $breakpoint-md) {
            width: 100%;
            padding: 15px 15px 0 15px;
          }

          .img-carousal {
            max-width: 100%;
            height: 200px;

            @media (max-width: $breakpoint-md) {
              height: 180px;
            }

            @media (max-width: $breakpoint-sm) {
              height: 150px;
            }
          }

          .details {
            margin: 15px 0 5px 0;

            h5 {
              font-size: 20px;
              font-weight: 600;

              @media (max-width: $breakpoint-md) {
                font-size: 18px;
              }
            }

            ul {
              text-decoration: none;
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              gap: 5px;
              margin-bottom: 10px;

              @media (max-width: $breakpoint-sm) {
                gap: 2px;
              }

              li {
                padding: 0 5px 0 0;
                font-size: 14px;
                font-weight: 500;
                display: flex;
                flex-direction: row;
                align-items: center;

                @media (max-width: $breakpoint-md) {
                  font-size: 12px;
                }

                .material-icons {
                  font-size: 18px;
                  margin-right: 5px;

                  @media (max-width: $breakpoint-md) {
                    font-size: 16px;
                  }
                }
              }
            }
          }

          .amenities {
            h2 {
              font-size: 24px;
              color: #17181c;
              font-weight: 700;

              @media (max-width: $breakpoint-md) {
                font-size: 20px;
              }
            }
            padding-top: 15px;
            ul {
              text-decoration: none;
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              gap: 5px;
              margin: 0 0 10px 0;
              row-gap: 10px;

              @media (max-width: $breakpoint-md) {
                gap: 3px;
                row-gap: 8px;
              }

              li {
                padding: 0 5px 0 0;
                font-size: 14px;
                font-weight: 500;
                display: flex;
                flex-direction: row;
                align-items: center;

                @media (max-width: $breakpoint-md) {
                  font-size: 12px;
                }

                .material-icons {
                  font-size: 18px;

                  @media (max-width: $breakpoint-md) {
                    font-size: 16px;
                  }
                }

                span {
                  padding: 0 0 0 5px;
                }
              }
            }
          }
        }

        .content2 {
          //width: 50%;
          display: flex;
          flex-direction: column;
          gap: 20px;

          @media (max-width: $breakpoint-md) {
            width: 100%;
            gap: 15px;
          }

          .option {
            height: auto;
            padding: 20px 20px 0 20px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 10px;

            @media (max-width: $breakpoint-md) {
              padding: 15px 15px 0 15px;
            }

            .heading {
              font-size: 18px;
              font-weight: 600;
              margin: 0 0 15px 0;

              @media (max-width: $breakpoint-md) {
                font-size: 16px;
                margin: 0 0 10px 0;
              }
            }

            ul {
              list-style: none;
              display: flex;
              flex-direction: column;
              row-gap: 10px;
              padding-bottom: 15px;
              border-bottom: 1px solid rgba(0, 0, 0, 0.2);

              @media (max-width: $breakpoint-md) {
                row-gap: 8px;
                padding-bottom: 10px;
              }

              li {
                display: flex;
                flex-direction: row;
                align-items: center;
                gap: 5px;
                font-size: 16px;
                font-weight: 500;

                @media (max-width: $breakpoint-md) {
                  font-size: 14px;
                }

                @media (max-width: $breakpoint-sm) {
                  font-size: 12px;
                }
              }

              .non-refundable {
                color: #dc3532;
              }

              .free-cancellation {
                color: #238c46;
              }
            }

            .price {
              width: 100%;
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              padding: 15px 0px 20px 0;

              @media (max-width: $breakpoint-md) {
                padding: 10px 0px 15px 0;
              }

              @media (max-width: $breakpoint-sm) {
                flex-direction: column;
              }

              &:last-child {
                border-bottom: none;
              }

              .pricing {
                .rounded-indicator {
                  width: fit-content;
                  padding: 0 4px;
                  border: 1px solid $primary_color;
                  border-radius: 9999px;
                  background-color: rgba(8, 119, 103, 0.05);
                  margin-bottom: 10px;

                  span {
                    padding: 0 5px;
                    font-size: 14px;
                    font-weight: 500;
                    color: $primary_color;

                    @media (max-width: $breakpoint-sm) {
                      font-size: 12px;
                    }
                  }
                }

                .discountAmt-amt {
                  display: flex;
                  flex-direction: row;
                  align-items: center;

                  .discountAmt {
                    padding: 0 0 0 5px;
                    font-size: 12px;
                    color: #5e616e;
                    font-weight: 400;
                    text-decoration: line-through;
                  }

                  .amt {
                    padding: 0 0 0 5px;
                    font-size: 12px;
                    color: #17181c;
                    font-size: 20px;
                    font-weight: 600;

                    @media (max-width: $breakpoint-md) {
                      font-size: 18px;
                    }
                  }
                }

                p {
                  font-size: 12px;
                  color: #5e616e;
                  font-weight: 400;
                }
              }

              .roomAvailability {
                display: flex;
                flex-direction: column;
                justify-content: end;

                @media (max-width: $breakpoint-sm) {
                  margin-top: 10px;
                }

                p {
                  font-size: 12px;
                  font-weight: 500;
                  color: $primary_color;
                  padding: 0 5px 7px 0;
                }

                .room-quantity-dropdown {
                  position: relative;
                  width: fit-content;
                  min-width: 120px;

                  .quantity-selector-button {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px 12px;
                    background-color: white;
                    color: #333;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: 400;
                    transition: border-color 0.2s ease;
                    min-height: 36px;

                    &:hover {
                      border-color: #999;
                    }

                    &:focus {
                      outline: none;
                      border-color: $primary_color;
                    }

                    .quantity-text {
                      font-weight: 600;
                      font-size: 16px;
                      color: #333;
                    }

                    .room-text {
                      font-size: 14px;
                      color: #666;
                      margin-left: 2px;
                    }

                    i {
                      font-size: 12px;
                      color: #666;
                      margin-left: auto;
                    }
                  }

                  .dropdown-overlay {
                    position: fixed;
                    inset: 0;
                    z-index: z-index(dropdown)-1;
                    background-color: transparent;
                  }

                  .quantity-dropdown-menu {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background-color: white;
                    border: 1px solid rgba(0, 0, 0, 0.1);
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: z-index(dropdown)-1;
                    overflow: hidden;
                    min-width: 120px;
                    max-height: 200px;
                    overflow-y: auto;

                    .quantity-option {
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      padding: 12px 16px;
                      cursor: pointer;
                      transition: background-color 0.2s ease;
                      border-bottom: 1px solid rgba(0, 0, 0, 0.05);

                      &:last-child {
                        border-bottom: none;
                      }

                      &:hover {
                        background-color: rgba(0, 0, 0, 0.05);
                      }

                      .quantity-number {
                        font-weight: 600;
                        font-size: 16px;
                        color: #17181c;
                      }

                      .quantity-price {
                        font-size: 14px;
                        color: #5e616e;
                        font-weight: 500;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// Mobile Layout Styles - Accordion Approach (≤950px)
.mobile-rooms-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 10px;
  margin-bottom: 120px; // Default space for sticky booking summary - will be dynamically updated
  overflow: visible; // Allow dropdowns to extend outside

  @media (max-width: $breakpoint-md) {
    padding: 0;
    
  }

  @media (min-width: 951px) {
    display: none; // Hide on desktop
  }

  .mobile-room-accordion {
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    overflow: visible; // Allow dropdowns to extend outside
  }

  .mobile-room-header {
    padding: 20px;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f9fafb;
    }

    .room-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;
    }

    .room-basic-info {
      flex: 1;

      h5 {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin: 0 0 8px 0;
      }

      .rooms-left {
        font-size: 13px;
        color: #dc2626;
        font-weight: 500;
        margin: 0 0 8px 0;
      }

      .room-price-preview {
        .from-price {
          font-size: 16px;
          font-weight: 600;
          color: #059669;
        }
      }
    }

    .expand-controls {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .options-preview {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
      }

      .expand-icon {
        font-size: 16px;
        color: #4b5563;
        transition: transform 0.2s ease;
      }
    }
  }

  .mobile-room-expanded-content {
    padding: 10px;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    overflow: visible; // Allow dropdowns to extend outside

    .room-details-section {
      margin-bottom: 24px;

      .img-carousal {
        max-width: 100%;
        height: 200px;
        margin-bottom: 16px;
        border-radius: 8px;
        overflow: hidden;
      }

      .details {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e5e7eb;

        ul {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;

            .material-icons {
              font-size: 16px;
              color: #6b7280;
            }
          }
        }
      }

      .amenities {
        margin-bottom: 16px;

        ul {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;

            .material-icons {
              font-size: 16px;
              color: #10b981;
            }
          }
        }

        .more-amenities {
          font-size: 13px;
          color: #3b82f6;
          font-weight: 600;
          cursor: pointer;
          margin-top: 10px;
        }
      }

      .viewDetails {
        font-size: 14px;
        font-weight: 600;
        color: #3b82f6;
        cursor: pointer;
        margin: 0;
      }
    }

    .room-options-section {
      overflow: visible; // Allow dropdowns to extend outside

      .options-title {
        font-size: 16px;
        font-weight: 600;
        color: #111827;
        margin: 0 0 16px 0;
      }

      // Horizontal scroll container for mobile room option cards
      .mobile-room-options-scroll-container {
        display: flex;
        gap: 12px; // Reduced gap to match smaller padding
        overflow-x: auto;
        overflow-y: visible; // Allow dropdowns to extend outside
        padding: 0 0 10px 0; // Reduced bottom padding
        margin: 0 -10px; // Adjusted negative margin to match parent padding
        padding-left: 10px;
        padding-right: 10px;

        // Hide scrollbar but keep functionality
        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // IE/Edge

        &::-webkit-scrollbar {
          display: none; // Chrome/Safari
        }

        // Smooth scrolling
        scroll-behavior: smooth;

        // Snap scrolling for better UX
        scroll-snap-type: x mandatory;

        // Note: Gradient fade effects removed to prevent stacking context issues
        // that were causing white space above mobile booking summary
      }
    }
  }

  .mobile-room-option-card {
    background: white;
    border-radius: 12px;
    padding: 12px; // Reduced padding to match overall design
    border: 1px solid #e5e7eb;
    overflow: visible; // Allow dropdowns to extend outside
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;

    // Horizontal scroll layout
    flex: 0 0 280px; // Fixed width for horizontal scroll
    min-width: 280px;
    max-width: 280px;
    scroll-snap-align: start; // Snap to start of card

    // Remove bottom margin for horizontal layout
    margin-bottom: 0;

    // Hover effect for better interactivity
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    // Responsive width adjustments
    @media (max-width: 480px) {
      flex: 0 0 260px;
      min-width: 260px;
      max-width: 260px;
      padding: 14px;
    }

    @media (max-width: 360px) {
      flex: 0 0 240px;
      min-width: 240px;
      max-width: 240px;
      padding: 12px;
    }

    .mobile-option-header {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 15px;

      .heading {
        font-size: 18px;
        font-weight: 600;
        color: #17181c;
        margin: 0;
      }

      .mobile-guests {
        display: flex;
        gap: 5px;

        .fa-user {
          color: #17181c;
          font-size: 14px;
        }
      }
    }

    .mobile-option-benefits {
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);

      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 8px;

        li {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          font-weight: 500;

          &.non-refundable {
            color: #dc3532;
          }

          &.free-cancellation {
            color: #238c46;
          }

          .fa-solid {
            font-size: 14px;
          }
        }
      }
    }

    .mobile-price-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      gap: 20px;
      overflow: visible; // Allow dropdowns to extend outside

      @media (max-width: 480px) {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
      }

      .mobile-pricing {
        flex: 1;

        .rounded-indicator {
          width: fit-content;
          padding: 2px 8px;
          border: 1px solid $primary_color;
          border-radius: 9999px;
          background-color: #faf7f4;
          margin-bottom: 8px;

          span {
            font-size: 12px;
            font-weight: 500;
            color: $primary_color;
          }
        }

        .discountAmt-amt {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 5px;

          .discountAmt {
            font-size: 12px;
            color: #5e616e;
            font-weight: 400;
            text-decoration: line-through;
          }

          .amt {
            font-size: 20px;
            font-weight: 600;
            color: #17181c;
          }
        }

        p {
          font-size: 12px;
          color: #5e616e;
          font-weight: 400;
          margin: 2px 0;
        }
      }

      .mobile-room-availability {
        flex-shrink: 0;
        text-align: right;

        @media (max-width: 480px) {
          text-align: left;
        }

        p {
          font-size: 12px;
          font-weight: 500;
          color: $primary_color;
          margin-bottom: 8px;
        }

        .room-quantity-counter {
          width: 100%;

          .quantity-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 8px 12px;

            .quantity-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              border: 1px solid $primary_color;
              border-radius: 6px;
              background-color: white;
              color: $primary_color;
              cursor: pointer;
              transition: all 0.2s ease;
              font-size: 14px;

              &:hover:not(:disabled) {
                background-color: $primary_color;
                color: white;
                transform: scale(1.05);
              }

              &:disabled {
                opacity: 0.4;
                cursor: not-allowed;
                transform: none;
              }

              &.decrease {
                &:disabled {
                  border-color: #ccc;
                  color: #ccc;
                }
              }

              &.increase {
                &:disabled {
                  border-color: #ccc;
                  color: #ccc;
                }
              }
            }

            .quantity-display {
              display: flex;
              align-items: center;
              justify-content: center;
              min-width: 80px; // Increased width to accommodate text
              height: 32px;
              padding: 0 12px; // Add horizontal padding
              font-size: 14px; // Slightly smaller font for better fit
              font-weight: 600;
              color: $primary_color;
              background-color: white;
              border: 2px solid $primary_color;
              border-radius: 6px;
              white-space: nowrap; // Prevent text wrapping
            }
          }
        }
      }
    }
  }
}

// Mobile Sticky Booking Summary - Compact Industry Standard Design
.mobile-sticky-booking-summary {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: z-index(dropdown-1);
  padding: 12px 16px; // Reduced padding

  @media (min-width: 951px) {
    display: none; // Hide on desktop
  }

  .mobile-booking-summary-content {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border-radius: 8px; // Smaller border radius
    padding: 12px 16px; // Much smaller padding
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;

    // Left side - Summary info
    .mobile-summary-left {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      flex: 1;

      .mobile-summary-header {
        h4 {
          font-size: 13px; // Smaller font
          font-weight: 600;
          margin: 0 0 2px 0;
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .mobile-total-price {
        display: flex;
        align-items: baseline;
        gap: 2px;

        .currency {
          font-size: 16px;
          font-weight: 600;
        }

        .amount {
          font-size: 20px; // Much smaller
          font-weight: 700;
          line-height: 1;
        }
      }

      .mobile-taxes-info {
        font-size: 11px; // Smaller font
        color: rgba(255, 255, 255, 0.8);
        margin-top: 2px;

        .tax-amount {
          font-weight: 500;
        }
      }
    }

    // Right side - Book button
    .mobile-reserve-button {
      background-color: rgba(255, 255, 255, 0.95);
      color: #357abd;
      border: none;
      border-radius: 6px;
      padding: 10px 20px; // Compact padding
      font-size: 14px; // Smaller font
      font-weight: 700;
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;
      min-width: 100px;

      &:hover:not(:disabled) {
        background-color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &:disabled {
        background-color: rgba(255, 255, 255, 0.5);
        color: rgba(53, 122, 189, 0.6);
        cursor: not-allowed;
      }
    }

    // Additional info (show only when needed)
    .mobile-additional-info {
      position: absolute;
      bottom: 100%;
      left: 16px;
      right: 16px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 6px 6px 0 0;
      font-size: 11px;
      display: none; // Hidden by default

      &.show {
        display: block;
      }

      .mobile-no-charge-info {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        i {
          font-size: 12px;
          color: #4ade80;
        }
      }

      .mobile-max-rooms-info {
        text-align: center;
        margin-top: 4px;

        i {
          margin-right: 4px;
        }
      }
    }
  }
}

