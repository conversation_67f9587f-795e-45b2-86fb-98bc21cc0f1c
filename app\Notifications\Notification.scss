/* Enhanced Notification Page Styles */
@use "/styles/variable" as *;

.notifications-page {
  padding: 2rem 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.notifications-container {
  max-width: 750px;
  margin: 0 auto;
  padding: 0 1rem;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notifications-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.unread-count {
  font-size: 0.875rem;
  color: #666;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.mark-read-button,
.clear-all-button {
  background-color: transparent;
  color: $primary-color;
  border: none;
  padding: 0.5rem 0;
  font-size: 0.875rem;
  cursor: pointer;
  font-weight: 500;
}

.mark-read-button:hover,
.clear-all-button:hover {
  color: #5a8a8a;
  text-decoration: underline;
}

.clear-all-button {
  color: #dc3545;
}

.clear-all-button:hover {
  color: #c82333;
}

.loading {
  text-align: center;
  padding: 2rem 0;
  color: #666;
}

.notifications-list {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.notification-item {
  padding: 1rem;
  border-bottom: 1px solid #eee;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background-color: #fafafa;
}

.notification-item.unread:hover {
  background-color: #f0f0f0;
}

.notification-item.unread:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: $primary-color;
}

.notification-content {
  padding-left: 0.5rem;
  flex: 1;
}

.notification-message {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9375rem;
  line-height: 1.5;
}

.notification-item.unread .notification-message {
  font-weight: 500;
}

.notification-time {
  font-size: 0.75rem;
  color: #888;
}

.notification-actions {
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.mark-read-btn,
.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.mark-read-btn {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
}

.mark-read-btn:hover {
  background-color: rgba(40, 167, 69, 0.2);
  transform: scale(1.1);
}

.delete-btn {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
  font-size: 1rem;
}

.delete-btn:hover {
  background-color: rgba(220, 53, 69, 0.2);
  transform: scale(1.1);
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-state p {
  margin-bottom: 1rem;
  color: #666;
}

.empty-state a {
  display: inline-block;
  color: $primary-color;
  text-decoration: none;
  font-weight: 500;
}

.empty-state a:hover {
  text-decoration: underline;
}

@media (max-width: 640px) {
  .notifications-page {
    padding: 1rem 0;
  }
  
  .notifications-header {
    margin-bottom: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    align-self: flex-end;
  }

  .notification-actions {
    opacity: 1; // Always show on mobile
  }

  .notification-item {
    padding: 0.875rem;
  }
}