/**
 * Icon types available for map markers
 */
export type IconType = 'hotel' | 'poi' | 'restaurant' | 'attraction' | 'default';

/**
 * Creates a custom icon for Leaflet map markers
 * This solves the issue of missing marker icons in Next.js
 *
 * @param iconType - The type of icon to use ('hotel', 'poi', 'restaurant', 'attraction', or 'default')
 */
export function createIcon(iconType: IconType = 'default') {
  // Define icon URLs based on type
  let iconUrl = '/marker-icon.png'; // Default fallback
  
  // Select icon based on type
  switch (iconType) {
    case 'hotel':
      iconUrl = '/hotel-marker-icon.svg';
      break;
    case 'poi':
      iconUrl = '/poi-marker.svg';
      break;
    case 'restaurant':
      iconUrl = '/restaurant-marker.svg';
      break;
    case 'attraction':
      iconUrl = '/attraction-marker.svg';
      break;
    default:
      iconUrl = '/marker-icon.png';
  }
  
  // This function will be called client-side after Leaflet is loaded
  // The actual icon creation happens in the Map component
  return {
    iconUrl,
    iconSize: [60, 72],
    iconAnchor: [20, 48],
    popupAnchor: [0, -44],
    tooltipAnchor: [20, -35]
  };
}