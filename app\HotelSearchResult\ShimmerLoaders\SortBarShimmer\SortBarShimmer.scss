@use '/styles/variable' as *;

.sort-bar-shimmer {
    width: 100%;

    @media (max-width: $breakpoint-md) {
      display: none;
      
    }
  
    .shimmer {
      background: linear-gradient(90deg, #f6f7f8 25%, #eaeaea 50%, #f6f7f8 75%);
      background-size: 200% 100%;
      animation: shimmer-animation 1.5s infinite linear;
      border-radius: 4px;
    }

    .title-toggle{
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .shimmer-title {
            width: 250px;
            height: 20px;
          }

          .shimmer-toggle {
            display: flex;
            gap: 10px;

          
        
            .shimmer-button {
              width: 60px;
              height: 35px;
              border-radius: 20px;

              @media (max-width: $isMobile) {
                display: none;
                
              }
            }

            .shimmer-button2 {
              width: 80px;
              height: 35px;
              border-radius: 6px;

           
            }
          }
        
    }
  
  
    .shimmer-sort {
      width: 200px;
      height: 35px;
      border-radius: 20px;

      
      @media (max-width: $isMobile) {
        display: none;
        
      }
    }
  
   
  }
  
  @keyframes shimmer-animation {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  