"use client";
import React, { useState, useEffect } from "react";
import "./profile.scss";
import { usePathname, useRouter } from "next/navigation";
import Sidebar from "./components/Sidebar/Sidebar";
import NavigationBar from "./components/NavigationBar/NavigationBar";
import TripPlannerGrid from "./components/TripPlannerGrid/TripPlannerGrid";
import Link from "next/link";
import Reviews from "./components/Reviews/Reviews";
import Wishlist from "./components/Wishlist/Wishlist";
import EmailSection from "./components/EmailSection/EmailSection";
import PhoneNumberSection from "./components/PhoneNumberSection/PhoneNumberSection";
import { showToast } from "../components/utilities/SonnerToasterCustom";

// Types consolidated
type BookingStatus = "confirmed" | "pending" | "cancelled";
type SortOption = "default" | "date" | "price";
type EditSection = "phone" | "name" | "password" | "email";

interface BookingItem {
  id: string;
  type: "hotel";
  name: string;
  location: string;
  date: string;
  status: BookingStatus;
  price: number;
  reference: string;
  amenities?: string[];
}

interface NewsletterOption {
  type: string;
  isSelected: boolean;
}

// Map query param tab values to your UI tab values
const TAB_MAPPING = {
  Bookings: "My bookings",
  Profile: "Profile",
  Reviews: "Reviews",
  Wishlist: "Wishlist",
};

const MOCK_BOOKINGS: BookingItem[] = [
  {
    id: "1",
    type: "hotel",
    name: "Seaside Resort & Spa",
    location: "Miami, USA",
    date: "2025-06-10 - 2025-06-15",
    status: "confirmed",
    price: 1250.0,
    reference: "HT8241ZX",
    amenities: ["Private Beach", "Spa Access", "Infinity Pool"],
  },
  {
    id: "2",
    type: "hotel",
    name: "Grandeur Plaza Hotel",
    location: "London, UK",
    date: "2025-05-12 - 2025-05-15",
    status: "confirmed",
    price: 423.75,
    reference: "HT3982PL",
    amenities: ["Free WiFi", "Breakfast Included", "Swimming Pool"],
  },
  {
    id: "3",
    type: "hotel",
    name: "Mountain View Lodge",
    location: "Aspen, USA",
    date: "2025-04-20 - 2025-04-25",
    status: "confirmed",
    price: 875.5,
    reference: "HT5642MT",
    amenities: ["Ski-in/Ski-out", "Fireplace", "Hot Tub"],
  },
  {
    id: "4",
    type: "hotel",
    name: "Le Royale Suite",
    location: "Paris, France",
    date: "2025-05-15 - 2025-05-19",
    status: "confirmed",
    price: 876.25,
    reference: "HT7394QP",
    amenities: ["Luxury Suite", "Spa Access", "Room Service"],
  },
  {
    id: "5",
    type: "hotel",
    name: "Cherry Blossom Inn",
    location: "Kyoto, Japan",
    date: "2025-04-05 - 2025-04-10",
    status: "confirmed",
    price: 650.0,
    reference: "HT9274JP",
    amenities: ["Traditional Rooms", "Garden View", "Tea Ceremony"],
  },
  {
    id: "6",
    type: "hotel",
    name: "Desert Oasis Resort",
    location: "Dubai, UAE",
    date: "2025-07-20 - 2025-07-27",
    status: "pending",
    price: 1895.75,
    reference: "HT4619DB",
    amenities: ["Private Pool", "Desert Safari", "5-Star Dining"],
  },
  {
    id: "7",
    type: "hotel",
    name: "Budget Comfort Inn",
    location: "Berlin, Germany",
    date: "2025-06-01 - 2025-06-05",
    status: "confirmed",
    price: 325.0,
    reference: "HT2187BL",
    amenities: ["Continental Breakfast", "City Shuttle", "WiFi"],
  },
  {
    id: "8",
    type: "hotel",
    name: "Historic Downtown Hotel",
    location: "Rome, Italy",
    date: "2025-05-25 - 2025-05-30",
    status: "confirmed",
    price: 750.5,
    reference: "HT6391RM",
    amenities: ["City View", "Walking Tours", "Italian Restaurant"],
  },
];

const DEFAULT_NEWSLETTER_OPTIONS: NewsletterOption[] = [
  { type: "Daily", isSelected: true },
  { type: "Twice a week", isSelected: false },
  { type: "Weekly", isSelected: false },
  { type: "Never", isSelected: false },
];

function Page() {
  const router = useRouter();
  const searchParams = usePathname();

  // UI state
  const [ui, setUi] = useState({
    activeTab: "My bookings",
    activeStatus: "Upcoming",
    sortBy: "default" as SortOption,
    searchQuery: "",
  });

  // Edit modes grouped together
  const [editMode, setEditMode] = useState({
    name: false,
    phone: false,
    password: false,
    email: false,
  });

  // Form fields state
  const [formState, setFormState] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  });

  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  });

  // Toggle settings grouped together
  const [settings, setSettings] = useState({
    creditInfo: true,
    bookingAssistReminder: false,
    promotionsReminder: false,
    upcomingTripInfo: true,
  });

  const [newsLetterOptions, setNewsLetterOptions] = useState<
    NewsletterOption[]
  >(DEFAULT_NEWSLETTER_OPTIONS);

  // Get default values from local storage (or use empty strings/default values)
  const getUserDataFromLocalStorage = () => {
    // Only run on client side
    if (typeof window !== "undefined") {
      try {
        const userData = localStorage.getItem("userData");
        if (userData) {
          const parsedData = JSON.parse(userData);
          return {
            firstName: parsedData.firstName || "",
            lastName: parsedData.lastName || "",
            email: parsedData.email || "",
            phone: parsedData.phone || "",
          };
        }
      } catch (error) {
        console.error("Error parsing user data from localStorage:", error);
      }
    }

    // Default values if localStorage is not available or empty
    return {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    };
  };

  // Use effect to load data from localStorage and check URL params when component mounts
  useEffect(() => {
    // Set form values with data from localStorage
    const userData = getUserDataFromLocalStorage();
    setFormState((prevState) => ({
      ...prevState,
      ...userData,
    }));

    // Handle the tab parameter from URL
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get("tab");
    if (tabParam && TAB_MAPPING[tabParam as keyof typeof TAB_MAPPING]) {
      setUi((prev) => ({
        ...prev,
        activeTab: TAB_MAPPING[tabParam as keyof typeof TAB_MAPPING],
      }));
    }
  }, [searchParams]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormState((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "",
      }));
    }
  };

  // Validators
  const validatePhone = (phone: string) => {
    if (!phone) return "";
    return /^[0-9]{10}$/.test(phone) ? "" : "Phone number must be 10 digits";
  };

  const validateEmail = (email: string) => {
    if (!email) return "";
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) ? "" : "Please enter a valid email address";
  };

  const validatePassword = (password: string, confirmPassword: string) => {
    if (password !== confirmPassword) {
      return "Passwords do not match";
    }
    return "";
  };

  // Handlers consolidated with cleaner implementations
  const handleToggle = (key: keyof typeof settings) => {
    setSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleCheckboxChange = (index: number) => {
    setNewsLetterOptions((prev) =>
      prev.map((item, i) => ({
        ...item,
        isSelected: i === index,
      }))
    );
  };

  const handleEditMode = (section: EditSection, value: boolean) => {
    setEditMode((prev) => ({
      ...prev,
      [section]: value,
    }));
  };

  const validateForm = (section: EditSection) => {
    let isValid = true;
    const newErrors = { ...formErrors };

    switch (section) {
      case "phone":
        const phoneError = validatePhone(formState.phone);
        newErrors.phone = phoneError;
        isValid = !phoneError;
        break;
      case "email":
        const emailError = validateEmail(formState.email);
        newErrors.email = emailError;
        isValid = !emailError;
        break;
      case "name":
        isValid = !!formState.firstName && !!formState.lastName;
        newErrors.firstName = formState.firstName
          ? ""
          : "First name is required";
        newErrors.lastName = formState.lastName ? "" : "Last name is required";
        break;
      case "password":
        if (!formState.currentPassword) {
          newErrors.currentPassword = "Current password is required";
          isValid = false;
        }
        if (!formState.newPassword) {
          newErrors.newPassword = "New password is required";
          isValid = false;
        }
        if (!formState.confirmNewPassword) {
          newErrors.confirmNewPassword = "Please confirm your password";
          isValid = false;
        }

        if (formState.newPassword && formState.confirmNewPassword) {
          const passwordError = validatePassword(
            formState.newPassword,
            formState.confirmNewPassword
          );
          if (passwordError) {
            newErrors.confirmNewPassword = passwordError;
            isValid = false;
          }
        }

        if (
          formState.currentPassword &&
          formState.newPassword &&
          formState.currentPassword === formState.newPassword
        ) {
          newErrors.newPassword =
            "New password must be different from current password";
          isValid = false;
        }
        break;
    }

    setFormErrors(newErrors);
    return isValid;
  };

  const handleSave = (section: EditSection) => {
    const isValid = validateForm(section);
    if (!isValid) return;

    // Update localStorage with new values
    if (typeof window !== "undefined") {
      try {
        const existingData = localStorage.getItem("userData");
        const userData = existingData ? JSON.parse(existingData) : {};

        if (section === "name") {
          userData.firstName = formState.firstName;
          userData.lastName = formState.lastName;
          localStorage.setItem("userData", JSON.stringify(userData));
          alert(`Name saved: ${formState.firstName} ${formState.lastName}`);
          handleEditMode("name", false);
        }

        if (section === "phone") {
          userData.phone = formState.phone;
          localStorage.setItem("userData", JSON.stringify(userData));
          alert(`Phone number saved: ${formState.phone}`);
          handleEditMode("phone", false);
        }

        if (section === "email") {
          userData.email = formState.email;
          localStorage.setItem("userData", JSON.stringify(userData));
          alert(`Email saved: ${formState.email}`);
          handleEditMode("email", false);
        }

        if (section === "password") {
          // Password would typically be handled differently (not stored in plain text)
          // This is just a demonstration
          alert("Password updated successfully!");
          // Reset password fields
          setFormState((prevState) => ({
            ...prevState,
            currentPassword: "",
            newPassword: "",
            confirmNewPassword: "",
          }));
          handleEditMode("password", false);
        }
      } catch (error) {
        console.error("Error saving to localStorage:", error);
        alert("Failed to save changes. Please try again.");
      }
    }
  };

  const getFilteredAndSortedBookings = () => {
    const filtered = MOCK_BOOKINGS.filter(
      (booking) =>
        booking.name.toLowerCase().includes(ui.searchQuery.toLowerCase()) ||
        booking.location.toLowerCase().includes(ui.searchQuery.toLowerCase()) ||
        booking.reference.toLowerCase().includes(ui.searchQuery.toLowerCase())
    );

    // If default sort is selected, return the original order
    if (ui.sortBy === "default") {
      return filtered;
    }

    return [...filtered].sort((a, b) => {
      if (ui.sortBy === "date") {
        const dateA = new Date(a.date.split(" - ")[0]);
        const dateB = new Date(b.date.split(" - ")[0]);
        return dateA.getTime() - dateB.getTime();
      } else {
        // Sort by price
        return a.price - b.price;
      }
    });
  };

  const handleEmailSave = (newEmail: string) => {
    // Update the email in formState
    setFormState((prevState) => ({
      ...prevState,
      email: newEmail,
    }));

    // Update localStorage with new values
    if (typeof window !== "undefined") {
      try {
        const existingData = localStorage.getItem("userData");
        const userData = existingData ? JSON.parse(existingData) : {};
        userData.email = newEmail;
        localStorage.setItem("userData", JSON.stringify(userData));
        alert(`Email saved: ${newEmail}`);
      } catch (error) {
        console.error("Error saving to localStorage:", error);
        alert("Failed to save changes. Please try again.");
      }
    }
  };

  const handlePhoneNumberSave = (newPhone: string) => {
    // Update the phone in formState
    setFormState((prevState) => ({
      ...prevState,
      phone: newPhone,
    }));

    // Update localStorage with new values
    if (typeof window !== "undefined") {
      try {
        const existingData = localStorage.getItem("userData");
        const userData = existingData ? JSON.parse(existingData) : {};
        userData.phone = newPhone;
        localStorage.setItem("userData", JSON.stringify(userData));
        showToast(`Phone saved: ${newPhone}`, "success", "top-right");
      } catch (error) {
        console.error("Error saving to localStorage:", error);
        showToast("Failed to save changes. Please try again.", "error", "top-right");
      }
    }
  };

  const sortedBookings = getFilteredAndSortedBookings();

  // Component rendering helpers
  const renderBookingCard = (booking: BookingItem) => (
    <div className="booking-card hotel-card" key={booking.id}>
      <div className="booking-icon">
        <i className="fa-solid fa-hotel"></i>
      </div>
      <div className="booking-details hotel-details">
        <div className="hotel-header">
          <h3>{booking.name}</h3>
          <div className={`booking-status ${booking.status}`}>
            {booking.status === "confirmed" && (
              <i className="fa-solid fa-circle-check"></i>
            )}
            {booking.status === "pending" && (
              <i className="fa-solid fa-clock"></i>
            )}
            {booking.status === "cancelled" && (
              <i className="fa-solid fa-ban"></i>
            )}
            {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
          </div>
        </div>
        <div className="hotel-info">
          <div className="hotel-location">
            <p className="booking-location">
              <i className="fa-solid fa-location-dot"></i> {booking.location}
            </p>
            <p className="booking-date">
              <i className="fa-regular fa-calendar"></i> {booking.date}
            </p>
          </div>
          {booking.amenities && (
            <div className="hotel-amenities">
              <p className="amenities-title">
                <i className="fa-solid fa-check-circle"></i> Amenities
              </p>
              <div className="amenities-list">
                {booking.amenities.map((amenity, index) => (
                  <span key={index} className="amenity-tag">
                    {amenity}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
        <div className="hotel-footer">
          <p className="booking-reference">
            <i className="fa-solid fa-hashtag"></i> Ref: {booking.reference}
          </p>
          <div className="hotel-actions">
            <div className="booking-price">${booking.price.toFixed(2)}</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderToggleButton = (
    label: string,
    isActive: boolean,
    toggleKey: keyof typeof settings
  ) => (
    <div className="toggle-button">
      <span className="label" style={{ opacity: isActive ? 1 : 0.5 }}>
        YES
      </span>
      <button
        type="button"
        className={`toggle ${isActive ? "active" : ""}`}
        onClick={() => handleToggle(toggleKey)}
        aria-checked={isActive}
        role="switch"
        aria-label="Toggle YES"
      >
        <div className="indicator"></div>
      </button>
    </div>
  );

  // Rendering different sections based on active tab
  const renderBookingsContent = () => {
    if (ui.activeStatus === "Upcoming") {
      return (
        <>
          <div className="bookings-header">
            <h1>Upcoming Bookings</h1>
            <div className="bookings-actions">
              <div className="search-box">
                <i className="fa-solid fa-magnifying-glass"></i>
                <input
                  type="text"
                  onChange={(e) =>
                    setUi((prev) => ({ ...prev, searchQuery: e.target.value }))
                  }
                  value={ui.searchQuery}
                  placeholder="Search hotels..."
                />
              </div>
            </div>
          </div>

          <div className="bookings-filters">
            <div className="sort-options">
              <span>Sort by:</span>
              <select
                value={ui.sortBy}
                onChange={(e) =>
                  setUi((prev) => ({
                    ...prev,
                    sortBy: e.target.value as SortOption,
                  }))
                }
              >
                <option value="default">Default</option>
                <option value="date">Date</option>
                <option value="price">Price</option>
              </select>
            </div>
          </div>

          <div className="bookings-list">
            {sortedBookings.length > 0 ? (
              sortedBookings.map(renderBookingCard)
            ) : (
              <div className="no-bookings">
                <i className="fa-solid fa-calendar-xmark"></i>
                <p>No hotel bookings found matching your criteria</p>
                <button
                  onClick={() =>
                    setUi((prev) => ({ ...prev, searchQuery: "" }))
                  }
                >
                  View All Hotels
                </button>
              </div>
            )}
          </div>

          <div className="bookings-summary">
            <div className="summary-box">
              <h4>Total Hotels</h4>
              <p>{sortedBookings.length}</p>
            </div>
            <div className="summary-box">
              <h4>Upcoming</h4>
              <p>
                {sortedBookings.filter((b) => b.status !== "cancelled").length}
              </p>
            </div>
            <div className="summary-box">
              <h4>Total Value</h4>
              <p>
                $
                {sortedBookings.reduce((sum, b) => sum + b.price, 0).toFixed(2)}
              </p>
            </div>
          </div>
        </>
      );
    } else {
      // Reusable empty state for completed and cancelled bookings
      const status =
        ui.activeStatus === "Completed" ? "completed" : "cancelled";
      return (
        <>
          <div className="bookings-header">
            <h1>{ui.activeStatus} Bookings</h1>
            <div className="bookings-actions">
              <div className="search-box">
                <i className="fa-solid fa-magnifying-glass"></i>
                <input type="text" placeholder="Search hotels..." />
              </div>
            </div>
          </div>

          <div className="bookings-filters">
            <div className="sort-options">
              <span>Sort by:</span>
              <select
                value={ui.sortBy}
                onChange={(e) =>
                  setUi((prev) => ({
                    ...prev,
                    sortBy: e.target.value as SortOption,
                  }))
                }
              >
                <option value="date">Date</option>
                <option value="price">Price</option>
              </select>
            </div>
          </div>

          <div className="no-bookings-found">
            <h3>User., you have no recent {status} bookings</h3>
            <Link href="/pages/hotel">Start planning your next trip!</Link>
          </div>
        </>
      );
    }
  };

  const renderProfileContent = () => (
    <>
      <div className="profile">
        <h3 className="profile__header">User details</h3>

        <form className="profile__userForm">
          {/* Name field */}
          <div
            className={`profile__form-field ${
              editMode.name ? "edit-mode" : ""
            }`}
          >
            {/* View Mode */}
            <div
              className={`form-field__details-group ${
                editMode.name ? "hidden" : ""
              }`}
            >
              <div className="form-field__userName">
                <div className="form-field__icon">
                  <span>U</span>
                </div>
                <div className="form-field__label-fullname">
                  <p className="label">Name</p>
                  <p className="fullname">
                    {formState.firstName} {formState.lastName}
                  </p>
                </div>
              </div>

              <span
                onClick={() => handleEditMode("name", true)}
                className="form-field__action"
              >
                Edit
              </span>
            </div>

            {/* Edit Mode */}
            <div
              className={`form-field__input-field-group ${
                editMode.name ? "" : "hidden"
              }`}
            >
              <div className="form-field__input-field">
                <h3>First name</h3>
                <input
                  maxLength={64}
                  placeholder="First Name"
                  type="text"
                  name="firstName"
                  value={formState.firstName}
                  onChange={handleInputChange}
                />
                {formErrors.firstName && (
                  <p className="profile-form-error">{formErrors.firstName}</p>
                )}
              </div>

              <div className="form-field__input-field">
                <h3>Last name</h3>
                <input
                  maxLength={64}
                  placeholder="Last Name"
                  type="text"
                  name="lastName"
                  value={formState.lastName}
                  onChange={handleInputChange}
                />
                {formErrors.lastName && (
                  <p className="profile-form-error">{formErrors.lastName}</p>
                )}
              </div>

              <div className="form-field__input-buttons">
                <button
                  onClick={() => handleEditMode("name", false)}
                  type="button"
                  className="btn cancelBtn"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn saveBtn"
                  onClick={() => handleSave("name")}
                >
                  Save
                </button>
              </div>
            </div>
          </div>

          {/* Email field */}
          {/* <div className="profile__form-field2">
            <div className="form-field__details-group">
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="label">Email</p>
                  {editMode.email || !formState.email ? (
                    <div className="verify-textbox-btn-group">
                      <input
                        className="verify-textbox-btn-group__textbox"
                        type="text"
                        name="email"
                        value={formState.email}
                        onChange={handleInputChange}
                      />
                      <button 
                        className="verify-textbox-btn-group__verifyBtn" 
                        type="button"
                        onClick={() => handleSave("email")}
                      >
                        Verify
                      </button>
                    </div>
                  ) : (
                    <p className="fullname">{formState.email}</p>
                  )}
                  {formErrors.email && (
                    <p className="profile-form-error">{formErrors.email}</p>
                  )}
                </div>
                
                {!editMode.email && formState.email && (
                  <span onClick={() => handleEditMode("email", true)} className="form-field__action">Edit</span>
                )}
                
                {editMode.email && formState.email && (
                  <span onClick={() => handleEditMode("email", false)} className="form-field__action">Cancel</span>
                )}
              </div>
            </div>
          </div> */}

          <EmailSection
            initialEmail={formState.email}
            onSaveEmail={handleEmailSave}
            parentErrorMessage={formErrors.email}
          />

          {/* Phone field */}
          {/* <div
            className={`profile__form-field bg-color-white ${
              editMode.phone ? "edit-mode" : ""
            }`}
          >
        
            <div
              className={`form-field__details-group ${
                editMode.phone ? "hidden" : ""
              }`}
            >
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="label">Phone Number</p>
                  <p className="fullname">
                    {formState.phone || "Not provided"}
                  </p>
                </div>
              </div>

              <span
                onClick={() => handleEditMode("phone", true)}
                className="form-field__action"
              >
                {formState.phone ? "Edit" : "Add"}
              </span>
            </div>

   
            <div
              className={`form-field__input-field-group ${
                editMode.phone ? "" : "hidden"
              }`}
            >
              <div className="form-field__input-field">
                <h3>Phone number</h3>
                <input
                  maxLength={10}
                  placeholder="Phone number"
                  type="text"
                  name="phone"
                  value={formState.phone}
                  onChange={handleInputChange}
                />
                {formErrors.phone && (
                  <p className="profile-form-error">{formErrors.phone}</p>
                )}
              </div>

              <div className="form-field__input-buttons">
                <button
                  onClick={() => handleEditMode("phone", false)}
                  type="button"
                  className="btn cancelBtn"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn saveBtn"
                  onClick={() => handleSave("phone")}
                >
                  Save
                </button>
              </div>
            </div>
          </div> */}

          <PhoneNumberSection
            initialPhoneNumber={formState.phone}
            onSavePhoneNumber={handlePhoneNumberSave}
            parentErrorMessage={formErrors.phone}
          />

          {/* Password field */}
          <div
            className={`profile__form-field bg-color-white ${
              editMode.password ? "edit-mode" : ""
            }`}
          >
            {/* View Mode */}
            <div
              className={`form-field__details-group ${
                editMode.password ? "hidden" : ""
              }`}
            >
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="label">Password</p>
                  <p className="password-dots">
                    {[...Array(8)].map((_, index) => (
                      <span key={index} className="dots"></span>
                    ))}
                  </p>
                </div>
              </div>

              <span
                onClick={() => handleEditMode("password", true)}
                className="form-field__action"
              >
                Edit
              </span>
            </div>

            {/* Edit Mode */}
            <div
              className={`form-field__input-field-group ${
                editMode.password ? "" : "hidden"
              }`}
            >
              <div className="form-field__input-field">
                <div className="mb-2">
                  <h3>Current Password</h3>
                  <input
                    maxLength={20}
                    placeholder="Current Password"
                    type="password"
                    name="currentPassword"
                    value={formState.currentPassword}
                    onChange={handleInputChange}
                  />
                  {formErrors.currentPassword && (
                    <p className="profile-form-error">
                      {formErrors.currentPassword}
                    </p>
                  )}
                </div>

                <div className="mb-2">
                  <h3>New Password</h3>
                  <input
                    maxLength={20}
                    placeholder="New Password"
                    type="password"
                    name="newPassword"
                    value={formState.newPassword}
                    onChange={handleInputChange}
                  />
                  {formErrors.newPassword && (
                    <p className="profile-form-error">
                      {formErrors.newPassword}
                    </p>
                  )}
                </div>

                <div>
                  <h3>Confirm new password</h3>
                  <input
                    maxLength={20}
                    placeholder="Confirm Password"
                    type="password"
                    name="confirmNewPassword"
                    value={formState.confirmNewPassword}
                    onChange={handleInputChange}
                  />
                  {formErrors.confirmNewPassword && (
                    <p className="profile-form-error">
                      {formErrors.confirmNewPassword}
                    </p>
                  )}
                </div>
              </div>

              <div className="form-field__input-buttons">
                <button
                  onClick={() => handleEditMode("password", false)}
                  type="button"
                  className="btn cancelBtn"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn saveBtn"
                  onClick={() => handleSave("password")}
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Payment Methods */}
      <div className="profile">
        <h3 className="profile__header">Payment methods</h3>
        <form className="profile__userForm">
          <div className="profile__form-field2">
            <div className="form-field__details-group">
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="label">Save my credit card information</p>
                </div>
              </div>
              {renderToggleButton("YES", settings.creditInfo, "creditInfo")}
            </div>
          </div>
        </form>
      </div>

      {/* Email subscriptions */}
      <div className="profile">
        <h3 className="profile__header">Email subscriptions</h3>
        <form className="profile__userForm">
          {/* Newsletter options */}
          <div className="profile__form-field2">
            <div className="form-field__details-group">
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="label">Newsletter</p>
                  <div className="custom-checkbox-container">
                    {newsLetterOptions.map((newsletter, index) => (
                      <label className="checkbox-container" key={index}>
                        <input
                          type="checkbox"
                          checked={newsletter.isSelected}
                          onChange={() => handleCheckboxChange(index)}
                        />
                        <span className="checkmark">
                          {newsletter.isSelected && (
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="white"
                              width="16"
                              height="16"
                            >
                              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
                            </svg>
                          )}
                        </span>
                        <span className="checkbox-label">
                          {newsletter.type}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Toggle settings */}
          <div className="profile__form-field2">
            <div className="form-field__details-group">
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="subscription-label">
                    I would like to receive booking assist reminders
                  </p>
                </div>
              </div>
              {renderToggleButton(
                "YES",
                settings.bookingAssistReminder,
                "bookingAssistReminder"
              )}
            </div>
          </div>

          <div className="profile__form-field2">
            <div className="form-field__details-group">
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="subscription-label">
                    I would like to receive emails about Kind Ali promotions
                  </p>
                </div>
              </div>
              {renderToggleButton(
                "YES",
                settings.promotionsReminder,
                "promotionsReminder"
              )}
            </div>
          </div>

          <div className="profile__form-field2">
            <div className="form-field__details-group">
              <div className="form-field__userName">
                <div className="form-field__label-fullname">
                  <p className="subscription-label">
                    I would like to know about information and offers related to
                    my upcoming trip
                  </p>
                </div>
              </div>
              {renderToggleButton(
                "YES",
                settings.upcomingTripInfo,
                "upcomingTripInfo"
              )}
            </div>
          </div>
        </form>
      </div>

      {/* Delete account option */}
      <div className="profile">
        <div className="profile__form-field2">
          <Link href="" className="delete-account">
            Delete My Account.
          </Link>
        </div>
      </div>
    </>
  );

  return (
    <div
      className="py-4 profile-page-main-container"
      style={{ backgroundColor: "#EFF4FC" }}
    >
      <div className="profile-page container">
        <div className="profile-page__leftSection">
          <Sidebar
            activeTab={ui.activeTab}
            onTabChange={(tab) => {
              setUi((prev) => ({ ...prev, activeTab: tab }));

              // Update URL when tab changes from sidebar
              // Find the correct param value for the selected tab
              const paramValue = Object.entries(TAB_MAPPING).find(
                ([_, value]) => value === tab
              )?.[0];

              if (paramValue) {
                router.push(`/profile?tab=${paramValue}`);
              }
            }}
          />
        </div>

        <div className="profile-page__rightSection">
          {ui.activeTab === "My bookings" && (
            <NavigationBar
              activeStatus={ui.activeStatus}
              onTabChange={(status) =>
                setUi((prev) => ({ ...prev, activeStatus: status }))
              }
            />
          )}
          <div className="content">
            {ui.activeTab === "My bookings" && (
              <>
                <div className="mt-3 pt-3">
                  <div className="list-container">
                    <div className="bookings-container">
                      {renderBookingsContent()}
                    </div>
                  </div>
                </div>
                <div className="mt-3">
                  <TripPlannerGrid />
                </div>
              </>
            )}

            {ui.activeTab === "Reviews" && (
              <div className="list-container mt-3">
                <Reviews />{" "}
              </div>
            )}

            {ui.activeTab === "Profile" && (
              <div className="mt-3 pt-3">
                <div className="list-container no-bg flex flex-col gap-y-9">
                  {renderProfileContent()}
                </div>
              </div>
            )}

            {ui.activeTab === "Wishlist" && (
              <div className="list-container mt-3">
                <Wishlist />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Page;
