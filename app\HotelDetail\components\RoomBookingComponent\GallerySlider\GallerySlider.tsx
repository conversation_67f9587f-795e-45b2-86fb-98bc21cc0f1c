import { useState, useEffect } from 'react';
import Image from 'next/image';
import './GallerySlider.scss';
import { CarouselImage } from '@/app/HotelDetail/hotel-detail-result.model';

interface GallerySliderProps {
  images: CarouselImage[];
  initialIndex?: number;
}

const GallerySlider: React.FC<GallerySliderProps> = ({
  images = [],
  initialIndex = 0
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Set current index safely when component loads or images change
  useEffect(() => {
    // Validate initialIndex within bounds
    if (images.length > 0) {
      const safeIndex = Math.max(0, Math.min(initialIndex, images.length - 1));
      setCurrentIndex(safeIndex);
    } else {
      setCurrentIndex(0);
    }
  }, [images, initialIndex]);

  // Check if we have valid images to display
  const hasImages = images && images.length > 0;

  // Handle navigation to previous image
  const goToPrevious = () => {
    if (isTransitioning || !hasImages) return;
    
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => {
      if (prevIndex === 0) {
        return images.length - 1;
      }
      return prevIndex - 1;
    });
    
    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  };

  // Handle navigation to next image
  const goToNext = () => {
    if (isTransitioning || !hasImages) return;
    
    setIsTransitioning(true);
    setCurrentIndex((prevIndex) => {
      if (prevIndex === images.length - 1) {
        return 0;
      }
      return prevIndex + 1;
    });
    
    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  };

  // Show current position indicator (e.g., "5/20")
  const positionIndicator = hasImages ? `${currentIndex + 1}/${images.length}` : '0/0';

  // If no images are available, show a placeholder
  if (!hasImages) {
    return (
      <div className="gallery-slider-container">
        <div className="gallery-slider-content">
          <div className="gallery-slider-image-container">
            <div className="gallery-slider-placeholder">
              No images available
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Get the current image safely
  const currentImage = images[currentIndex];

  return (
    <div className="gallery-slider-container">
      <div className="gallery-slider-content">
        <div className="gallery-slider-image-container">
          <Image
            src={currentImage.url}
            alt={currentImage.caption || 'Hotel image'}
            fill
            className={`gallery-slider-image ${isTransitioning ? 'transitioning' : ''}`}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
            priority
          />
        </div>
        
        {images.length > 1 && (
          <>
            <button 
              className="gallery-slider-button gallery-slider-button-prev"
              onClick={goToPrevious}
              aria-label="Previous image"
            >
              <span className="gallery-slider-arrow">&#10094;</span>
            </button>
            
            <button 
              className="gallery-slider-button gallery-slider-button-next"
              onClick={goToNext}
              aria-label="Next image"
            >
              <span className="gallery-slider-arrow">&#10095;</span>
            </button>
            
            <div className="gallery-slider-indicator">
              {positionIndicator}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default GallerySlider;