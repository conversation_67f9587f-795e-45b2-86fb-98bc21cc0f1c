import { RoomsAndRatesRequest, RoomsAndRatesResponse } from "@/models/hotel/rooms-and-rates.model";
import apiService from "../api-service";

/**
 * Service for fetching rooms and rates data for a specific hotel
 */
export const getRoomsAndRates = async (
  searchKey: string,
  hotelId: string
): Promise<RoomsAndRatesResponse> => {
  console.log("🏨 Calling rooms and rates API:");
  console.log("   - Search Key:", searchKey);
  console.log("   - Hotel ID:", hotelId);

  const payload: RoomsAndRatesRequest = {
    search_key: searchKey,
    hotel_id: hotelId,
  };

  try {
    const response = await apiService.post<RoomsAndRatesResponse>(
      "search/rooms-and-rates",
      payload
    );

    console.log("✅ Rooms and rates API response received:");
    console.log("   - Number of recommendations:", response.length);
    
    // Log summary of rooms and rates
    response.forEach((recommendation, index) => {
      console.log(`   - Recommendation ${index + 1}:`, {
        id: recommendation.recommendationId,
        groupedRatesCount: recommendation.groupedRates.length,
        rooms: recommendation.groupedRates.map(gr => ({
          roomName: gr.room.name,
          totalRate: gr.rate.totalRate,
          currency: gr.rate.currency,
          refundable: gr.rate.refundable,
        }))
      });
    });

    return response;
  } catch (error) {
    console.error("❌ Error calling rooms and rates API:", error);
    throw error;
  }
};

/**
 * Utility function to get the cheapest rate from rooms and rates response
 */
export const getCheapestRate = (roomsAndRates: RoomsAndRatesResponse): number | null => {
  if (!roomsAndRates || roomsAndRates.length === 0) {
    return null;
  }

  let cheapestRate = Infinity;
  
  roomsAndRates.forEach(recommendation => {
    recommendation.groupedRates.forEach(groupedRate => {
      if (groupedRate.rate.totalRate < cheapestRate) {
        cheapestRate = groupedRate.rate.totalRate;
      }
    });
  });

  return cheapestRate === Infinity ? null : cheapestRate;
};

/**
 * Utility function to get all unique room types from rooms and rates response
 */
export const getRoomTypes = (roomsAndRates: RoomsAndRatesResponse): string[] => {
  if (!roomsAndRates || roomsAndRates.length === 0) {
    return [];
  }

  const roomTypes = new Set<string>();
  
  roomsAndRates.forEach(recommendation => {
    recommendation.groupedRates.forEach(groupedRate => {
      roomTypes.add(groupedRate.room.name);
    });
  });

  return Array.from(roomTypes);
};

/**
 * Utility function to filter rooms by refundability
 */
export const getRefundableRooms = (roomsAndRates: RoomsAndRatesResponse): RoomsAndRatesResponse => {
  if (!roomsAndRates || roomsAndRates.length === 0) {
    return [];
  }

  return roomsAndRates.map(recommendation => ({
    ...recommendation,
    groupedRates: recommendation.groupedRates.filter(groupedRate => 
      groupedRate.rate.refundable
    )
  })).filter(recommendation => recommendation.groupedRates.length > 0);
};

/**
 * Utility function to sort rooms by price (ascending)
 */
export const sortRoomsByPrice = (roomsAndRates: RoomsAndRatesResponse): RoomsAndRatesResponse => {
  if (!roomsAndRates || roomsAndRates.length === 0) {
    return [];
  }

  return roomsAndRates.map(recommendation => ({
    ...recommendation,
    groupedRates: [...recommendation.groupedRates].sort((a, b) => 
      a.rate.totalRate - b.rate.totalRate
    )
  }));
};
