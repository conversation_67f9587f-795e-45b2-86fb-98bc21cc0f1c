@use "/styles/variable" as *;
@use "/styles/zIndex" as *;
@use "sass:color";

.hotel-detail-container {
  width: 100%;
  height: auto;
  padding: 10px 0;
  position: relative;

  // .search-bar-container{
  //     max-width: 1280px;
  //     padding: 20px;
  //     margin: 0 auto;
  // }

  .overview-section {
    // margin: 0 43px;
    // padding:20px 20px 15px 20px;
    //padding: 20px 0 15px 0;
    display: flex;
    flex-direction: column;

    // .hotel-info-header {
    //   width: 100%;
    //   display: flex;
    //   flex-direction: column;
    //   padding: 0 0 30px 0;

    //   .info-header {
    //     width: 100%;
    //     display: flex;
    //     flex-direction: row;
    //     justify-content: space-between;

    //     @media (max-width: $breakpoint-md) {
    //       flex-direction: column;
    //     }

    //     .heading {
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;
    //       padding: 0 0 15px;

    //       h2 {
    //         font-size: 32px;
    //         font-weight: 700;

    //         @media (max-width: $breakpoint-md) {
    //           font-size: 28px;
    //         }

    //         @media (max-width: $breakpoint-sm) {
    //           font-size: 24px;
    //         }

    //         @media (max-width: $breakpoint-xs) {
    //           font-size: 20px;
    //         }
    //       }

    //       .rating {
    //         font-size: 10px;
    //         color: $rating_color;
    //         margin-left: 8px;

    //         .fa-star {
    //           margin-right: 2px;
    //         }
    //       }
    //     }
    //     .buttons {
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;

    //       @media (max-width: $breakpoint-md) {
    //         padding-bottom: 15px;
    //       }
    //     }
    //   }

    //   .review-location {
    //     display: flex;
    //     flex-direction: row;
    //     justify-content: start;
    //     gap: 60px;

    //     @media (max-width: $isMobile) {
    //       flex-direction: column;
    //       gap: 15px;
    //     }

    //     .review,
    //     .location {
    //       display: flex;
    //       flex-direction: row;
    //     }

    //     .review {
    //       .rating {
    //         padding: 10px;
    //         margin: 0 10px 0 0;
    //         background-color: #17181c;
    //         font-size: 20px;
    //         color: #fafafa;
    //         font-weight: 700;
    //         border-radius: 10px;

    //         @media (max-width: $breakpoint-md) {
    //           font-size: 18px;
    //         }

    //         @media (max-width: $breakpoint-xs) {
    //           font-size: 16px;
    //         }
    //       }

    //       .rating-detail {
    //         .detail1 {
    //           font-size: 16px;
    //           font-weight: 600;
    //           color: #17181c;

    //           @media (max-width: $breakpoint-sm) {
    //             font-size: 15px;
    //           }

    //           @media (max-width: $breakpoint-xs) {
    //             font-size: 14px;
    //           }
    //         }

    //         .detail2 {
    //           font-size: 14px;
    //           font-weight: 600;
    //           color: #5e616e;
    //           @media (max-width: $breakpoint-sm) {
    //             font-size: 13px;
    //           }
    //         }
    //       }
    //     }

    //     .location {
    //       display: flex;
    //       flex-direction: row;
    //       align-items: center;
    //       gap: 10px;

    //       .icon {
    //         background-color: rgba(8, 119, 103, 0.05);
    //         color: $primary-color;
    //         font-size: 26px;
    //         padding: 5px 12px;
    //         border-radius: 5px;

    //         @media (max-width: $breakpoint-sm) {
    //             font-size: 24px;
    //           }

    //           @media (max-width: $breakpoint-xs) {
    //             font-size: 22px;
    //           }
    //       }
    //       .details {
    //         .detail1 {
    //           font-size: 16px;
    //           font-weight: 400;

    //           @media (max-width: $breakpoint-sm) {
    //             font-size: 15px;
    //           }

    //           @media (max-width: $breakpoint-xs) {
    //             font-size: 14px;
    //           }
    //         }

    //         .detail2 {
    //           color: $primary-color;
    //           font-size: 14px;
    //           font-weight: 500;
    //           cursor: pointer;

    //           @media (max-width: $breakpoint-sm) {
    //             font-size: 13px;
    //           }
    //         }

    //       }
    //     }
    //   }
    // }
  }
}

.detail-page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: z-index(overlay);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }
}


// .booking-section-bottom-0 {
//     background-color: #ffffff;
//     width: 100%;
//     padding: 10px 20px;
//     display: flex
// ;
//     justify-content: space-between;
//     position: fixed;
//     bottom: -3px;
//     left: 0;
//     right: 0;
//     border-top: 1px solid rgba(0, 0, 0, 0.1);
//     z-index: 400;

//     .totalAmtLabelValue .value {
//     font-size: 20px;
//     font-weight: 700;
// }

//     .bookingBtn {
//     width: 180px;
//     min-height: 50px;
//     padding: 0 20px;
//     font-size: 18px;
//     font-weight: 600;
//     color: #ffffff;
//     background-color: #003b95;
//     border-radius: 10px;
//     transition: background-color 0.2s ease;
// }
// }