// // 'use client';

// // import React, { useState, useEffect } from 'react';
// // import MapWrapper from '@/components/map/MapWrapper';
// // import FullScreenMap from '@/components/map/FullScreenMap';
// // import { useTranslation } from '@/app/hooks/useTranslation';
// // import { useLanguage } from '@/app/contexts/languageContext';
// // import './HotelSearchMap.scss';
// // import { Hotel } from '../../hotel-search-result.model';
// // import { HotelFilterData } from '@/app/components/utilities/helpers/hotel/filterHotels';

// // interface HotelSearchMapProps {
// //   hotels?: Array<{
// //     id: string;
// //     name: string;
// //     location: {
// //       latitude: number;
// //       longitude: number;
// //       address?: string;
// //     };
// //     rating?: number;
// //     price?: string;
// //     image?: string; // Add image property
// //   }>;
// //   destination?: {
// //     name: string;
// //     latitude: number;
// //     longitude: number;
// //   };
// //   onHotelSelect?: (hotelId: string) => void;
// //   // New props to accept data from parent
// //   actualHotels?: Hotel[];
// //   filterData?: HotelFilterData;
// // }

// // // Dummy hotel data for testing
// // const DUMMY_HOTELS = [
// //   {
// //     id: "hotel1",
// //     name: "Blue Lagoon Resort",
// //     location: {
// //       latitude: 15.7,
// //       longitude: 73.91,
// //       address: "Arambol, Goa"
// //     },
// //     rating: 4.7,
// //     price: "$120"
// //   },
// //   {
// //     id: "hotel2",
// //     name: "Sunset Beach Hotel",
// //     location: {
// //       latitude: 15.675,
// //       longitude: 73.86,
// //       address: "Vagator Beach, Goa"
// //     },
// //     rating: 4.5,
// //     price: "$150"
// //   },
// //   {
// //     id: "hotel3",
// //     name: "Palm Grove Resort",
// //     location: {
// //       latitude: 15.545,
// //       longitude: 73.785,
// //       address: "Calangute, Goa"
// //     },
// //     rating: 4.2,
// //     price: "$95"
// //   },
// //   {
// //     id: "hotel4",
// //     name: "Seaside Luxury Hotel",
// //     location: {
// //       latitude: 15.27,
// //       longitude: 73.95,
// //       address: "Palolem, Goa"
// //     },
// //     rating: 4.8,
// //     price: "$180"
// //   },
// //   {
// //     id: "hotel5",
// //     name: "Tropical Paradise Inn",
// //     location: {
// //       latitude: 15.735,
// //       longitude: 73.88,
// //       address: "Morjim, Goa"
// //     },
// //     rating: 4.3,
// //     price: "$110"
// //   },
// //   {
// //     id: "hotel6",
// //     name: "Ocean View Resort",
// //     location: {
// //       latitude: 15.65,
// //       longitude: 73.82,
// //       address: "Anjuna, Goa"
// //     },
// //     rating: 4.6,
// //     price: "$140"
// //   },
// //   {
// //     id: "hotel7",
// //     name: "Golden Sands Hotel",
// //     location: {
// //       latitude: 15.5,
// //       longitude: 73.77,
// //       address: "Baga, Goa"
// //     },
// //     rating: 4.4,
// //     price: "$130"
// //   },
// //   {
// //     id: "hotel8",
// //     name: "Royal Palm Resort",
// //     location: {
// //       latitude: 15.35,
// //       longitude: 73.9,
// //       address: "Agonda, Goa"
// //     },
// //     rating: 4.9,
// //     price: "$200"
// //   },
// //   {
// //     id: "hotel9",
// //     name: "Coconut Grove Inn",
// //     location: {
// //       latitude: 15.6,
// //       longitude: 73.85,
// //       address: "Candolim, Goa"
// //     },
// //     rating: 4.1,
// //     price: "$90"
// //   },
// //   {
// //     id: "hotel10",
// //     name: "Beachfront Luxury Resort",
// //     location: {
// //       latitude: 15.42,
// //       longitude: 73.8,
// //       address: "Colva, Goa"
// //     },
// //     rating: 4.7,
// //     price: "$170"
// //   }
// // ];

// // // Dummy destination
// // const DUMMY_DESTINATION = {
// //   name: "Goa, India",
// //   latitude: 15.5,
// //   longitude: 73.85
// // };

// // const HotelSearchMap: React.FC<HotelSearchMapProps> = ({
// //   hotels = [],
// //   destination,
// //   onHotelSelect,
// //   actualHotels: parentActualHotels,
// //   filterData: parentFilterData
// // }) => {
// //   const { t } = useTranslation();
// //   const { isRTL } = useLanguage();
// //   const [mapHotels, setMapHotels] = useState(hotels);
// //   const [mapDestination, setMapDestination] = useState(destination);
// //   const [actualHotels, setActualHotels] = useState<Hotel[]>(parentActualHotels || []);
// //   const [mapFilterData, setMapFilterData] = useState<HotelFilterData | undefined>(parentFilterData);

// //   // Use dummy data if no hotels are provided
// //   useEffect(() => {
// //     if (hotels.length === 0) {
// //       setMapHotels(DUMMY_HOTELS);
// //       setMapDestination(DUMMY_DESTINATION);
// //     } else {
// //       setMapHotels(hotels);
// //       setMapDestination(destination);
// //     }
// //   }, [hotels, destination]);

// //   // Use actual hotels from parent if provided, otherwise generate from mapHotels
// //   useEffect(() => {
// //     if (parentActualHotels && parentActualHotels.length > 0) {
// //       setActualHotels(parentActualHotels);
// //     } else if (mapHotels.length > 0) {
// //       try {
// //         // Generate hotel data from mapHotels if no actual hotels are provided
// //         const generatedHotels: Hotel[] = mapHotels.map((hotel, index) => ({
// //           hotelId: parseInt(hotel.id) || index + 1,
// //           locationId: index + 1,
// //           name: hotel.name,
// //           locality: hotel.location.address?.split(',')[0] || '',
// //           city: hotel.location.address?.split(',')[1]?.trim() || '',
// //           userRating: '8.5',
// //           userRatingCategory: 'Excellent',
// //           starRating: hotel.rating || 4,
// //           userRatingCount: 100 + index,
// //           imageInfoList: hotel.image ? [{
// //             url: hotel.image,
// //             pictureId: '1',
// //             caption: hotel.name,
// //             imageCategory: 'hotel',
// //             rank: 1
// //           }] : [],
// //           roomDetails: [{
// //             type: 'Deluxe Room',
// //             bedroom: 1,
// //             livingRoom: 1,
// //             bathroom: 1,
// //             size: '30m²',
// //             bed: '1 King Bed'
// //           }],
// //           comfortRating: 4.5,
// //           bookingDetails: [{
// //             nights: 2,
// //             adults: 2
// //           }],
// //           taxesAndCharges: 500,
// //           accommodationType: 'Hotel',
// //           topOfferings: ['Free WiFi', 'Breakfast Included'],
// //           roomsCountLeft: 5,
// //           distanceFromSearchedEntity: '2.5 km',
// //           fomoTags: [],
// //           amenities: [{
// //             name: 'Free WiFi',
// //             amenityUrl: ''
// //           }],
// //           geoLocationInfo: {
// //             lat: hotel.location.latitude,
// //             lon: hotel.location.longitude
// //           },
// //           fareDetail: {
// //             displayedBaseFare: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '1000'),
// //             totalPrice: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '1000')
// //           },
// //           isVisible: true
// //         }));

// //         setActualHotels(generatedHotels);
// //       } catch (error) {
// //         console.error('Error generating hotel data:', error);
// //         setActualHotels([]);
// //       }
// //     }
// //   }, [parentActualHotels, mapHotels]);

// //   // Use filter data from parent if provided
// //   useEffect(() => {
// //     if (parentFilterData && typeof parentFilterData === 'object') {
// //       // Check if parentFilterData has the expected properties
// //       if (parentFilterData.priceRange || parentFilterData.starRatings || parentFilterData.amenities) {
// //         setMapFilterData(parentFilterData);
// //       }
// //     }
// //   }, [parentFilterData]);



// //   // Define center location
// //   const center = mapDestination ? {
// //     latitude: mapDestination.latitude,
// //     longitude: mapDestination.longitude,
// //     address: mapDestination.name
// //   } : undefined;

// //   // Only use the filter data from parent - no default filter data

// //   // State to control full-screen map visibility
// //   const [isFullScreenMapOpen, setIsFullScreenMapOpen] = useState(false);

// //   // Open fullscreen map directly
// //   const openFullScreenMap = () => {
// //     setIsFullScreenMapOpen(true);
// //   };

// //   // Define map center for the fullscreen map
// //   const mapCenter: [number, number] = center
// //     ? [center.latitude, center.longitude]
// //     : [15.5, 73.85]; // Default center (Goa)

// //   // Create markers for hotels
// //   const hotelMarkers = mapHotels.map(hotel => ({
// //     id: hotel.id,
// //     position: [hotel.location.latitude, hotel.location.longitude] as [number, number],
// //     iconType: 'hotel' as const,
// //     details: {
// //       id: hotel.id,
// //       name: hotel.name,
// //       rating: hotel.rating,
// //       price: hotel.price
// //     }
// //   }));

// //   return (
// //     <div className="hotel-search-map idle">
// //       {/* Idle state - just show map background with no markers and a centered button */}
// //       <div className="map-background">
// //         <MapWrapper
// //           center={center ? [center.latitude, center.longitude] : undefined}
// //           zoom={12}
// //           markers={[]}
// //           style={{ height: '100%', width: '100%', borderRadius: '8px' }}
// //           interactive={false} // Disable zoom controls and interaction
// //         />
// //         <div className="show-map-button-container">
// //           <button
// //             className="show-map-button"
// //             onClick={openFullScreenMap}
// //             aria-label={t('hotel.card.showOnMap')}
// //           >
// //             <i className={`fa-solid fa-location-dot ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
// //             {t('hotel.card.showOnMap')}
// //           </button>
// //         </div>
// //       </div>

// //       {/* Full-screen map component */}
// //       <FullScreenMap
// //         isOpen={isFullScreenMapOpen}
// //         onClose={() => setIsFullScreenMapOpen(false)}
// //         center={mapCenter}
// //         zoom={12}
// //         markers={hotelMarkers}
// //         title={mapDestination?.name || t('map.selectedArea')}
// //         hotels={actualHotels}
// //         destination={center ? {
// //           name: center.address || '',
// //           latitude: center.latitude,
// //           longitude: center.longitude
// //         } : undefined}
// //         isHotelSearchMap={true}
// //         filterData={mapFilterData}
// //         onHotelSelect={(hotelId) => {
// //           if (onHotelSelect) {
// //             onHotelSelect(hotelId.toString());
// //           }
// //         }}
// //       />
// //     </div>
// //   );
// // };

// // export default HotelSearchMap;

// 'use client';

// import React, { useState, useEffect } from 'react';
// import MapWrapper from '@/components/map/MapWrapper';
// import FullScreenMap from '@/components/map/FullScreenMap';
// import { useTranslation } from '@/app/hooks/useTranslation';
// import { useLanguage } from '@/app/contexts/languageContext';
// import './HotelSearchMap.scss';
// import { Hotel } from '../../hotel-search-result.model';
// import { HotelFilterData } from '@/app/components/utilities/helpers/hotel/filterHotels';
// import DetailMap from '@/components/map/detail-map/DetailMap';

// interface HotelSearchMapProps {
//   hotels?: Array<{
//     id: string;
//     name: string;
//     location: {
//       latitude: number;
//       longitude: number;
//       address?: string;
//     };
//     rating?: number;
//     price?: string;
//     image?: string;
//   }>;
//   destination?: {
//     name: string;
//     latitude: number;
//     longitude: number;
//   };
//   onHotelSelect?: (hotelId: string) => void;
//   actualHotels?: Hotel[];
//   filterData?: HotelFilterData;
//   // New prop to determine if we should show the detail map
//   isDetail?: boolean;
//   // Data needed for DetailMap
//   detailData?: {
//     name: string;
//     rating: number;
//     stars: string;
//     position: [number, number];
//     description: string;
//     reviews: string;
//     locationScore: string;
//     address: string;
//     attractions: Array<{
//       name: string;
//       distance: string;
//     }>;
//   };
//   // Additional handlers for DetailMap
//   onDetailFavoriteClick?: () => void;
//   onDetailCloseClick?: () => void;
//   onDetailShowPrices?: () => void;
// }

// // Dummy hotel data for testing
// const DUMMY_HOTELS = [
//   {
//     id: "hotel1",
//     name: "Blue Lagoon Resort",
//     location: {
//       latitude: 15.7,
//       longitude: 73.91,
//       address: "Arambol, Goa"
//     },
//     rating: 4.7,
//     price: "$120"
//   },
//   // ... other dummy hotels
// ];

// // Dummy destination
// const DUMMY_DESTINATION = {
//   name: "Goa, India",
//   latitude: 15.5,
//   longitude: 73.85
// };

// // Default detail data if needed
// const DEFAULT_DETAIL_DATA = {
//   name: "Blue Lagoon Resort",
//   rating: 8.7,
//   stars: "★★★★",
//   position: [15.7, 73.91] as [number, number],
//   description: "Excellent",
//   reviews: "1,234 reviews",
//   locationScore: "Great location - 9.2",
//   address: "Arambol Beach, Goa, India",
//   attractions: [
//     { name: "Arambol Beach", distance: "0.1 km" },
//     { name: "Sweet Water Lake", distance: "1.5 km" },
//     { name: "Mandrem Beach", distance: "3.2 km" }
//   ]
// };

// const HotelSearchMap: React.FC<HotelSearchMapProps> = ({
//   hotels = [],
//   destination,
//   onHotelSelect,
//   actualHotels: parentActualHotels,
//   filterData: parentFilterData,
//   isDetail = false,
//   detailData,
//   onDetailFavoriteClick,
//   onDetailCloseClick,
//   onDetailShowPrices
// }) => {
//   const { t } = useTranslation();
//   const { isRTL } = useLanguage();
//   const [mapHotels, setMapHotels] = useState(hotels);
//   const [mapDestination, setMapDestination] = useState(destination);
//   const [actualHotels, setActualHotels] = useState<Hotel[]>(parentActualHotels || []);
//   const [mapFilterData, setMapFilterData] = useState<HotelFilterData | undefined>(parentFilterData);
  
//   // State to control map visibility
//   const [isMapOpen, setIsMapOpen] = useState(false);

//   // Initialize detail data if not provided
//   const [activeDetailData, setActiveDetailData] = useState(detailData || DEFAULT_DETAIL_DATA);

//   // Use dummy data if no hotels are provided
//   useEffect(() => {
//     if (hotels.length === 0) {
//       setMapHotels(DUMMY_HOTELS);
//       setMapDestination(DUMMY_DESTINATION);
//     } else {
//       setMapHotels(hotels);
//       setMapDestination(destination);
//     }
//   }, [hotels, destination]);

//   // Update detail data when prop changes
//   useEffect(() => {
//     if (detailData) {
//       setActiveDetailData(detailData);
//     }
//   }, [detailData]);

//   // Use actual hotels from parent if provided, otherwise generate from mapHotels
//   useEffect(() => {
//     if (parentActualHotels && parentActualHotels.length > 0) {
//       setActualHotels(parentActualHotels);
//     } else if (mapHotels.length > 0) {
//       try {
//         // Generate hotel data from mapHotels if no actual hotels are provided
//         const generatedHotels: Hotel[] = mapHotels.map((hotel, index) => ({
//           hotelId: parseInt(hotel.id) || index + 1,
//           locationId: index + 1,
//           name: hotel.name,
//           locality: hotel.location.address?.split(',')[0] || '',
//           city: hotel.location.address?.split(',')[1]?.trim() || '',
//           userRating: '8.5',
//           userRatingCategory: 'Excellent',
//           starRating: hotel.rating || 4,
//           userRatingCount: 100 + index,
//           imageInfoList: hotel.image ? [{
//             url: hotel.image,
//             pictureId: '1',
//             caption: hotel.name,
//             imageCategory: 'hotel',
//             rank: 1
//           }] : [],
//           roomDetails: [{
//             type: 'Deluxe Room',
//             bedroom: 1,
//             livingRoom: 1,
//             bathroom: 1,
//             size: '30m²',
//             bed: '1 King Bed'
//           }],
//           comfortRating: 4.5,
//           bookingDetails: [{
//             nights: 2,
//             adults: 2
//           }],
//           taxesAndCharges: 500,
//           accommodationType: 'Hotel',
//           topOfferings: ['Free WiFi', 'Breakfast Included'],
//           roomsCountLeft: 5,
//           distanceFromSearchedEntity: '2.5 km',
//           fomoTags: [],
//           amenities: [{
//             name: 'Free WiFi',
//             amenityUrl: ''
//           }],
//           geoLocationInfo: {
//             lat: hotel.location.latitude,
//             lon: hotel.location.longitude
//           },
//           fareDetail: {
//             displayedBaseFare: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '1000'),
//             totalPrice: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '1000')
//           },
//           isVisible: true
//         }));

//         setActualHotels(generatedHotels);
//       } catch (error) {
//         console.error('Error generating hotel data:', error);
//         setActualHotels([]);
//       }
//     }
//   }, [parentActualHotels, mapHotels]);

//   // Use filter data from parent if provided
//   useEffect(() => {
//     if (parentFilterData && typeof parentFilterData === 'object') {
//       // Check if parentFilterData has the expected properties
//       if (parentFilterData.priceRange || parentFilterData.starRatings || parentFilterData.amenities) {
//         setMapFilterData(parentFilterData);
//       }
//     }
//   }, [parentFilterData]);

//   // Define center location
//   const center = mapDestination ? {
//     latitude: mapDestination.latitude,
//     longitude: mapDestination.longitude,
//     address: mapDestination.name
//   } : undefined;

//   // Open map
//   const openMap = () => {
//     setIsMapOpen(true);
//   };

//   // Close map handler
//   const closeMap = () => {
//     setIsMapOpen(false);
//   };

//   // Define map center for the fullscreen map
//   const mapCenter: [number, number] = center
//     ? [center.latitude, center.longitude]
//     : [15.5, 73.85]; // Default center (Goa)

//   // Create markers for hotels
//   const hotelMarkers = mapHotels.map(hotel => ({
//     id: hotel.id,
//     position: [hotel.location.latitude, hotel.location.longitude] as [number, number],
//     iconType: 'hotel' as const,
//     details: {
//       id: hotel.id,
//       name: hotel.name,
//       rating: hotel.rating,
//       price: hotel.price
//     }
//   }));

//   return (
//     <div className="hotel-search-map idle">
//       {/* Idle state - just show map background with no markers and a centered button */}
//       <div className="map-background">
//         <MapWrapper
//           center={center ? [center.latitude, center.longitude] : undefined}
//           zoom={12}
//           markers={[]}
//           style={{ height: '100%', width: '100%', borderRadius: '8px' }}
//           interactive={false} // Disable zoom controls and interaction
//         />
//         <div className="show-map-button-container">
//           <button
//             className="show-map-button"
//             onClick={openMap}
//             aria-label={t('hotel.card.showOnMap')}
//           >
//             <i className={`fa-solid fa-location-dot ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
//             {t('hotel.card.showOnMap')}
//           </button>
//         </div>
//       </div>

//       {/* Conditional rendering based on isDetail prop */}
//       {isDetail ? (
//         /* Detail Map component */
//         <DetailMap
//           location={activeDetailData}
//           style={{ height: '600px', width: '100%' }}
//           onFavoriteClick={onDetailFavoriteClick || (() => {})}
//           onCloseClick={() => {
//             closeMap();
//             if (onDetailCloseClick) onDetailCloseClick();
//           }}
//           onShowPrices={onDetailShowPrices || (() => {})}
//         />
//       ) : (
//         /* Full-screen map component */
//         <FullScreenMap
//           isOpen={isMapOpen}
//           onClose={closeMap}
//           center={mapCenter}
//           zoom={12}
//           markers={hotelMarkers}
//           title={mapDestination?.name || t('map.selectedArea')}
//           hotels={actualHotels}
//           destination={center ? {
//             name: center.address || '',
//             latitude: center.latitude,
//             longitude: center.longitude
//           } : undefined}
//           isHotelSearchMap={true}
//           filterData={mapFilterData}
//           onHotelSelect={(hotelId) => {
//             if (onHotelSelect) {
//               onHotelSelect(hotelId.toString());
//             }
//           }}
//         />
//       )}
//     </div>
//   );
// };

// export default HotelSearchMap;

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import MapWrapper from '@/components/map/MapWrapper';
import FullScreenMap from '@/components/map/FullScreenMap';
import { useTranslation } from '@/app/hooks/useTranslation';
import { useLanguage } from '@/app/contexts/languageContext';
import './HotelSearchMap.scss';
import { Hotel } from '../../hotel-search-result.model';
import { HotelFilterData } from '@/app/components/utilities/helpers/hotel/filterHotels';
import DetailMap from '@/components/map/detail-map/DetailMap';

interface HotelSearchMapProps {
  hotels?: Array<{
    id: string;
    name: string;
    location: {
      latitude: number;
      longitude: number;
      address?: string;
    };
    rating?: number;
    price?: string;
    image?: string;
  }>;
  destination?: {
    name: string;
    latitude: number;
    longitude: number;
  };
  onHotelSelect?: (hotelId: string) => void;
  actualHotels?: Hotel[];
  filterData?: HotelFilterData;
  onFilterChange?: (filterData: HotelFilterData | undefined) => void;
  // New prop to determine if we should show the detail map
  isDetail?: boolean;
  // Data needed for DetailMap
  detailData?: {
    name: string;
    rating: number;
    stars: string;
    position: [number, number];
    description: string;
    reviews: string;
    locationScore: string;
    address: string;
    attractions: Array<{
      name: string;
      distance: string;
    }>;
  };
  // Additional handlers for DetailMap
  onDetailFavoriteClick?: () => void;
  onDetailCloseClick?: () => void;
  onDetailShowPrices?: () => void;
}

// Dummy hotel data for testing
const DUMMY_HOTELS = [
  {
    id: "hotel1",
    name: "Blue Lagoon Resort",
    location: {
      latitude: 15.7,
      longitude: 73.91,
      address: "Arambol, Goa"
    },
    rating: 4.7,
    price: "$120"
  },
  // ... other dummy hotels
];

// Dummy destination
const DUMMY_DESTINATION = {
  name: "Goa, India",
  latitude: 15.5,
  longitude: 73.85
};

// Default detail data if needed
const DEFAULT_DETAIL_DATA = {
  name: "Blue Lagoon Resort",
  rating: 8.7,
  stars: "★★★★",
  position: [15.7, 73.91] as [number, number],
  description: "Excellent",
  reviews: "1,234 reviews",
  locationScore: "Great location - 9.2",
  address: "Arambol Beach, Goa, India",
  attractions: [
    { name: "Arambol Beach", distance: "0.1 km" },
    { name: "Sweet Water Lake", distance: "1.5 km" },
    { name: "Mandrem Beach", distance: "3.2 km" }
  ]
};

const HotelSearchMap: React.FC<HotelSearchMapProps> = ({
  hotels = [],
  destination,
  onHotelSelect,
  actualHotels: parentActualHotels,
  filterData: parentFilterData,
  onFilterChange,
  isDetail = false,
  detailData,
  onDetailFavoriteClick,
  onDetailCloseClick,
  onDetailShowPrices
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [mapHotels, setMapHotels] = useState(hotels);
  const [mapDestination, setMapDestination] = useState(destination);
  const [actualHotels, setActualHotels] = useState<Hotel[]>(parentActualHotels || []);
  const [mapFilterData, setMapFilterData] = useState<HotelFilterData | undefined>(parentFilterData);
  
  // State to control maps visibility
  const [isMapOpen, setIsMapOpen] = useState(false);
  const [isDetailMapOpen, setIsDetailMapOpen] = useState(false);

  // Initialize detail data if not provided
  const [activeDetailData, setActiveDetailData] = useState(detailData || DEFAULT_DETAIL_DATA);

  // Handle filter changes from FullScreenMap
  const handleFilterChange = useCallback((newFilterData: HotelFilterData | undefined) => {
    setMapFilterData(newFilterData);
    // Notify parent component of filter changes
    if (onFilterChange) {
      onFilterChange(newFilterData);
    }
  }, [onFilterChange]);

  // Use dummy data if no hotels are provided
  useEffect(() => {
    if (hotels.length === 0) {
      setMapHotels(DUMMY_HOTELS);
      setMapDestination(DUMMY_DESTINATION);
    } else {
      setMapHotels(hotels);
      setMapDestination(destination);
    }
  }, [hotels, destination]);



  // Update detail data when prop changes
  useEffect(() => {
    if (detailData) {
      setActiveDetailData(detailData);
    }
  }, [detailData]);

// Use actual hotels from parent if provided, otherwise generate from mapHotels
useEffect(() => {
  if (parentActualHotels && parentActualHotels.length > 0) {
    setActualHotels(parentActualHotels);
  } else if (mapHotels.length > 0) {
    try {
      // Generate hotel data from mapHotels if no actual hotels are provided
      const generatedHotels: Hotel[] = mapHotels.map((hotel, index) => ({
        hotelId: parseInt(hotel.id) || index + 1,
        locationId: index + 1,
        name: hotel.name,
        address: hotel.location.address || '',
        locality: hotel.location.address?.split(',')[0] || '',
        city: hotel.location.address?.split(',')[1]?.trim() || '',
        userRating: '8.5',
        userRatingCategory: 'Excellent',
        starRating: hotel.rating || 4,
        userRatingCount: 100 + index,
        imageInfoList: hotel.image ? [{
          url: hotel.image,
          pictureId: '1',
          caption: hotel.name,
          imageCategory: 'hotel',
          rank: 1
        }] : [],
        roomDetails: [{
          type: 'Deluxe Room',
          bedroom: 1,
          livingRoom: 1,
          bathroom: 1,
          size: '30m²',
          bed: '1 King Bed'
        }],
        comfortRating: 4.5,
        taxesAndCharges: 500,
        category: 'Hotel',
        hotelType: 'Hotel',
        accommodationType: 'Hotel',
        topOfferings: ['Free WiFi', 'Breakfast Included'],
        roomsCountLeft: 5,
        distanceFromSearchedEntity: '2.5 km',
        fomoTags: [],
        amenities: [{
          name: 'Free WiFi',
          amenityUrl: ''
        }],
        geoLocationInfo: {
          lat: hotel.location.latitude,
          lon: hotel.location.longitude
        },
        fareDetail: {
          displayedBaseFare: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '1000'),
          totalPrice: parseFloat(hotel.price?.replace(/[^0-9.]/g, '') || '1000')
        },
        about: `${hotel.name} is a comfortable hotel located in ${hotel.location.address || 'a prime location'}.`,
        // Add the missing required properties:
        specialAmenities: ['Free WiFi'], // Only one element as required by the type
        offer: {
          couponCode: `HOTEL${index + 1}`,
          instantDiscount: 500,
          displayedInstantDiscount: 500,
          cashback: 100,
          displayedCashback: 100,
          applyMessage: 'Discount applied automatically'
        },
        isVisible: true
      }));

      setActualHotels(generatedHotels);
    } catch (error) {
      console.error('Error generating hotel data:', error);
      setActualHotels([]);
    }
  }
}, [parentActualHotels, mapHotels]);

  // Use filter data from parent if provided
  useEffect(() => {
    if (parentFilterData && typeof parentFilterData === 'object') {
      // Check if parentFilterData has the expected properties
      if (parentFilterData.priceRange || parentFilterData.starRatings || parentFilterData.amenities) {
        setMapFilterData(parentFilterData);
      }
    }
  }, [parentFilterData]);

  // Define center location
  const center = mapDestination ? {
    latitude: mapDestination.latitude,
    longitude: mapDestination.longitude,
    address: mapDestination.name
  } : undefined;

  // Open normal map
  const openMap = () => {
    setIsMapOpen(true);
  };

  // Open detail map
  const openDetailMap = () => {
    setIsDetailMapOpen(true);
  };

  // Close maps
  const closeMap = () => {
    setIsMapOpen(false);
  };

  const closeDetailMap = () => {
    setIsDetailMapOpen(false);
    if (onDetailCloseClick) onDetailCloseClick();
  };

  // Define map center
  const mapCenter: [number, number] = center
    ? [center.latitude, center.longitude]
    : [15.5, 73.85]; // Default center (Goa)

  // Create markers for hotels
  const hotelMarkers = mapHotels.map(hotel => ({
    id: hotel.id,
    position: [hotel.location.latitude, hotel.location.longitude] as [number, number],
    iconType: 'hotel' as const,
    details: {
      id: hotel.id,
      name: hotel.name,
      rating: hotel.rating,
      price: hotel.price
    }
  }));

  return (
    <div className="hotel-search-map idle">
      {/* Idle state - show map background with button */}
      <div className="map-background">
        <MapWrapper
          center={center ? [center.latitude, center.longitude] : undefined}
          zoom={12}
          markers={[]}
          style={{ height: '100%', width: '100%', borderRadius: '8px' }}
          interactive={false} // Disable zoom controls and interaction
        />
        <div className="show-map-button-container">
          <button
            className="show-map-button"
            onClick={isDetail ? openDetailMap : openMap}
            aria-label={t('hotel.card.showOnMap')}
          >
            <i className={`fa-solid fa-location-dot ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
            {t('hotel.card.showOnMap')}
          </button>
        </div>
      </div>

      {/* Full-screen map component */}
      {!isDetail && (
        <FullScreenMap
          isOpen={isMapOpen}
          onClose={closeMap}
          center={mapCenter}
          zoom={12}
          markers={hotelMarkers}
          title={mapDestination?.name || t('map.selectedArea')}
          hotels={actualHotels}
          destination={center ? {
            name: center.address || '',
            latitude: center.latitude,
            longitude: center.longitude
          } : undefined}
          isHotelSearchMap={true}
          filterData={mapFilterData}
          onFilterChange={handleFilterChange}
          onHotelSelect={(hotelId) => {
            if (onHotelSelect) {
              onHotelSelect(hotelId.toString());
            }
          }}
        />
      )}

      {/* Fixed position DetailMap */}
      {isDetail && (
        <div className={`detail-map-fixed-container ${isDetailMapOpen ? 'active' : ''}`}>
          <div className="detail-map-content">
            {/* Custom close button */}
            <button 
              className="detail-map-close" 
              onClick={closeDetailMap}
              aria-label={t('common.close')}
            >
              <i className="fa-solid fa-times"></i>
            </button>
            
            {/* DetailMap component */}
            <DetailMap
              location={activeDetailData}
              style={{ height: '100%', width: '100%' }}
              onFavoriteClick={onDetailFavoriteClick}
              onCloseClick={closeDetailMap}
              onShowPrices={onDetailShowPrices}
            />
            
            {/* Optional footer with hotel information */}
            <div className="detail-map-footer">
              <div className="hotel-details">
                <h4>{activeDetailData.name}</h4>
                <p className="location">{activeDetailData.address}</p>
              </div>
              <div className="hotel-actions">
                <button onClick={onDetailFavoriteClick}>
                  <i className="fa-regular fa-heart"></i> {t('hotel.favorite')}
                </button>
                <button className="primary" onClick={onDetailShowPrices}>
                  {t('hotel.showPrices')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HotelSearchMap;