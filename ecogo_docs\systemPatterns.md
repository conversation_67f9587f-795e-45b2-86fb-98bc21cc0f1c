# System Patterns

## Architecture
- Next.js application with React components
- Client-side rendering for dynamic content
- Component-based architecture with reusable UI elements

## Component Structure
- Page components in the `app` directory following Next.js App Router pattern
- Reusable UI components in the `components` directory
- Feature-specific components organized in feature directories

## Data Flow
- Data is typically fetched at the page level
- Props are passed down to child components
- Some components fetch their own data when necessary
- Context API is used for global state (e.g., language context)

## Map Components
- `MapWrapper`: Base component that wraps Leaflet map functionality
- `MapContainer`: Container component that handles map display and controls
- `FullScreenMap`: Component for displaying maps in full-screen mode
- `FullScreenHotelSearchMap`: Specialized full-screen map for hotel search with filtering

## Hotel Search Flow
1. `HotelSearchResult` page fetches hotel data
2. Data is filtered based on user selections
3. Filtered data is displayed in hotel cards and map
4. Map components visualize hotel locations

## Component Patterns
- Container components handle data fetching and state management
- Presentation components focus on rendering UI
- Higher-order components provide additional functionality (e.g., translations)
- Responsive design with mobile-first approach
- Reusable form components (e.g., OtpInput) for consistent user experience
- Compound components for complex UI elements

## State Management
- React hooks for local component state
- Context API for global state
- Props for passing data between components
