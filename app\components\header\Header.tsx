// "use client";

// import React, { useEffect, useState, useRef } from "react";
// import Link from "next/link";
// import Image from "next/image";
// import "./header.scss";
// import Logo2 from "../../../public/assets/img/logo2.jpg";
// import { useRouter } from "next/navigation";
// import { useCommonContext } from "@/app/contexts/commonContext";
// import { useLanguage } from "@/app/contexts/languageContext";
// import { useTranslation } from "@/app/hooks/useTranslation";
// import { languageMap } from "@/i18n";
// import LoginSignupPopup from "../login/LoginSignupPopup";
// import MobileMenuModal from "./components/MobileMenu/MobileMenu";
// import useScrollLock from "../utilities/ScrollLock/useScrollLock";
// import MobileSubMenuModal from "./components/MobileSubMenuModal/MobileSubMenuModal";
// import { Country } from "@/common-models/common.model";

// // Define types for better type safety
// type Currency = {
//   name: string;
//   symbol: string;
// };

// type NavItem = {
//   label: string;
//   icon: string;
// };

// type PriceView = {
//   label: string;
//   icon: string;
//   description: string;
// };

// type Notification = {
//   id: string;
//   message: string;
//   timestamp: string;
//   isRead: boolean;
// };

// // Extract data to constants outside component to prevent recreation on each render
// const CURRENCIES: Currency[] = [
//   { name: "INR", symbol: "₹" },
//   { name: "USD", symbol: "$" },
//   { name: "EUR", symbol: "€" },
//   { name: "GBP", symbol: "£" },
//   { name: "JPY", symbol: "¥" },
//   { name: "AUD", symbol: "A$" },
//   { name: "CAD", symbol: "C$" },
//   { name: "CHF", symbol: "CHF" },
//   { name: "CNY", symbol: "¥" },
//   { name: "HKD", symbol: "HK$" },
//   { name: "NZD", symbol: "NZ$" },
//   { name: "SGD", symbol: "S$" },
//   { name: "KRW", symbol: "₩" },
//   { name: "RUB", symbol: "₽" },
//   { name: "ZAR", symbol: "R" },
//   { name: "AED", symbol: "د.إ" },
//   { name: "THB", symbol: "฿" },
//   { name: "MXN", symbol: "Mex$" },
//   { name: "BRL", symbol: "R$" },
//   { name: "MYR", symbol: "RM" },
//   { name: "SEK", symbol: "kr" },
// ];

// const NAV_ITEMS: NavItem[] = [
//   { label: "Hotels", icon: "fa-hotel" },
//   { label: "Holidays", icon: "fa-plane" },
//   { label: "Activities", icon: "fa-person-hiking" },
//   { label: "Visa", icon: "fa-passport" },
//   { label: "Cruise", icon: "fa-ship" },
// ];

// const Settings: NavItem[] = [
//   { label: "Language", icon: "fa-globe" },
//   { label: "Price Display", icon: "fa-tag" },
//   { label: "Helpline", icon: "fa-phone-volume" },
// ];

// const PROFILE_NAV_ITEMS: NavItem[] = [
//   {
//     label: "Bookings",
//     icon: "",
//   },
//   {
//     label: "Profile",
//     icon: "",
//   },
//   {
//     label: "Wishlist",
//     icon: "",
//   },
//   {
//     label: "Reviews",
//     icon: "",
//   },
//   {
//     label: "Logout",
//     icon: "",
//   },
// ];

// const PROFILE_NAV_ITEMS_MOBILE: NavItem[] = [
//   {
//     label: "Bookings",
//     icon: "fa-calendar-check",
//   },
//   {
//     label: "Profile",
//     icon: "fa-user",
//   },
//   {
//     label: "Wishlist",
//     icon: "fa-heart",
//   },
//   {
//     label: "Reviews",
//     icon: "fa-star",
//   },
// ];

// const Price_View: PriceView[] = [
//   {
//     label: "Average per night",
//     icon: "fa-tags",
//     description: "Includes taxes & fees",
//   },
//   {
//     label: "Base per night",
//     icon: "fa-tags",
//     description: "Room rate only",
//   },
// ];

// export interface CurrencyItem {
//   name: string;
//   symbol?: string;
//   code: string;
// }

// export const currencyList: CurrencyItem[] = [
//   { name: "Afghani", symbol: "؋", code: "AFN" },
//   { name: "Arab Emirates Dirham", symbol: "د.إ", code: "AED" },
//   { name: "Australian Dollar", symbol: "A$", code: "AUD" },
//   { name: "Bangladeshi Taka", symbol: "৳", code: "BDT" },
//   { name: "Bahraini Dinar", symbol: ".د.ب", code: "BHD" },
//   { name: "Brazilian Real", symbol: "R$", code: "BRL" },
//   { name: "British Pound", symbol: "£", code: "GBP" },
//   { name: "Bulgarian Lev", symbol: "лв", code: "BGN" },
//   { name: "Cambodian Riel", symbol: "៛", code: "KHR" },
//   { name: "Canadian Dollar", symbol: "C$", code: "CAD" },
//   { name: "CFP Franc", symbol: "₣", code: "XPF" },
//   { name: "Chinese Yuan", symbol: "¥", code: "CNY" },
//   { name: "Czech Koruna", symbol: "Kč", code: "CZK" },
//   { name: "Danish Krone", symbol: "kr", code: "DKK" },
//   { name: "Euro", symbol: "€", code: "EUR" },
//   { name: "Hong Kong Dollar", symbol: "HK$", code: "HKD" },
//   { name: "Hungarian Forint", symbol: "Ft", code: "HUF" },
//   { name: "Indian Rupee", symbol: "₹", code: "INR" },
//   { name: "Indonesian Rupiah", symbol: "Rp", code: "IDR" },
//   { name: "Israeli New Shekel", symbol: "₪", code: "ILS" },
//   { name: "Japanese Yen", symbol: "¥", code: "JPY" },
//   { name: "Kazakhstani Tenge", symbol: "₸", code: "KZT" },
//   { name: "Kenyan Shilling", symbol: "KSh", code: "KES" },
//   { name: "Kuwaiti Dinar", symbol: "د.ك", code: "KWD" },
//   { name: "Malaysian Ringgit", symbol: "RM", code: "MYR" },
//   { name: "Mexican Peso", symbol: "$", code: "MXN" },
//   { name: "Nepalese Rupee", symbol: "रु", code: "NPR" },
//   { name: "New Zealand Dollar", symbol: "NZ$", code: "NZD" },
//   { name: "Nigerian Naira", symbol: "₦", code: "NGN" },
//   { name: "Norwegian Krone", symbol: "kr", code: "NOK" },
//   { name: "Omani Rial", symbol: "﷼", code: "OMR" },
//   { name: "Pakistani Rupee", symbol: "₨", code: "PKR" },
//   { name: "Philippine Peso", symbol: "₱", code: "PHP" },
//   { name: "Polish Zloty", symbol: "zł", code: "PLN" },
//   { name: "Qatari Riyal", symbol: "﷼", code: "QAR" },
//   { name: "Romanian Leu", symbol: "lei", code: "RON" },
//   { name: "Russian Ruble", symbol: "₽", code: "RUB" },
//   { name: "Saudi Riyal", symbol: "﷼", code: "SAR" },
//   { name: "Singapore Dollar", symbol: "S$", code: "SGD" },
//   { name: "South African Rand", symbol: "R", code: "ZAR" },
//   { name: "South Korean Won", symbol: "₩", code: "KRW" },
//   { name: "Sri Lankan Rupee", symbol: "Rs", code: "LKR" },
//   { name: "Swedish Krona", symbol: "kr", code: "SEK" },
//   { name: "Swiss Franc", symbol: "CHF", code: "CHF" },
//   { name: "Taiwan Dollar", symbol: "NT$", code: "TWD" },
//   { name: "Thai Baht", symbol: "฿", code: "THB" },
//   { name: "Turkish Lira", symbol: "₺", code: "TRY" },
//   { name: "Ukrainian Hryvnia", symbol: "₴", code: "UAH" },
//   { name: "US Dollar", symbol: "$", code: "USD" },
//   { name: "Vietnamese Dong", symbol: "₫", code: "VND" },
// ];

// const Header: React.FC = () => {
//   const [selectedCurrency, setSelectedCurrency] = useState<string>("INR");
//   const [selectedNavItem, setSelectedNavItem] = useState<string>("Hotels");
//   const [isProfileDdVisible, setIsProfileDdVisible] = useState<boolean>(false);
//   const [isLoginSignupPopupOpen, setIsLoginSignupPopupOpen] =
//     useState<boolean>(false);
//   const [isMobileMenuVisible, setIsMobileMenuVisible] =
//     useState<boolean>(false);
//   const [notifications, setNotifications] = useState<Notification[]>([]);
//   const [showNotifications, setShowNotifications] = useState<boolean>(false);
//   const [unreadCount, setUnreadCount] = useState<number>(0);
//   const notificationRef = useRef<HTMLDivElement>(null);
//   const {
//     isLoggedIn,
//     setIsLoggedIn,
//     setUserData,
//     userData,
//     setHotelSearchFormData,
//   } = useCommonContext();
//   const { currentLanguage, changeLanguage } = useLanguage();
//   const { t } = useTranslation("common");
//   const router = useRouter();
//   const [activeProfileItem, setActiveProfileItem] = useState<string>("");
//   const [isLanguageModalVisible, setIsLanguageModalVisible] =
//     useState<boolean>(false);
//   const [isPriceDisplayModalVisible, setIsPriceDisplayModalVisible] =
//     useState<boolean>(false);
//   const [countries, setCountries] = useState<Country[]>([]);
//   const [currentSelectedLanguage, setCurrentSelectedLanguage] =
//     useState<string>("English");
//   const [currentSelectedPriceView, setCurrentSelectedPriceView] =
//     useState<string>("Average per night");
//   const [currentSelectedCurrency, setCurrentSelectedCurrency] =
//     useState<string>("AED");
//   const [searchQuery, setSearchQuery] = useState("");

//   // Filtered currency list
//   const filteredCurrencies = currencyList?.filter(
//     (currency) =>
//       currency.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
//       currency.code.toLowerCase().includes(searchQuery.toLowerCase())
//   );

//   useEffect(() => {
//     console.log("ff", currentSelectedLanguage);
//   }, [currentSelectedLanguage]);

//   useEffect(() => {
//     const userData = localStorage.getItem("userData");
//     const searchData = localStorage.getItem("hotelSearchFormData");
//     if (userData) {
//       setIsLoggedIn(true);
//       setUserData(JSON.parse(userData));
//     }
//     if (searchData) {
//       setHotelSearchFormData(JSON.parse(searchData));
//     }
//   }, [setIsLoggedIn, setUserData, setHotelSearchFormData]);

//   // Mock notifications data - in a real app, you'd fetch these from an API
//   useEffect(() => {
//     // Simulating notifications data
//     const mockNotifications: Notification[] = [
//       {
//         id: "1",
//         message: "Your hotel booking in Dubai is confirmed!",
//         timestamp: "2 hours ago",
//         isRead: false,
//       },
//       {
//         id: "2",
//         message: "New holiday package for Bali is available now.",
//         timestamp: "1 day ago",
//         isRead: false,
//       },
//       {
//         id: "3",
//         message: "Your flight to Paris departs tomorrow.",
//         timestamp: "2 days ago",
//         isRead: true,
//       },
//     ];

//     setNotifications(mockNotifications);
//     setUnreadCount(mockNotifications.filter((n) => !n.isRead).length);
//   }, []);

//   // Close notifications dropdown when clicking outside
//   useEffect(() => {
//     function handleClickOutside(event: MouseEvent) {
//       if (
//         notificationRef.current &&
//         !notificationRef.current.contains(event.target as Node)
//       ) {
//         setShowNotifications(false);
//       }
//     }
//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [notificationRef]);

//   // Close profile dropdown when clicking outside
//   useEffect(() => {
//     function handleClickOutside(event: MouseEvent) {
//       if (isProfileDdVisible) {
//         const target = event.target as HTMLElement;
//         if (!target.closest(".login-button")) {
//           setIsProfileDdVisible(false);
//         }
//       }
//     }
//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [isProfileDdVisible]);

//   useEffect(() => {
//     fetch("/assets/json/countries.json")
//       .then((response) => response.json())
//       .then((data) => setCountries(data))
//       .catch((error) => console.error("Error fetching JSON:", error));
//   }, []);

//   const handleProfileMenuClick = (item: string) => {
//     setIsProfileDdVisible(false);
//     setIsMobileMenuVisible(false);
//     setActiveProfileItem(item);

//     if (
//       item === "Bookings" ||
//       item == "Profile" ||
//       item == "Reviews" ||
//       item == "Wishlist"
//     ) {
//       router.push(`/profile?tab=${item}`);
//     } else if (item == "Logout") {
//       localStorage.removeItem("isLoggedIn");
//       localStorage.removeItem("userData");
//       setIsLoggedIn(false);
//       router.push("/");
//     }
//   };

//   const handleProfileMenuAtMobileClick = (item: string) => {
//     // Close profile dropdown
//     setIsProfileDdVisible(false);
//     setActiveProfileItem(item);

//     // Close mobile menu for all items except Language
//     if (item !== "Language") {
//       setIsMobileMenuVisible(false);
//     }

//     // Handle different menu items
//     switch (item) {
//       case "Bookings":
//       case "Profile":
//       case "Reviews":
//       case "Wishlist":
//         router.push(`/profile?tab=${item}`);
//         break;

//       case "Language":
//         setIsLanguageModalVisible(true);
//         break;

//       case "Price Display":
//         setIsPriceDisplayModalVisible(true);
//         setIsMobileMenuVisible(true);
//         break;

//       case "Logout":
//         localStorage.removeItem("isLoggedIn");
//         localStorage.removeItem("userData");
//         setIsLoggedIn(false);
//         router.push("/");
//         break;
//     }
//   };
//   const handleNotificationClick = () => {
//     setShowNotifications(!showNotifications);
//   };

//   const markAllAsRead = () => {
//     const updatedNotifications = notifications.map((notification) => ({
//       ...notification,
//       isRead: true,
//     }));
//     setNotifications(updatedNotifications);
//     setUnreadCount(0);
//   };

//   const viewAllNotifications = () => {
//     setShowNotifications(false);
//     // Navigate to notifications page
//     router.push("/Notifications");
//   };

//   const handleCurrentSelectedMobileLanguage = (language: string) => {
//     setCurrentSelectedLanguage(language);
//     setIsLanguageModalVisible(false);
//   };

//   useScrollLock(isMobileMenuVisible);
//   useScrollLock(isLanguageModalVisible);
//   useScrollLock(isPriceDisplayModalVisible);

//   return (
//     <>
//       <header className="header">
//         <div className="top-bar">
//           <div className="logo">
//             <Link href="/">
//               <Image
//                 src={Logo2}
//                 alt="Kindali Travels"
//                 fill
//                 style={{ objectFit: "contain" }}
//               />
//             </Link>
//           </div>
//           <i
//             onClick={() => setIsMobileMenuVisible(true)}
//             className="fa-solid fa-bars phone-menu-btn"
//           ></i>

//           <div className="right-section">
//             <div className="dropdown">
//               <button className="dropdown-button" aria-label="Helpline Options">
//                 {t("header.helpline")}{" "}
//                 <i className="fas fa-angle-down" aria-hidden="true"></i>
//               </button>
//               <div className="dropdown-content">
//                 <a href="tel:+971800729626">{t("header.contact.phone")}</a>
//                 <a href="mailto:<EMAIL>">
//                   {t("header.contact.email")}
//                 </a>
//               </div>
//             </div>

//             <div className="dropdown">
//               <button
//                 className="dropdown-button"
//                 aria-label="Language Selection"
//               >
//                 {languageMap[currentLanguage]}{" "}
//                 <i className="fas fa-angle-down" aria-hidden="true"></i>
//               </button>
//               <div className="dropdown-content">
//                 {Object.entries(languageMap).map(([code, name]) => (
//                   <button
//                     key={code}
//                     onClick={() => changeLanguage(code)}
//                     className={`dropdown-item ${
//                       currentLanguage === code ? "active" : ""
//                     }`}
//                   >
//                     {name}
//                   </button>
//                 ))}
//               </div>
//             </div>

//             <div className="dropdown">
//               <button
//                 className="dropdown-button"
//                 aria-label="Currency Selection"
//               >
//                 {selectedCurrency}{" "}
//                 <i className="fas fa-angle-down" aria-hidden="true"></i>
//               </button>
//               <div className="dropdown-content last-item">
//                 <div className="currency-selector-container">
//                   {CURRENCIES.map((currency) => (
//                     <button
//                       key={currency.name}
//                       onClick={() => setSelectedCurrency(currency.name)}
//                       className={`currency-selector ${
//                         selectedCurrency === currency.name ? "active" : ""
//                       }`}
//                       aria-pressed={selectedCurrency === currency.name}
//                     >
//                       <span className="currency-name">{currency.name}</span>
//                       <span className="currency-symbol">{currency.symbol}</span>
//                     </button>
//                   ))}
//                 </div>
//               </div>
//             </div>

//             {isLoggedIn && (
//               <>
//                 {" "}
//                 {/* Notification Bell */}
//                 <div className="notification-bell" ref={notificationRef}>
//                   <button
//                     className="notification-button"
//                     onClick={handleNotificationClick}
//                     aria-label="Notifications"
//                   >
//                     <i className="fas fa-bell" aria-hidden="true"></i>
//                     {unreadCount > 0 && (
//                       <span className="notification-count">{unreadCount}</span>
//                     )}
//                   </button>

//                   {showNotifications && (
//                     <div className="notification-dropdown">
//                       <div className="notification-header">
//                         <h3>Notifications</h3>
//                         {unreadCount > 0 && (
//                           <button
//                             className="mark-read-button"
//                             onClick={markAllAsRead}
//                           >
//                             Mark all as read
//                           </button>
//                         )}
//                       </div>

//                       <div className="notification-list">
//                         {notifications.length > 0 ? (
//                           notifications.map((notification) => (
//                             <div
//                               key={notification.id}
//                               className={`notification-item ${
//                                 !notification.isRead ? "unread" : ""
//                               }`}
//                             >
//                               <p className="notification-message">
//                                 {notification.message}
//                               </p>
//                               <span className="notification-time">
//                                 {notification.timestamp}
//                               </span>
//                             </div>
//                           ))
//                         ) : (
//                           <div className="notification-empty">
//                             <p>No notifications yet</p>
//                           </div>
//                         )}
//                       </div>

//                       <div className="notification-footer">
//                         <button onClick={viewAllNotifications}>
//                           View all notifications
//                         </button>
//                       </div>
//                     </div>
//                   )}
//                 </div>
//               </>
//             )}

//             <div className="login-button">
//               {isLoggedIn ? (
//                 <div onClick={() => setIsProfileDdVisible(true)}>
//                   <i className="fas fa-user" aria-hidden="true"></i>{" "}
//                   {userData?.first_name}
//                 </div>
//               ) : (
//                 <div
//                   className=""
//                   onClick={() => setIsLoginSignupPopupOpen(true)}
//                 >
//                   Login/signup
//                 </div>
//               )}

//               {isProfileDdVisible && (
//                 <>
//                   <div className="profile-drop-down active">
//                     <h3 className="profile-drop-down__header">MY ACCOUNT</h3>
//                     <div className="profile-drop-down__content">
//                       <ul>
//                         {PROFILE_NAV_ITEMS.map((item, index) => (
//                           <li
//                             className={`${
//                               activeProfileItem == item.label ? "active" : ""
//                             }`}
//                             key={index}
//                             onClick={() => handleProfileMenuClick(item.label)}
//                           >
//                             {item.label}
//                           </li>
//                         ))}
//                       </ul>
//                     </div>
//                   </div>

//                   <div
//                     className="hotel-dropdown-overlay"
//                     onClick={(e) => {
//                       e.stopPropagation();
//                       setIsProfileDdVisible(false);
//                     }}
//                   ></div>
//                 </>
//               )}
//             </div>
//           </div>
//         </div>

//         <nav className="navigation">
//           <div className="nav-container">
//             {NAV_ITEMS.map((item) => (
//               <Link
//                 key={item.label}
//                 href="#"
//                 onClick={(e) => {
//                   e.preventDefault();
//                   setSelectedNavItem(item.label);
//                 }}
//                 className={`nav-item ${
//                   selectedNavItem === item.label ? "active" : ""
//                 }`}
//                 aria-current={
//                   selectedNavItem === item.label ? "page" : undefined
//                 }
//               >
//                 <i className={`fas ${item.icon}`} aria-hidden="true"></i>{" "}
//                 {t(`header.nav.${item.label.toLowerCase()}`)}
//               </Link>
//             ))}
//           </div>
//         </nav>
//       </header>
//       <MobileMenuModal
//         title={userData?.first_name}
//         isOpen={isMobileMenuVisible}
//         handleClose={() => setIsMobileMenuVisible(false)}
//       >
//         <div>
//           <div className="mobile-menu-group">
//             <h3 className="mobile-menu-group__header">ACCOUNT</h3>
//             <div className="mobile-menu-group__content">
//               <ul>
//                 {PROFILE_NAV_ITEMS_MOBILE.map((item, index) => (
//                   <li
//                     className={`${
//                       activeProfileItem == item.label ? "active" : ""
//                     }`}
//                     key={index}
//                     onClick={() => handleProfileMenuAtMobileClick(item.label)}
//                   >
//                     <span className={`fa-solid ${item.icon}`}></span>
//                     {item.label}
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           </div>

//           <div className="mobile-menu-group">
//             <h3 className="mobile-menu-group__header">Settings</h3>
//             <div className="mobile-menu-group__content">
//               <ul>
//                 {Settings.map((item, index) => (
//                   <li
//                     onClick={() => handleProfileMenuAtMobileClick(item.label)}
//                     key={index}
//                   >
//                     <span className={`fa-solid ${item.icon}`}></span>
//                     {item.label}
//                   </li>
//                 ))}
//               </ul>
//             </div>
//           </div>
//         </div>
//       </MobileMenuModal>

//       <MobileSubMenuModal
//         isOpen={isLanguageModalVisible}
//         handleClose={() => setIsLanguageModalVisible(false)}
//         title="Language"
//       >
//         <div className="common-content-container">
//           <div className="common-content-container__header">
//             Suggested languages
//           </div>

//           <div className="common-content-container__content-container">
//             <div
//               className="common-content-container__content"
//               onClick={() => handleCurrentSelectedMobileLanguage("English")}
//             >
//               <div className="common-content-container__label-with-icon">
//                 <Image
//                   className="common-content-container__img"
//                   alt="national-flag-image"
//                   width={24}
//                   height={18}
//                   src={`https://upload.wikimedia.org/wikipedia/commons/thumb/0/0b/English_language.svg/2560px-English_language.svg.png`}
//                 ></Image>

//                 <span className="common-content-container__label">English</span>
//               </div>

//               <div>
//                 <i
//                   className={`fa-solid fa-check checkIcon ${
//                     currentSelectedLanguage == "English" ? "active" : ""
//                   }`}
//                 ></i>
//               </div>
//             </div>
//           </div>
//         </div>
//         <div className="common-content-container">
//           <div className="common-content-container__header">All languages</div>

//           <div className="common-content-container__content-container">
//             {countries?.map((nationality, index) => (
//               <div
//                 className="common-content-container__content"
//                 key={index}
//                 onClick={() =>
//                   handleCurrentSelectedMobileLanguage(nationality.native)
//                 }
//               >
//                 <div className="common-content-container__label-with-icon">
//                   <Image
//                     className="common-content-container__img"
//                     alt="national-flag-image"
//                     width={24}
//                     height={18}
//                     src={`/assets/img/country-logo/${nationality.iso2.toLowerCase()}.webp`}
//                   ></Image>

//                   <span className="common-content-container__label">
//                     {nationality?.native}
//                   </span>
//                 </div>

//                 <div>
//                   <i
//                     className={`fa-solid fa-check checkIcon ${
//                       currentSelectedLanguage == nationality.native
//                         ? "active"
//                         : ""
//                     }`}
//                   ></i>
//                 </div>
//               </div>
//             ))}
//           </div>
//         </div>
//       </MobileSubMenuModal>

//       <MobileSubMenuModal
//         isOpen={isPriceDisplayModalVisible}
//         handleClose={() => setIsPriceDisplayModalVisible(false)}
//         title="Price Display"
//       >
//         <div>
//           <div className="common-content-container">
//             <div className="common-content-container__header">price view</div>

//             <div className="common-content-container__content-container">
//               {Price_View.map((priceview, index) => (
//                 <div
//                   className="common-content-container__content"
//                   key={index}
//                   onClick={() => setCurrentSelectedPriceView(priceview.label)}
//                 >
//                   <div>
//                     <div className="common-content-container__label-with-icon-description">
//                       <div className="common-content-container__label-with-icon">
//                         <i
//                           className={`fa-solid fa-tags common-content-container__label-icon ${priceview?.icon}`}
//                         ></i>
//                         <span className="mobile-languages-container__label">
//                           {priceview?.label}
//                         </span>
//                       </div>
//                       <p className="common-content-container__description">
//                         {priceview?.description}
//                       </p>
//                     </div>
//                   </div>

//                   <div>
//                     <i
//                       className={`fa-solid fa-check checkIcon ${
//                         currentSelectedPriceView == priceview?.label
//                           ? "active"
//                           : ""
//                       }`}
//                     ></i>
//                   </div>
//                 </div>
//               ))}
//             </div>

//             <div className="common-content-container__header">
//               Choose a currency
//               <div className="common-content-container__header-input">
//                 <span className="searchIcon">
//                   <i className="fa-solid fa-magnifying-glass"></i>
//                 </span>
//                 <input
//                   value={searchQuery}
//                   onChange={(e) => setSearchQuery(e.target.value)}
//                   type="text"
//                   placeholder="Search"
//                 />
//               </div>
//             </div>

//             {filteredCurrencies.length > 0 ? (
//               filteredCurrencies?.map((currency, index) => (
//                 <div
//                   onClick={() => {
//                     setCurrentSelectedCurrency(currency.code);
//                     setIsPriceDisplayModalVisible(false);
//                   }}
//                   key={index}
//                   className="common-content-container__content"
//                 >
//                   <div className="common-content-container__label-with-icon">
//                     <span className="common-content-container__label">
//                       {currency?.name}
//                     </span>
//                   </div>

//                   <div className="flex items-center">
//                     <div className="mr-2 flex">
//                       <span className="border-r border-gray-400 pr-2">
//                         {currency?.symbol}
//                       </span>
//                       <span className="pl-2">{currency?.code}</span>
//                     </div>
//                     <span style={{ minWidth: "20px" }}>
//                       <i
//                         className={`fa-solid fa-check checkIcon ${
//                           currentSelectedCurrency == currency.code
//                             ? "active"
//                             : ""
//                         }`}
//                       ></i>
//                     </span>
//                   </div>
//                 </div>
//               ))
//             ) : (
//               <>No data found...</>
//             )}
//           </div>
//         </div>
//       </MobileSubMenuModal>

//       <LoginSignupPopup
//         isOpen={isLoginSignupPopupOpen}
//         onClose={() => setIsLoginSignupPopupOpen(false)}
//       />
//     </>
//   );
// };

// export default Header;

"use client";

import React, { useEffect, useState, useRef } from "react";
import { createPortal } from "react-dom";
import Image from "next/image";
import "./header.scss";
import { useRouter, usePathname } from "next/navigation";
import { useCommonContext } from "@/app/contexts/commonContext";
import { useLanguage } from "@/app/contexts/languageContext";
import { useTranslation } from "@/app/hooks/useTranslation";
import { languageMap, languageFlags } from "@/i18n";
import MobileMenuModal from "./components/MobileMenu/MobileMenu";
import MobileMenuContent, { MobileMenuModals } from "./components/MobileMenuContent/MobileMenuContent";
import useScrollLock from "../utilities/ScrollLock/useScrollLock";
import MobileSubMenuModal from "./components/MobileSubMenuModal/MobileSubMenuModal";
import LanguageSelector from "./components/LanguageSelector/LanguageSelector";
import CurrencySelector from "./components/CurrencySelector/CurrencySelector";
import { Country } from "@/common-models/common.model";
import LoginModal from "../login/LoginModal";

// Icon Component for both PNG and SVG
interface IconProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
}

const IconComponent: React.FC<IconProps> = ({ src, alt, className = "", width = 20, height = 20 }) => {
  const isSvg = src.endsWith('.svg');
  const isKindaliServiceIcon = src.includes('Kindali Service icons');
  const isBluePngIcon = src.includes('BLUE') && src.includes('PNG');

  // Debug logging removed - icons should now be working

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={`${className} object-contain`}
      style={{
        // No filter for Kindali Service icons or blue PNG icons (they should keep their original colors)
        filter: (isKindaliServiceIcon || isBluePngIcon) ? 'none' : (isSvg ? 'none' : 'brightness(0) invert(1)'),
        minWidth: `${width}px`,
        minHeight: `${height}px`
      }}
      onError={(e) => {
        console.error('Image failed to load:', src, e);
      }}
      // Add these props for better Next.js Image optimization
      priority={false}
      placeholder="empty"
      unoptimized={false}
    />
  );
};

// Simple SVG Icon Component for missing icons
interface SvgIconProps {
  className?: string;
  children: React.ReactNode;
}

const SvgIcon: React.FC<SvgIconProps> = ({ className = "", children }) => (
  <svg
    className={className}
    fill="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    {children}
  </svg>
);

// Icon mapping for Kindali Service icons
const iconPaths = {
  hotel: "/assets/img/Kindali Service icons/BLUE HOTEL PNG.png",
  flight: "/assets/img/Kindali Service icons/BLUE FLIGHT PNG.png",
  visa: "/assets/img/Kindali Service icons/BLUE VISA PNG.png",
  cruise: "/assets/img/Kindali Service icons/BLUE SHIP PNG.png",
  attractions: "/assets/img/Kindali icons/icons8-chicago-100.png", // Using chicago icon for attractions
};

// Icon components for missing icons
const Globe = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
  </SvgIcon>
);

const ChevronDown = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
  </SvgIcon>
);

const Headphones = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 1c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h3v-8H5v-2c0-3.87 3.13-7 7-7s7 3.13 7 7v2h-4v8h3c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9z"/>
  </SvgIcon>
);

const Bell = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
  </SvgIcon>
);

const User = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
  </SvgIcon>
);

const Menu = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
  </SvgIcon>
);

// Additional icons for profile menu
const Calendar = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
  </SvgIcon>
);

const Heart = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
  </SvgIcon>
);

const Star = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
  </SvgIcon>
);

const LogOut = ({ className }: { className?: string }) => (
  <SvgIcon className={className}>
    <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
  </SvgIcon>
);

// Icon renderer function
const renderIcon = (iconName: string, className: string = "w-5 h-5") => {
  switch (iconName) {
    case "calendar":
      return <Calendar className={className} />;
    case "user":
      return <User className={className} />;
    case "heart":
      return <Heart className={className} />;
    case "star":
      return <Star className={className} />;
    case "logout":
      return <LogOut className={className} />;
    case "phone":
      return <Headphones className={className} />;
    default:
      return <User className={className} />;
  }
};

// Removed renderNavIcon function - using PNG icons with IconComponent instead
import { getZIndex } from "@/app/constants/zIndex";

// Define types for better type safety
type NavItem = {
  label: string;
  icon: string;
};

type PriceView = {
  label: string;
  icon: string;
  description: string;
};

type Notification = {
  id: string;
  message: string;
  timestamp: string;
  isRead: boolean;
};

const Settings: NavItem[] = [
  // { label: "Language", icon: "globe" },
  // { label: "Price Display", icon: "tag" },
  { label: "Helpline", icon: "phone" },
];

const PROFILE_NAV_ITEMS: NavItem[] = [
  {
    label: "Bookings",
    icon: "calendar",
  },
  {
    label: "Profile",
    icon: "user",
  },
  {
    label: "Wishlist",
    icon: "heart",
  },
  {
    label: "Reviews",
    icon: "star",
  },
  {
    label: "Logout",
    icon: "logout",
  },
];

const PROFILE_NAV_ITEMS_MOBILE: NavItem[] = [
  {
    label: "Bookings",
    icon: "calendar",
  },
  {
    label: "Profile",
    icon: "user",
  },
  {
    label: "Wishlist",
    icon: "heart",
  },
  {
    label: "Reviews",
    icon: "star",
  },
   {
    label: "Helpline",
    icon: "phone",
  },
];

const Price_View: PriceView[] = [
  {
    label: "Average per night",
    icon: "tags",
    description: "Includes taxes & fees",
  },
  {
    label: "Base per night",
    icon: "tag",
    description: "Room rate only",
  },
];

export interface CurrencyItem {
  name: string;
  symbol?: string;
  code: string;
}

export const currencyList: CurrencyItem[] = [
  { name: "Afghani", symbol: "؋", code: "AFN" },
  { name: "Arab Emirates Dirham", symbol: "د.إ", code: "AED" },
  { name: "Australian Dollar", symbol: "A$", code: "AUD" },
  { name: "Bahraini Dinar", symbol: ".د.ب", code: "BHD" },
  { name: "Brazilian Real", symbol: "R$", code: "BRL" },
  { name: "British Pound", symbol: "£", code: "GBP" },
  { name: "Chinese Yuan", symbol: "¥", code: "CNY" },
  { name: "Euro", symbol: "€", code: "EUR" },
  { name: "Indian Rupee", symbol: "₹", code: "INR" },
  { name: "Japanese Yen", symbol: "¥", code: "JPY" },
  { name: "Kuwaiti Dinar", symbol: "د.ك", code: "KWD" },
  { name: "Omani Rial", symbol: "﷼", code: "OMR" },
  { name: "Pakistani Rupee", symbol: "₨", code: "PKR" },
  { name: "Philippine Peso", symbol: "₱", code: "PHP" },
  { name: "Qatari Riyal", symbol: "﷼", code: "QAR" },
  { name: "Russian Ruble", symbol: "₽", code: "RUB" },
  { name: "Saudi Riyal", symbol: "﷼", code: "SAR" },
  { name: "Singapore Dollar", symbol: "S$", code: "SGD" },
  { name: "US Dollar", symbol: "$", code: "USD" },
];

const Header: React.FC = () => {
  const [selectedCurrency, setSelectedCurrency] = useState<CurrencyItem>(
    { name: "Indian Rupee", symbol: "₹", code: "INR" },
  );
  const [activeTab, setActiveTab] = useState<string>("hotels");
  const [isProfileDdVisible, setIsProfileDdVisible] = useState<boolean>(false);
  const [isLoginSignupPopupOpen, setIsLoginSignupPopupOpen] =
    useState<boolean>(false);
  const [isMobileMenuVisible, setIsMobileMenuVisible] =
    useState<boolean>(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showNotifications, setShowNotifications] = useState<boolean>(false);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [windowWidth, setWindowWidth] = useState<number | undefined>(undefined);
  const [isMounted, setIsMounted] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const {
    isLoggedIn,
    setIsLoggedIn,
    setUserData,
    userData,
    setHotelSearchFormData,
    isMobileModalOpen,
  } = useCommonContext();
  const { currentLanguage, changeLanguage, isRTL } = useLanguage();
  const { t } = useTranslation("common");
  const router = useRouter();
  const pathname = usePathname();
  const [activeProfileItem, setActiveProfileItem] = useState<string>("");
  const [isLanguageModalVisible, setIsLanguageModalVisible] =
    useState<boolean>(false);
  const [isPriceDisplayModalVisible, setIsPriceDisplayModalVisible] =
    useState<boolean>(false);
  const [countries, setCountries] = useState<Country[]>([]);
  const [currentSelectedLanguage, setCurrentSelectedLanguage] =
    useState<string>("English");
  const [currentSelectedPriceView, setCurrentSelectedPriceView] =
    useState<string>("Average per night");
  const [currentSelectedCurrency, setCurrentSelectedCurrency] =
    useState<string>("INR");
    const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState<boolean>(false);
  const [isCurrencyDropdownOpen, setIsCurrencyDropdownOpen] = useState<boolean>(false);

  // Mobile menu modal states
  const [isMobileLanguageModalOpen, setIsMobileLanguageModalOpen] = useState<boolean>(false);
  const [isMobilePriceModalOpen, setIsMobilePriceModalOpen] = useState<boolean>(false);

  // Refs for positioning dropdowns
  const languageButtonRef = useRef<HTMLButtonElement>(null);
  const currencyButtonRef = useRef<HTMLButtonElement>(null);
  const notificationButtonRef = useRef<HTMLButtonElement>(null);
  const profileButtonRef = useRef<HTMLButtonElement>(null);

  // Handle window resize for dynamic layout changes
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // Set initial window width and mounted state
    if (typeof window !== 'undefined') {
      setWindowWidth(window.innerWidth);
      setIsMounted(true);
      window.addEventListener('resize', handleResize);
    }

    // Cleanup event listener
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  useEffect(() => {
    console.log("ff", currentSelectedLanguage);
  }, [currentSelectedLanguage]);

  useEffect(() => {
    // Only access localStorage on the client side
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem("userData");
      const searchData = localStorage.getItem("hotelSearchFormData");
      if (userData) {
        setIsLoggedIn(true);
        setUserData(JSON.parse(userData));
      }
      if (searchData) {
        setHotelSearchFormData(JSON.parse(searchData));
      }
    }
  }, [setIsLoggedIn, setUserData, setHotelSearchFormData]);

  // Mock notifications data - in a real app, you'd fetch these from an API
  useEffect(() => {
    // Simulating notifications data
    const mockNotifications: Notification[] = [
      {
        id: "1",
        message: "Your hotel booking in Dubai is confirmed!",
        timestamp: "2 hours ago",
        isRead: false,
      },
      {
        id: "2",
        message: "New holiday package for Bali is available now.",
        timestamp: "1 day ago",
        isRead: false,
      },
      {
        id: "3",
        message: "Your flight to Paris departs tomorrow.",
        timestamp: "2 days ago",
        isRead: true,
      },
    ];

    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter((n) => !n.isRead).length);
  }, []);

  // Close notifications dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target as Node)
      ) {
        setShowNotifications(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [notificationRef]);

  // Close profile dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (isProfileDdVisible) {
        const target = event.target as HTMLElement;
        if (!target.closest(".profile-dropdown-container")) {
          setIsProfileDdVisible(false);
        }
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isProfileDdVisible]);

  useEffect(() => {
    fetch("/assets/json/countries.json")
      .then((response) => response.json())
      .then((data) => setCountries(data))
      .catch((error) => console.error("Error fetching JSON:", error));
  }, []);

  const handleProfileMenuClick = (item: string) => {
    setIsProfileDdVisible(false);
    setIsMobileMenuVisible(false);
    setActiveProfileItem(item);

    if (
      item === "Bookings" ||
      item == "Profile" ||
      item == "Reviews" ||
      item == "Wishlist"
    ) {
      router.push(`/profile?tab=${item}`);
    } else if (item == "Logout") {
      // Only need to remove userData from localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem("userData");
      }
      setIsLoggedIn(false);
      router.push("/");
    }
  };

  const handleProfileMenuAtMobileClick = (item: string) => {
    // Close profile dropdown
    setIsProfileDdVisible(false);
    setActiveProfileItem(item);

    // Close mobile menu for all items except Language
    if (item !== "Language") {
      setIsMobileMenuVisible(false);
    }

    // Handle different menu items
    switch (item) {
      case "Bookings":
      case "Profile":
      case "Reviews":
      case "Wishlist":
        router.push(`/profile?tab=${item}`);
        setIsProfileDdVisible(false);
        break;

      case "Language":
        setIsLanguageModalVisible(true);
        break;

      case "Price Display":
        setIsPriceDisplayModalVisible(true);
        setIsMobileMenuVisible(true);
        break;

      case "Logout":
        // Only need to remove userData from localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem("userData");
        }
        setIsLoggedIn(false);
        router.push("/");
        break;
    }
  };

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
  };

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map((notification) => ({
      ...notification,
      isRead: true,
    }));
    setNotifications(updatedNotifications);
    setUnreadCount(0);
  };

  const viewAllNotifications = () => {
    setShowNotifications(false);
    // Navigate to notifications page
    router.push("/Notifications");
  };

  const handleCurrentSelectedMobileLanguage = (language: string) => {
    setCurrentSelectedLanguage(language);
    setIsLanguageModalVisible(false);
  };

  useScrollLock(isMobileMenuVisible);
  useScrollLock(isLanguageModalVisible);
  useScrollLock(isPriceDisplayModalVisible);

  // Navigation handler for nav items
  const handleNavItemClick = (itemId: string) => {
    setActiveTab(itemId);

    // Navigate to appropriate pages
    switch (itemId) {
      case "hotels":
        router.push("/Hotel");
        break;
      case "flight":
        router.push("/flights");
        break;
      case "visa":
        router.push("/visa");
        break;
      case "cruise":
        router.push("/cruise");
        break;
      default:
        break;
    }
  };

  return (
    <>
  {/* Header with new white background and primary-color text styling */}
<header
  style={{zIndex: isMobileModalOpen ? 0 : getZIndex('sticky')}}
  className={`bg-white text-primary-color shadow-lg border-b border-gray-200 md:fixed w-full top-0 ${isRTL ? 'rtl' : ''}`}
>
  <div className="container mx-auto px-4">
    <div className="flex justify-between items-center h-16">
      {/*
        LOGO SECTION - Kindali official logo
        - Clickable logo that navigates to home page
        - Consistent sizing across all devices
      */}
      <div className="cursor-pointer" onClick={()=>router.push('/')}>
        <Image
          src="/assets/img/kindali logo/kindali-logo-blue.png"
          alt="Kindali Logo"
          width={100}
          height={100}
          style={{minWidth:'140px'}}
          className="w-full h-auto"
          priority
        />
      </div>
        {
          isMounted && windowWidth !== undefined && windowWidth >= 1280 && (  <div className={`container mx-auto px-4`}>
    <div className={`flex gap-4 overflow-x-auto hide-scrollbar justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
      {[
        { id: "hotels", icon: iconPaths.hotel, label: t("header.nav.hotels", "Stays") },
      ].map(item => {
        return (
          <button
            key={item.id}
            className={`px-4 py-1 font-medium text-sm transition-all duration-200 flex flex-row items-center justify-center cursor-pointer whitespace-nowrap gap-2 rounded-lg ${
              activeTab === item.id
                ? "bg-gradient-to-br from-blue-50 to-blue-100 text-primary-color shadow-sm"
                : "text-primary-color hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm"
            }`}
            onClick={() => handleNavItemClick(item.id)}
          >
            <div
              className="w-8 h-8 transition-all duration-200"
              style={{
                filter: activeTab === item.id ? 'grayscale(0)' : 'grayscale(1)',
                transition: 'filter 0.2s ease-in-out'
              }}
            >
              <IconComponent
                src={item.icon}
                alt={item.label}
                className="w-8 h-8"
                width={32}
                height={32}
              />
            </div>
            <span className="text-sm font-semibold">{item.label}</span>
          </button>
        );
      })}
      {[
        { id: "flight", icon: iconPaths.flight, label: t("header.nav.flights", "Flights") },
        { id: "visa", icon: iconPaths.visa, label: t("header.nav.visa", "Visa") },
        { id: "cruise", icon: iconPaths.cruise, label: t("header.nav.cruise", "Cruise") }
      ].map(item => {
        return (
          <button
            key={item.id}
            className={`px-4 py-1 font-medium text-sm transition-all duration-200 flex flex-row items-center justify-center cursor-pointer whitespace-nowrap gap-2 rounded-lg ${
              activeTab === item.id
                ? "bg-gradient-to-br from-blue-50 to-blue-100 text-primary-color shadow-sm"
                : "text-primary-color hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm"
            }`}
            onClick={() => handleNavItemClick(item.id)}
          >
            <div
              className="w-8 h-8 transition-all duration-200"
              style={{
                filter: activeTab === item.id ? 'grayscale(0)' : 'grayscale(1)',
                transition: 'filter 0.2s ease-in-out'
              }}
            >
              <IconComponent
                src={item.icon}
                alt={item.label}
                className="w-8 h-8"
                width={32}
                height={32}
              />
            </div>
            <span className="text-sm font-semibold">{item.label}</span>
          </button>
        );
      })}
    </div>
  </div>)
        }
      <div className={`hidden md:flex items-center ${isRTL ? 'space-x-reverse space-x-8' : 'space-x-8'}`}>
        {/* Language Dropdown */}
        <div className="relative">
          <button
            ref={languageButtonRef}
            onClick={() => {
              setIsLanguageDropdownOpen(!isLanguageDropdownOpen);
              setIsCurrencyDropdownOpen(false); // Close currency dropdown
            }}
            className={`text-primary-color hover:text-primary-color-dark text-sm font-semibold flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap hover:opacity-80`}
          >
            <Globe className="w-5 h-5 text-primary-color" />
            <span>{languageMap[currentLanguage] || "English"}</span>
            <ChevronDown className={`w-4 h-4 text-primary-color transition-transform duration-200 ${isLanguageDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

        </div>

        {/* Currency Dropdown */}
        <div className="relative">
          <button
            ref={currencyButtonRef}
            onClick={() => {
              setIsCurrencyDropdownOpen(!isCurrencyDropdownOpen);
              setIsLanguageDropdownOpen(false); // Close language dropdown
            }}
            className={`text-primary-color hover:text-primary-color-dark text-sm font-semibold flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap hover:opacity-80`}
          >
            <span>{selectedCurrency.symbol}</span>
            <span>{selectedCurrency.code}</span>
            <ChevronDown className={`w-4 h-4 text-primary-color transition-transform duration-200 ${isCurrencyDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

        </div>

        {/* Customer Service */}
        <div className="relative">
          <button className={`text-primary-color hover:text-primary-color-dark text-sm font-semibold flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap hover:opacity-80`}>
            <Headphones className="w-5 h-5 text-primary-color" />
            <span>{t("header.helpline")}</span>
          </button>
        </div>

        {/* Notification Bell (if logged in) */}
        {isLoggedIn && (
          <div className="relative" ref={notificationRef}>
            <button
              ref={notificationButtonRef}
              onClick={handleNotificationClick}
              className={`text-primary-color hover:text-primary-color-dark text-sm font-medium flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} transition-all duration-200 cursor-pointer !rounded-button whitespace-nowrap relative`}
            >
              <Bell className="w-5 h-5 text-primary-color" />
              {unreadCount > 0 && (
                <span className={`absolute -top-1 ${isRTL ? '-left-1' : '-right-1'} bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center`}>
                  {unreadCount}
                </span>
              )}
            </button>


          </div>
        )}

        <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
          {/* Login/Register Button */}
          {isLoggedIn ? (
            <div className="relative profile-dropdown-container">
              <button
                ref={profileButtonRef}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsProfileDdVisible(!isProfileDdVisible);
                }}
                className={`bg-primary-color text-white px-6 py-2 rounded-md hover:bg-primary-color-dark transition-all duration-200 text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} shadow-sm hover:shadow`}
              >
                <User className="w-5 h-5 text-white" />
                <span>{userData?.first_name || t("common.account", "Account")}</span>
              </button>


            </div>
          ) : (
            <button
              onClick={() => setIsLoginSignupPopupOpen(true)}
              className={`bg-primary-color text-white px-6 py-2 rounded-md hover:bg-primary-color-dark transition-all duration-200 text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} shadow-sm hover:shadow`}
            >
              <User className="w-5 h-5 text-white" />
              <span>{t("common.signInRegister", "Sign in / Register")}</span>
            </button>
          )}
        </div>
      </div>

      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuVisible(true)}
        className="md:hidden text-primary-color p-2 hover:bg-gray-100 rounded-md transition-colors duration-200"
      >
        <Menu className="w-6 h-6 text-primary-color" />
      </button>
    </div>

      {
          isMounted && windowWidth !== undefined && windowWidth < 1280 && pathname === '/Hotel' && (
          <div className={`container hide-scrollbar`}>
            <div className={`flex justify-start sm:justify-center overflow-x-auto  gap-1 ${isRTL ? 'flex-row-reverse' : ''} px-4`} style={{scrollbarWidth: 'none', msOverflowStyle: 'none'}}>
  {[
    { id: "hotels", icon: iconPaths.hotel, label: t("header.nav.hotels", "Stays") },
    { id: "flight", icon: iconPaths.flight, label: t("header.nav.flights", "Flights") },
    { id: "visa", icon: iconPaths.visa, label: t("header.nav.visa", "Visa") },
    { id: "cruise", icon: iconPaths.cruise, label: t("header.nav.cruise", "Cruise") }
  ].map(item => {
    const isActive = activeTab === item.id;

    return (
      <button
        key={item.id}
        className={`px-3 py-1 font-medium text-sm transition-all duration-200 flex flex-col items-center justify-center cursor-pointer whitespace-nowrap ${
          activeTab === item.id
            ? "bg-gradient-to-br from-blue-50 to-blue-100 text-primary-color shadow-sm rounded-t-lg"
            : "text-primary-color hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 hover:shadow-sm rounded-lg"
        }`}
        onClick={() => handleNavItemClick(item.id)}
        role="tab"
        aria-selected={isActive}
        aria-controls={`${item.id}-panel`}
      >
        <div
          className="w-8 h-8 mb-1 transition-all duration-200"
          style={{
            filter: activeTab === item.id ? 'grayscale(0)' : 'grayscale(1)',
            transition: 'filter 0.2s ease-in-out'
          }}
        >
          <IconComponent
            src={item.icon}
            alt={item.label}
            className="w-8 h-8"
            width={36}
            height={36}
          />
        </div>
        <span className="text-xs font-semibold">{item.label}</span>
      </button>
    );
  })}
</div>
          </div>
      )}
  </div>
</header>

{/* Portal Dropdowns - Outside header stacking context */}
{isMounted && typeof window !== 'undefined' && createPortal(
  <>
    {/* Language Dropdown */}
    {isLanguageDropdownOpen && languageButtonRef.current && (
      <div
        className="fixed bg-white rounded-md shadow-lg z-[250] py-2 w-56"
        style={{
          top: languageButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 4,
          left: isRTL ? languageButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       languageButtonRef.current.getBoundingClientRect().right + window.scrollX - 224,
        }}
      >
        {Object.entries(languageMap)
          .filter(([code]) => ['en', 'ar', 'es', 'fr', 'hi'].includes(code))
          .map(([code, name]) => (
            <a
              key={code}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                changeLanguage(code);
                setIsLanguageDropdownOpen(false);
              }}
              className={`flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 ${currentLanguage === code ? 'bg-gray-100' : ''}`}
            >
              <div className="w-6 h-4 mr-3 relative overflow-hidden rounded-sm border border-gray-200">
                <Image
                  src={languageFlags[code]}
                  alt={`${name} flag`}
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <span>{name}</span>
            </a>
          ))}
      </div>
    )}

    {/* Currency Dropdown */}
    {isCurrencyDropdownOpen && currencyButtonRef.current && (
      <div
        className="fixed bg-white rounded-xl shadow-lg border border-gray-200 z-[250]"
        style={{
          top: currencyButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 8,
          left: isRTL ? currencyButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       currencyButtonRef.current.getBoundingClientRect().right + window.scrollX - 300,
        }}
      >
        <div className="currency-selector-container">
          {currencyList.map((currency, index) => (
            <button
              key={index}
              onClick={() => {
                setSelectedCurrency(currency);
                setIsCurrencyDropdownOpen(false);
              }}
              className={`currency-selector ${selectedCurrency.code === currency.code ? "active" : ""}`}
            >
              <span className="currency-name">{currency.code}</span>
              <span className="currency-symbol">{currency.symbol}</span>
            </button>
          ))}
        </div>
      </div>
    )}

    {/* Notifications Dropdown */}
    {showNotifications && notificationButtonRef.current && (
      <div
        className={`fixed bg-white rounded-md shadow-lg z-[250] py-2 w-64 ${isRTL ? 'text-right' : 'text-left'}`}
        style={{
          top: notificationButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 4,
          left: isRTL ? notificationButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       notificationButtonRef.current.getBoundingClientRect().right + window.scrollX - 256,
        }}
      >
        <div className="px-4 py-2 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-gray-800 font-medium">{t("common.notifications", "Notifications")}</h3>
          {unreadCount > 0 && (
            <button
              className="text-blue-600 text-xs hover:text-blue-800"
              onClick={markAllAsRead}
            >
              {t("common.markAllAsRead", "Mark all as read")}
            </button>
          )}
        </div>
        <div className="max-h-64 overflow-y-auto">
          {notifications.length > 0 ? (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`px-4 py-3 border-b border-gray-100 hover:bg-gray-50 ${
                  !notification.isRead ? "bg-blue-50" : ""
                }`}
              >
                <p className="text-sm text-gray-800">{notification.message}</p>
                <span className="text-xs text-gray-500 mt-1 block">
                  {notification.timestamp}
                </span>
              </div>
            ))
          ) : (
            <div className="px-4 py-3 text-center text-gray-500 text-sm">
              {t("common.noNotifications", "No notifications yet")}
            </div>
          )}
        </div>
        <div className="px-4 py-2 text-center border-t border-gray-200">
          <button
            onClick={viewAllNotifications}
            className="text-blue-600 text-sm hover:text-blue-800"
          >
            {t("common.viewAllNotifications", "View all notifications")}
          </button>
        </div>
      </div>
    )}

    {/* Profile Dropdown */}
    {isProfileDdVisible && profileButtonRef.current && (
      <div
        className={`fixed bg-white rounded-md shadow-lg z-[250] py-2 w-48 ${isRTL ? 'text-right' : 'text-left'}`}
        style={{
          top: profileButtonRef.current.getBoundingClientRect().bottom + window.scrollY + 4,
          left: isRTL ? profileButtonRef.current.getBoundingClientRect().left + window.scrollX :
                       profileButtonRef.current.getBoundingClientRect().right + window.scrollX - 192,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="px-4 py-2 border-b border-gray-200">
          <h3 className="text-gray-800 font-medium text-sm">{t("common.myAccount", "MY ACCOUNT")}</h3>
        </div>
        {PROFILE_NAV_ITEMS.map((item, index) => {
          return (
            <a
              key={index}
              href="#"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleProfileMenuClick(item.label);
              }}
              className={` px-4 py-2 text-sm flex items-center ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'} ${
                activeProfileItem === item.label
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              {renderIcon(item.icon, "w-5 h-5")}
              <span>{t(`profile.${item.label.toLowerCase()}`, item.label)}</span>
            </a>
          );
        })}
      </div>
    )}
  </>,
  document.body
)}

{/* Light Overlay for all dropdowns */}
{(isLanguageDropdownOpen || isCurrencyDropdownOpen || showNotifications || isProfileDdVisible) && (
  <div
    className="fixed inset-0 bg-black bg-opacity-20 z-[240]"
    onClick={() => {
      setIsLanguageDropdownOpen(false);
      setIsCurrencyDropdownOpen(false);
       if (setShowNotifications) setShowNotifications(false);
  if (setIsProfileDdVisible) setIsProfileDdVisible(false);
    }}
  />
)}

{/* Spacer for fixed header */}
<div className="h-0 md:h-16"></div>

{/* Navigation Tabs */}

{/* Mobile Menu Modal */}
<MobileMenuModal
  isOpen={isMobileMenuVisible}
  handleClose={() => setIsMobileMenuVisible(false)}
  isRTL={isRTL}
>
  <MobileMenuContent
    userData={userData}
    activeProfileItem={activeProfileItem}
    isRTL={isRTL}
    handleProfileMenuAtMobileClick={handleProfileMenuAtMobileClick}
    profileItems={PROFILE_NAV_ITEMS_MOBILE}
    settingsItems={Settings}
    isLanguageModalOpen={isMobileLanguageModalOpen}
    isPriceModalOpen={isMobilePriceModalOpen}
    setIsLanguageModalOpen={setIsMobileLanguageModalOpen}
    setIsPriceModalOpen={setIsMobilePriceModalOpen}
    currentPathname={pathname}
  />
</MobileMenuModal>

{/* Mobile Menu Modals - Rendered outside mobile menu for full screen coverage */}
<MobileMenuModals
  isLanguageModalOpen={isMobileLanguageModalOpen}
  isPriceModalOpen={isMobilePriceModalOpen}
  setIsLanguageModalOpen={setIsMobileLanguageModalOpen}
  setIsPriceModalOpen={setIsMobilePriceModalOpen}
  selectedCurrency={selectedCurrency}
  setSelectedCurrency={setSelectedCurrency}
  currentLanguage={currentLanguage}
  changeLanguage={changeLanguage}
/>

{/* Language Modal */}
<MobileSubMenuModal
  isOpen={isLanguageModalVisible}
  handleClose={() => setIsLanguageModalVisible(false)}
  title={t("settings.language", "Language")}
  isRTL={isRTL}
  onBack={() => {
    console.log("Language modal back button clicked");
    setIsLanguageModalVisible(false);
    setIsMobileMenuVisible(true);
  }}
>
  <LanguageSelector
    handleLanguageSelection={handleCurrentSelectedMobileLanguage}
    isRTL={isRTL}
  />
</MobileSubMenuModal>

{/* Price Display Modal */}
<MobileSubMenuModal
  isOpen={isPriceDisplayModalVisible}
  handleClose={() => setIsPriceDisplayModalVisible(false)}
  title={t("settings.priceDisplay", "Price Display")}
  isRTL={isRTL}
  onBack={() => {
    console.log("Currency modal back button clicked");
    setIsPriceDisplayModalVisible(false);
    setIsMobileMenuVisible(true);
  }}
>
  <CurrencySelector
    currencies={currencyList}
    currentSelectedCurrency={currentSelectedCurrency}
    handleCurrencySelection={(currencyCode) => {
      setCurrentSelectedCurrency(currencyCode);
      setIsPriceDisplayModalVisible(false);
      setIsMobileMenuVisible(true);
    }}
    priceViewOptions={Price_View}
    currentSelectedPriceView={currentSelectedPriceView}
    handlePriceViewSelection={setCurrentSelectedPriceView}
  />
</MobileSubMenuModal>

{/* Login/Signup Popup */}
<LoginModal
  registerRoute="/profile?tab=Profile"
  isOpen={isLoginSignupPopupOpen}
  onClose={() => setIsLoginSignupPopupOpen(false)}
/>
    </>
  );
};

export default Header;