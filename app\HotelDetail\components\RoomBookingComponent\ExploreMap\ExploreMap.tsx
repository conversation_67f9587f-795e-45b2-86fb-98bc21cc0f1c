'use client';

import React from 'react';
import './ExploreMap.scss';
import MapContainer from '@/components/map/MapContainer';
import { useTranslation } from '@/app/hooks/useTranslation';

interface ExploreMapProps {
  hotelLocation?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  hotelDetails?: {
    name: string;
    rating?: number;
    price?: string;
  };
}

function ExploreMap({
  hotelLocation = {
    latitude: 15.2108,
    longitude: 73.9506,
    address: '613/D Near Zuri Resort, Pedda Nomeio, Varca South Goa, 403721'
  },
  hotelDetails = {
    name: 'Zuri White Sands Resort',
    rating: 4.5,
    price: '$120 per night'
  }
}: ExploreMapProps) {
  const { t } = useTranslation();

  // // Define nearby points of interest
  // const pointsOfInterest = [
  //   {
  //     name: t('Varca Beach'),
  //     location: {
  //       latitude: hotelLocation.latitude + 0.01,
  //       longitude: hotelLocation.longitude + 0.01
  //     }
  //   },
  //   {
  //     name: t('Seafood Restaurant'),
  //     location: {
  //       latitude: hotelLocation.latitude - 0.005,
  //       longitude: hotelLocation.longitude + 0.008
  //     }
  //   },
  //   {
  //     name: t('Cavelossim Market'),
  //     location: {
  //       latitude: hotelLocation.latitude + 0.008,
  //       longitude: hotelLocation.longitude - 0.005
  //     }
  //   }
  // ];

  return (
    <div className="explore-map-container">
      {/* <MapContainer
        center={hotelLocation}
        zoom={14}
        height="400px"
        width="100%"
        hotels={[
          {
            id: '1',
            name: hotelDetails.name,
            location: hotelLocation,
            rating: hotelDetails.rating,
            price: hotelDetails.price
          }
        ]}
        // pointsOfInterest={pointsOfInterest}
        showHeader={true}
        title={t('Explore the Area')}
        showGoogleMapsLink={true}
        googleMapsLinkText={t('View On Google Maps')}
      /> */}
    </div>
  );
}

export default ExploreMap;