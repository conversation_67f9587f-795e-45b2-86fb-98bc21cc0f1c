"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import "./HotelDetail.scss";
import RoomBookingComponent from "./components/RoomBookingComponent/RoomBookingComponent";
import ShimmerImageGallery from "./components/ShimmerImageGallery/ShimmerImageGallery";
import ShimmerInfoHeader from "./components/ShimmerInfoHeader/ShimmerInfoHeader";
import { useCommonContext } from "../contexts/commonContext";
import useScrollLock from "../components/utilities/ScrollLock/useScrollLock";
import HotelFacilities from "./components/RoomBookingComponent/HotelFacilities/HotelFacilities";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslation } from "@/app/hooks/useTranslation";
import { showToast } from "../components/utilities/SonnerToasterCustom";
import { getHotelDetails } from "@/api/hotel/hotel-detail-service";
import { getRoomsAndRates } from "@/api/hotel/rooms-and-rates-service";

import axios from "axios";

const API_URL = "/data/hoteldetails.json"

function Page() {
  const { t } = useTranslation();
  const { hotelSearchFormData } = useCommonContext();
  const [isShimmerLoading, setisShimmerLoading] = useState<boolean>(false);
  const roomBookingRef = useRef<{ scrollToRoomCard: () => void } | null>(null);
  const [isMobileFacilitiesActive, setIsMobileFacilitiesActive] = useState<boolean>(false);

  // Client-side only states to prevent hydration mismatch
  const [mounted, setMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [canShare, setCanShare] = useState(false);

  const parentOverviewRef = useRef<HTMLDivElement>(null!);

  const router = useRouter();
  const searchParams = useSearchParams();
  const { hotelDetailsResponse, setHotelDetailsResponse, setIsLoading, selectedHotelId, setSelectedHotelId, searchKey } = useCommonContext();

  // Initialize client-side only features after component mounts
  useEffect(() => {
    setMounted(true);
    
    // Check if mobile
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 950);
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    // Check if sharing is supported
    setCanShare(typeof navigator !== 'undefined' && !!navigator.share);
    
    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  // useEffect(() => {
  //   setIsLoading(false);
  //   setTimeout(() => {
  //     setisShimmerLoading(false);
  //   }, 2000);
  // }, [setIsLoading]);

  const handleReserveClick = () => {
    roomBookingRef.current?.scrollToRoomCard();
  };

  // const hotelImages = [
  //   {
  //     src: "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
  //     alt: "hotel-image",
  //     category: "rooms",
  //   },
  //   {
  //     src: "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
  //     alt: "hotel-image",
  //     category: "exterior",
  //   },
  //   {
  //     src: "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
  //     alt: "hotel-image",
  //     category: "public areas",
  //   },
  //   {
  //     src: "https://q-xx.bstatic.com/xdata/images/hotel/840x460/233593433.jpg?k=87ddadf81d7dff133d755e3ff3778afa1f252b03368775286d7f1d0e297ffc00&o=",
  //     alt: "hotel-image",
  //     category: "bathroom",
  //   },
  //   {
  //     src: "https://pix8.agoda.net/hotelImages/8348930/-1/cfc1cc12d06b9bb675e7d26ccee0b7ec.jpg?ca=10&ce=1&s=1024x768",
  //     alt: "hotel-image",
  //     category: "dining areas",
  //   },
  //   {
  //     src: "https://pix8.agoda.net/hotelImages/8348930/-1/83dd539294d01e36130154d723abc7e4.jpg?ca=10&ce=1&s=1024x768",
  //     alt: "hotel-image",
  //     category: "food & beverages",
  //   },
  //   {
  //     src: "https://pix8.agoda.net/hotelImages/8348930/-1/3fdde253ef33cd5cd385828a571f0914.jpg?ca=10&ce=1&s=1024x768",
  //     alt: "hotel-image",
  //     category: "facilities",
  //   },
  //   {
  //     src: "https://pix8.agoda.net/hotelImages/8348930/-1/522586791962f1f04ba04b4dcac29a4b.jpg?ca=10&ce=1&s=1024x768",
  //     alt: "hotel-image",
  //     category: "rooms",
  //   },
  // ];

  useScrollLock(isMobileFacilitiesActive);

  const scrollToOverview = () => {
    parentOverviewRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  };

  const handlaSave = useCallback(() => {
    showToast("Successfully added to wishlist!", "default", "top-right");
  }, []);

  const shareHotel = useCallback((hotel: { name: string; url: string }) => {
    // Only execute on client side
    if (!mounted || typeof navigator === 'undefined') return;
    
    if (canShare) {
      navigator
        .share({
          title: `Check out this hotel: ${hotel.name}`,
          text: `Found this great hotel: ${hotel.name}`,
          url: hotel.url,
        })
        .then(() => console.log("Shared successfully"))
        .catch((error) => console.error("Error sharing:", error));
    } else if (navigator.clipboard) {
      navigator.clipboard.writeText(hotel.url)
        .then(() => {
          showToast("Link copied to clipboard!", "default", "top-right");
        })
        .catch(() => {
          // Fallback for older browsers
          const textArea = document.createElement('textarea');
          textArea.value = hotel.url;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          showToast("Link copied to clipboard!", "default", "top-right");
        });
    }
  }, [mounted, canShare]);

  // Function to get hotel ID from context, URL params, or session storage
  const getHotelId = useCallback((): string => {
    console.log('Getting hotel ID...');
    console.log('selectedHotelId from context:', selectedHotelId);
    console.log('searchParams:', searchParams);

    // First check context
    if (selectedHotelId) {
      console.log('Using hotel ID from context:', selectedHotelId);
      return selectedHotelId;
    }

    // Then check URL parameters
    const urlHotelId = searchParams?.get('hotelId');
    console.log('URL hotel ID:', urlHotelId);
    if (urlHotelId) {
      console.log('Using hotel ID from URL:', urlHotelId);
      return urlHotelId;
    }

    // Finally check session storage
    if (typeof window !== 'undefined') {
      const selectedHotel = sessionStorage.getItem('selectedHotel');
      console.log('Session storage selectedHotel:', selectedHotel);
      if (selectedHotel) {
        try {
          const hotelData = JSON.parse(selectedHotel);
          console.log('Parsed hotel data from session storage:', hotelData);
          if (hotelData.hotelId) {
            console.log('Using hotel ID from session storage:', hotelData.hotelId);
            return hotelData.hotelId.toString();
          }
        } catch (error) {
          console.error('Error parsing selectedHotel from session storage:', error);
        }
      }
    }

    // Fallback hotel ID if none found
    console.log('No hotel ID found in context, URL params, or session storage. Using fallback ID: 70482250');
    return '70482250';
  }, [selectedHotelId, searchParams]);

  const fetchHotelDetails = useCallback(async () => {
    const hotelId = getHotelId();

    console.log('Fetching hotel details for hotel ID:', hotelId);

    try {
      setIsLoading(true);
      const response = await getHotelDetails(hotelId);
      console.log('Hotel details API response:', response);
      setHotelDetailsResponse(response.data.hotelDetailResponse);

      // Update context with the hotel ID if it wasn't already set
      if (!selectedHotelId) {
        setSelectedHotelId(hotelId);
      }

      // Call rooms and rates API if searchKey is available
      if (searchKey) {
        console.log('🏨 Calling rooms and rates API...');
        try {
          const roomsAndRatesResponse = await getRoomsAndRates(searchKey, hotelId);
          console.log('✅ Rooms and rates API response:', roomsAndRatesResponse);
        } catch (roomsError) {
          console.error('❌ Error calling rooms and rates API:', roomsError);
        }
      } else {
        console.log('⚠️ No searchKey available, skipping rooms and rates API call');

        // Try to get searchKey from localStorage
        const storedSearchKey = localStorage.getItem('searchKey');
        if (storedSearchKey) {
          console.log('🔄 Found searchKey in localStorage, calling rooms and rates API...');
          try {
            const roomsAndRatesResponse = await getRoomsAndRates(storedSearchKey, hotelId);
            console.log('✅ Rooms and rates API response:', roomsAndRatesResponse);
          } catch (roomsError) {
            console.error('❌ Error calling rooms and rates API with stored searchKey:', roomsError);
          }
        } else {
          console.log('❌ No searchKey found in localStorage either');
        }
      }
    } catch (err) {
      console.error('Error fetching hotel details:', err);
      // Fallback to mock data if API fails
      try {
        console.log('Falling back to mock data...');
        const fallbackResponse = await axios.get(API_URL);
        setHotelDetailsResponse(fallbackResponse.data.data.hotelDetailResponse);
      } catch (fallbackErr) {
        console.error('Fallback API also failed:', fallbackErr);
      }
    } finally {
      setIsLoading(false);
    }
  }, [getHotelId, setHotelDetailsResponse, setIsLoading, selectedHotelId, setSelectedHotelId]);

  // Separate useEffect for API call that runs immediately
  useEffect(() => {
    console.log('HotelDetail component mounted, calling fetchHotelDetails...');
    fetchHotelDetails();
  }, [fetchHotelDetails]);

  // Keep the original mounted effect for UI state
  useEffect(() => {
    console.log('Setting mounted state to true');
    setMounted(true);
  }, []);

  const handleReserveButtonClick = () => {
    if (!mounted) return; // Don't execute before client hydration
    
    if (isMobile) {
      router.push("/RoomSelection");
    } else {
      handleReserveClick();
    }
  };

  // Show loading state during hydration to prevent mismatch
  if (!mounted) {
    return (
      <div className="hotel-detail-container">
        <div className="overview-section common-container">
              <ShimmerInfoHeader />
          <ShimmerImageGallery />
    
        </div>
      </div>
    );
  }
   const handleBookingClick = () => {
     router.push("/RoomSelection");
   }

  return (
    <div className="hotel-detail-container">

         {/* <div className="booking-section-bottom-0">
                <div className="totalAmtLabelValue">
                  <p className="value">₹1,32,868.<span className="xs-text">
                  70
                  </span></p>

                  <span className="label">Total amount</span>
                </div>
                <button  onClick={handleBookingClick} className="bookingBtn">  <i className="fa-solid fa-share-from-square"></i> Reserve</button>
              </div> */}

      {/* <div className="search-bar-container common-container">

          {" "}
          <HotelSearchBar />{" "}

      </div> */}

      {isShimmerLoading ? (
        <div className="overview-section common-container">
          <ShimmerImageGallery />
          <ShimmerInfoHeader />
        </div>
      ) : (
        <>
          <div
            className="overview-section common-container"
            ref={parentOverviewRef}
          >
            {/* <ImageGallery newImageInfo={hotelDetailsResponse?.newImageInfo} images={hotelDetailsResponse?.carouselImages ?? []} /> */}

            {/* <div className="hotel-info-header ">
              <div className="info-header">
                <div className="heading">
                  <h2>{hotelDetailsResponse?.name || "..."}</h2>
                  <div className="rating">
                    {
                      Array.from({length: hotelDetailsResponse?.starRating ?? 0}).map((_, index) => (
                           <i key={index} className="fa-solid fa-star"></i>
                      ))
                    }
              
                
                 
                  </div>
                </div>

                <div className="buttons">
                  <div className="hotel-info-header-btn" onClick={handlaSave}>
                    {t("hotel.detail.save")}{" "}
                    <i className="fa-solid fa-heart"></i>
                  </div>

                  <div
                    className="hotel-info-header-btn"
                    onClick={() =>
                      shareHotel({
                        name: "Santana Beach Resort",
                        url: "http://localhost:3000/HotelDetail",
                      })
                    }
                  >
                    {t("hotel.detail.share")}
                    <i className="fa-solid fa-share-nodes"></i>
                  </div>

                  <div
                    className="hotel-info-header-reserveBtn"
                    onClick={handleReserveButtonClick}
                    style={{ marginLeft: "10px" }}
                  >
                    {t("hotel.detail.reserve")}
                    <i className="fa-solid fa-share-from-square"></i>
                  </div>
                </div>
              </div>

              <div className="review-location">
                <div className="review">
                  <div className="rating">{hotelDetailsResponse?.ratingView?.averageRating}</div>

                  <div className="rating-detail">
                    <p className="detail detail1">
                      {hotelDetailsResponse?.userRatingCategory}
                    </p>
                    <p className="detail detail2">
                      {hotelDetailsResponse?.ratingView?.ratingCount} {t("hotel.detail.ratings")}
                     </p>
                  </div>
                </div>
                <div className="location">
                  <div className="icon">
                    <i className="fa-solid fa-location-dot"></i>
                  </div>
                  <div className="details">
                    <div className="detail detail1">
                      {hotelDetailsResponse?.address}
                    </div>

                    <Link href={""}>
                      {" "}
                      <div className="detail detail2">
                        {t("hotel.detail.viewOnMap")}
                      </div>
                    </Link>
                  </div>
                </div>
                <DateRangeDisplay 
                  checkInDate={hotelSearchFormData?.checkInDate ?? ''} checkOutDate={hotelSearchFormData?.checkOutDate ?? ''} />
              </div>
            </div> */}
          </div>
          

          <div className="hotel-room-selection-section">
            <RoomBookingComponent
              ref={roomBookingRef}
              setIsMobileFacilitiesActive={setIsMobileFacilitiesActive}
              // scrollToParentOverview={scrollToOverview}
              // parentOverviewRef={parentOverviewRef}
              images={hotelDetailsResponse?.carouselImages ?? []}
            />
          </div>
        </>
      )}

      <div className={`detail-page-overlay ${isMobileFacilitiesActive ? 'show' : ''}`}>

      </div>

      <div
        className={`mobile-bottom-to-top-modal-container ${
          isMobileFacilitiesActive ? "active" : ""
        }`}
      >
        <div className="mobile-bottom-to-top-modal-header">
          <div
            className="mobile-bottom-to-top-modal-header__close-bttn"
            onClick={() => {
              setIsMobileFacilitiesActive(false);
            }}
          >
            <i className="fa-solid fa-xmark"></i>
          </div>
              <h3 className="mobile-bottom-to-top-modal-heading">Hotel Facilities:</h3>
        </div>

        <div className="mobile-bottom-to-top-modal-content">
          <div className="hotel-facilities-mobile-container common-container">
            <HotelFacilities amenityDetails={hotelDetailsResponse?.amenityDetails} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Page;