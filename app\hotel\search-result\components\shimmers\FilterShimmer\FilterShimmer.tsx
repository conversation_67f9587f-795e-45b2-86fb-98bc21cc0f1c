"use client";
import React from "react";
import "./FilterShimmer.scss";

function FilterShimmer() {
  return (
    <div className="shimmer-filter">
      <div className="shimmer-filter-head">
      <div className="shimmer-title"></div>
      <div className="shimmer-resetBtn">

      </div>
      </div>
      
      <div className="shimmer-checkbox-container">
        <div className="shimmer-checkboxTitle"></div>
        <div className="shimmer-checkbox">
          <div className="shimmer-checkbox-label"></div>
          <div className="shimmer-checkbox-count"></div>
        </div>
        <div className="shimmer-checkbox">
          <div className="shimmer-checkbox-label"></div>
          <div className="shimmer-checkbox-count"></div>
        </div>
      </div>
      <div className="shimmer-slider-container">
        <div className="shimmer-slider-title"></div>
        <div className="shimmer-slider-price"></div>
        <div className="shimmer-slider">
          <div className="shimmer-slider-shape shape1"></div>
          <div className="shimmer-slider-shape shape2"></div>
        </div>
      </div>

      <div className="shimmer-counter-container">
        <div className="shimmer-counter-title"></div>

        <div className="shimmer-counter">
          <div className="shimmer-counter-label"></div>
          <div className="shimmer-counter-counter"></div>
        </div>

        <div className="shimmer-counter">
          <div className="shimmer-counter-label"></div>
          <div className="shimmer-counter-counter"></div>
        </div>
      </div>

      <div className="shimmer-checkbox-container">
        <div className="shimmer-checkboxTitle"></div>
        <div className="shimmer-checkbox">
          <div className="shimmer-checkbox-label"></div>
          <div className="shimmer-checkbox-count"></div>
        </div>
        <div className="shimmer-checkbox">
          <div className="shimmer-checkbox-label"></div>
          <div className="shimmer-checkbox-count"></div>
        </div>
      </div>

      <div className="shimmer-checkbox-container">
        <div className="shimmer-checkboxTitle"></div>
        <div className="shimmer-checkbox">
          <div className="shimmer-checkbox-label"></div>
          <div className="shimmer-checkbox-count"></div>
        </div>
        <div className="shimmer-checkbox">
          <div className="shimmer-checkbox-label"></div>
          <div className="shimmer-checkbox-count"></div>
        </div>
      </div>
    </div>
  );
}

export default FilterShimmer;
