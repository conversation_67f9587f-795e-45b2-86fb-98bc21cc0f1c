import type { Metadata } from "next";
import "../styles/globals.scss"
import "../styles/variable.css"
import { CommonProvider } from "./contexts/commonContext";
import { AlertProvider } from "./components/utilities/Alert/Alert";
import { LanguageProvider } from "./contexts/languageContext";
import Header from './components/header/Header'
import Loader from "./components/Loader/Loader";
import LanguageLayoutWrapper from "./components/LanguageLayoutWrapper";
import { ToastContainers } from "./components/utilities/SonnerToasterCustom";


export const metadata: Metadata = {
  title: "KindAli Travel & Tourism",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
     <head>
     <meta name="theme-color" content="#000000" />
     <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
     <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"></link>
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossOrigin="anonymous" referrerPolicy="no-referrer" />
     <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&display=swap" rel="stylesheet" />
     </head>
      <body id="__next">
      <ToastContainers />
        <CommonProvider>
          <LanguageProvider>
            <LanguageLayoutWrapper>
              <Header />
              <Loader />
              <AlertProvider>
                {children}
              </AlertProvider>
            </LanguageLayoutWrapper>
          </LanguageProvider>
        </CommonProvider>
      </body>
    </html>
  );
}
