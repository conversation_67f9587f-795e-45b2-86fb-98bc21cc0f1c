@use "/styles/variable" as *;

.fare-summary-container {
  width: 100%;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;

  @media (max-width: $breakpoint-md) {
    border-radius: 0%;
    
  }

  &__header {
    font-size: 20px;
    color: $booking_black;
    margin-bottom: 10px;
    font-weight: 700;
  }

  &__label-price {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;

    .label,
    .price {
      font-size: 16px;
      font-weight: 500;
      color: $booking_black;
    }

    .label {
      span {
        .fa-circle-exclamation {
          font-size: 10px;
        }
      }

      &.discounts {
        color: #238c46;
      }

      &.netAmt {
        font-size: 20px;
        font-weight: 600;
      }
    }

    .price {
      .sub {
        font-size: 12px;
      }

      &.netAmt {
        font-size: 20px;
        font-weight: 600;
      }
    }
  }
}
