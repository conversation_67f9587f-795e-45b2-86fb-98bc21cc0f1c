// constants/zIndex.ts
export const Z_INDEX = {
  hide: -1,
  auto: 0,
  base: 1,
  header: 50,
  dropdown: 100,
  sticky: 200,
  overlay: 300,
  modal: 400,
  popover: 500,
  toast: 600,
  tooltip: 700,
} as const;

// Export type for TypeScript safety
export type ZIndexValue = typeof Z_INDEX[keyof typeof Z_INDEX];
export type ZIndexKey = keyof typeof Z_INDEX;

// Utility function (optional)
export const getZIndex = (key: ZIndexKey): ZIndexValue => Z_INDEX[key];