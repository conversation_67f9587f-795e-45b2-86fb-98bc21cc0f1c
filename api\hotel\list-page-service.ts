import { HotelSearchData, SearchApiRequest, SearchApiResponse } from "@/models/hotel/search-page.model"
import { SearchInitApiResponse } from "@/app/HotelSearchResult/hotel-search-result.model"
import apiService from "../api-service"

export const searchInitApi = async (body:HotelSearchData, skip: number = 0, limit: number = 1) : Promise<SearchInitApiResponse> => {
    const queryParams = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString()
    });

    return apiService.post(`search/init?${queryParams.toString()}`, body)
}

// Updated search API with skip parameter and polling until isCompleted
export const searchApi = async (body: SearchApiRequest, skip: number = 0): Promise<SearchApiResponse> => {
    const queryParams = new URLSearchParams({
        skip: skip.toString()
    });

    return apiService.post(`search?${queryParams.toString()}`, body)
}

// New function to poll search API until all batches are completed
export const pollSearchApiUntilComplete = async (searchKey: string): Promise<SearchApiResponse | null> => {
    const initialDelay = 1000; // Wait 1 second before starting polling
    const pollingInterval = 2000; // 2 seconds between attempts
    let currentSkip = 0;
    let allBatches: any[] = [];
    let isLastBatch = false;

    console.log(`🔄 Starting search API polling:`);
    console.log(`   - Will poll until is_last_batch: true`);
    console.log(`   - Initial delay: ${initialDelay / 1000}s`);
    console.log(`   - Polling interval: ${pollingInterval / 1000}s`);
    console.log(`   - Always using limit: 1`);

    // Wait before starting the polling
    await new Promise(resolve => setTimeout(resolve, initialDelay));

    // Continue polling until is_last_batch is true
    while (!isLastBatch) {
        try {
            console.log(`🔄 Polling - search_key: ${searchKey}, skip: ${currentSkip}, isLastBatch: ${isLastBatch}`);

            const searchResponse = await searchApi({ search_key: searchKey }, currentSkip);
            console.log(`📥 searchApi response for skip ${currentSkip}:`, searchResponse);

            if (searchResponse && searchResponse.batches && searchResponse.batches.length > 0) {
                // Add new batches to our collection
                allBatches.push(...searchResponse.batches);

                // Check the latest batch for is_last_batch
                const latestBatch = searchResponse.batches[searchResponse.batches.length - 1];
                if (latestBatch && latestBatch.batch_info) {
                    isLastBatch = latestBatch.batch_info.is_last_batch;
                    console.log(`📊 Batch info - is_last_batch: ${isLastBatch}, batch_number: ${latestBatch.batch_info.batch_number}`);

                    // IMMEDIATELY break the loop if this is the last batch
                    if (isLastBatch) {
                        console.log(`🎯 LAST BATCH DETECTED! Stopping polling immediately.`);
                        console.log(`✅ Final result: Total batches collected: ${allBatches.length}`);

                        // Return the final response with all batches
                        return {
                            ...searchResponse,
                            batches: allBatches,
                            isCompleted: true
                        };
                    }
                }

                // Only continue if NOT the last batch
                currentSkip++;
                console.log(`⏳ Not the last batch yet. Incrementing skip to ${currentSkip}`);
                console.log(`⏰ Waiting ${pollingInterval / 1000} seconds before next attempt...`);
                console.log(`🔄 Will continue polling for more batches...`);
                await new Promise(resolve => setTimeout(resolve, pollingInterval));

            } else {
                console.log(`⚠️ No batches received for skip ${currentSkip}, retrying...`);
                await new Promise(resolve => setTimeout(resolve, pollingInterval));
            }

        } catch (error) {
            console.error(`❌ Error on searchApi for skip ${currentSkip}:`, error);
            console.log(`🔄 Retrying after error. Waiting ${pollingInterval / 1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, pollingInterval));
        }
    }

    // This should only be reached if isLastBatch becomes true through some other means
    console.log(`🏁 Polling loop ended. Final state - isLastBatch: ${isLastBatch}, total batches: ${allBatches.length}`);

    return null;
}

// New function to poll search API with batch-by-batch callbacks
export const pollSearchApiWithBatchUpdates = async (
    searchKey: string,
    onBatchReceived: (batchData: any[], isFirstBatch: boolean, isLastBatch: boolean) => void
): Promise<SearchApiResponse | null> => {
    const initialDelay = 1000; // Wait 1 second before starting polling
    const pollingInterval = 2000; // 2 seconds between attempts
    let currentSkip = 0;
    let allBatches: any[] = [];
    let isLastBatch = false;
    let isFirstBatch = true;

    console.log(`🔄 Starting search API polling with batch updates:`);
    console.log(`   - Will poll until is_last_batch: true`);
    console.log(`   - Will call onBatchReceived for each batch`);
    console.log(`   - Initial delay: ${initialDelay / 1000}s`);
    console.log(`   - Polling interval: ${pollingInterval / 1000}s`);

    // Wait before starting the polling
    await new Promise(resolve => setTimeout(resolve, initialDelay));

    // Continue polling until is_last_batch is true
    while (!isLastBatch) {
        try {
            console.log(`🔄 Polling - search_key: ${searchKey}, skip: ${currentSkip}, isLastBatch: ${isLastBatch}`);

            const searchResponse = await searchApi({ search_key: searchKey }, currentSkip);
            console.log(`📥 searchApi response for skip ${currentSkip}:`, searchResponse);

            if (searchResponse && searchResponse.batches && searchResponse.batches.length > 0) {
                // Add new batches to our collection
                allBatches.push(...searchResponse.batches);

                // Check the latest batch for is_last_batch
                const latestBatch = searchResponse.batches[searchResponse.batches.length - 1];
                if (latestBatch && latestBatch.batch_info) {
                    isLastBatch = latestBatch.batch_info.is_last_batch;
                    console.log(`📊 Batch info - is_last_batch: ${isLastBatch}, batch_number: ${latestBatch.batch_info.batch_number}`);

                    // Call the callback with the new batch data
                    console.log(`📤 Calling onBatchReceived with ${searchResponse.batches.length} batches, isFirstBatch: ${isFirstBatch}, isLastBatch: ${isLastBatch}`);
                    onBatchReceived(searchResponse.batches, isFirstBatch, isLastBatch);
                    isFirstBatch = false; // Mark that we've processed the first batch

                    // IMMEDIATELY break the loop if this is the last batch
                    if (isLastBatch) {
                        console.log(`🎯 LAST BATCH DETECTED! Stopping polling immediately.`);
                        console.log(`✅ Final result: Total batches collected: ${allBatches.length}`);

                        // Return the final response with all batches
                        return {
                            ...searchResponse,
                            batches: allBatches,
                            isCompleted: true
                        };
                    }
                }

                // Only continue if NOT the last batch
                currentSkip++;
                console.log(`⏳ Not the last batch yet. Incrementing skip to ${currentSkip}`);
                console.log(`⏰ Waiting ${pollingInterval / 1000} seconds before next attempt...`);
                console.log(`🔄 Will continue polling for more batches...`);
                await new Promise(resolve => setTimeout(resolve, pollingInterval));

            } else {
                console.log(`⚠️ No batches received for skip ${currentSkip}, retrying...`);
                await new Promise(resolve => setTimeout(resolve, pollingInterval));
            }

        } catch (error) {
            console.error(`❌ Error on searchApi for skip ${currentSkip}:`, error);
            console.log(`🔄 Retrying after error. Waiting ${pollingInterval / 1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, pollingInterval));
        }
    }

    // This should only be reached if isLastBatch becomes true through some other means
    console.log(`🏁 Polling loop ended. Final state - isLastBatch: ${isLastBatch}, total batches: ${allBatches.length}`);

    return null;
}

