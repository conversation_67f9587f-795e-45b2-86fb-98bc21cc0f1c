"use client";
import React from 'react';
import Image from 'next/image';
import { useTranslation } from '@/app/hooks/useTranslation';
import { useLanguage } from '@/app/contexts/languageContext';

export interface ItineraryCardProps {
  data: ItineraryData;
}

export interface ItineraryData {
  bookingId: string;
  hotelName: string;
  hotelAddress: string;
  hotelRating: number;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  roomType: string;
  guestCount: {
    rooms: number;
    adults: number;
  };
  primaryGuest: {
    title: string;
    name: string;
    email: string;
    phone: string;
  };
  priceDetails: {
    roomRate: number;
    nights: number;
    taxesAndFees: number;
    resortFee: number;
  };
  paymentInfo: {
    isPaid: boolean;
    cardType: string;
    cardLastDigits: string;
  };
  cancellationDeadline: string;
  hotelImage?: string;
}

const ItineraryCard: React.FC<ItineraryCardProps> = ({ data }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Calculate total amount
  const totalAmount =
    data.priceDetails.roomRate +
    data.priceDetails.taxesAndFees +
    data.priceDetails.resortFee;

  // Default hotel image if not provided
  const hotelImageSrc = data.hotelImage || "https://via.placeholder.com/100";

  return (
    <div className="bg-white rounded-md shadow p-5 mb-6 hover:shadow-md transition-all duration-300">
      {/* Header with Booking Info and Action Buttons */}
      <div className="flex justify-between items-start mb-5">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            {t('itinerary.bookingConfirmation')}
          </h2>
          <p className="text-base text-gray-600">
            {t('itinerary.bookingId')}: <span className="font-medium">{data.bookingId}</span>
          </p>
          <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-green-50 to-green-100 text-green-700 text-sm">
            <i className={`fas fa-check-circle ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
            {t('itinerary.confirmed')}
          </div>
        </div>
        <div className="text-right space-y-2">
  <button className="w-full md:w-auto px-3 py-1.5 text-base text-gray-600 hover:text-gray-800 flex items-center justify-end hover:bg-gray-50 rounded transition-all duration-300">
    <i className={`fas fa-print ${isRTL ? 'ml-0 md:ml-2' : 'mr-0 md:mr-2'}`}></i>
    <span className="hidden sm:inline">{t('itinerary.print')}</span>
  </button>
  <button className="w-full md:w-auto px-3 py-1.5 text-base text-gray-600 hover:text-gray-800 flex items-center justify-end hover:bg-gray-50 rounded transition-all duration-300">
    <i className={`fas fa-download ${isRTL ? 'ml-0 md:ml-2' : 'mr-0 md:mr-2'}`}></i>
    <span className="hidden sm:inline">{t('itinerary.downloadPdf')}</span>
  </button>
</div>

      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        {/* Left Column (2/3 width on medium screens and up) */}
        <div className="md:col-span-2">
          {/* Hotel Information */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <div className="flex items-start">
              <Image
                width={80}
                height={80}
                src={hotelImageSrc}
                alt={data.hotelName}
                className="w-20 h-20 rounded object-cover mr-4"
              />
              <div>
                <h3 className="text-lg font-bold text-gray-800 mb-1">
                  {data.hotelName}
                </h3>
                <p className="text-sm text-gray-600 mb-1.5 flex items-center">
                  <i style={{ color: "var(--primary-color)" }} className="fas fa-map-marker-alt mr-1.5"></i>
                  {data.hotelAddress}
                </p>
                <div className="flex items-center text-sm">
                  <div className="flex items-center text-yellow-400 mr-1.5">
                    {Array.from({ length: Math.max(0, data.hotelRating || 0) }).map((_, i) => (
                      <i key={i} className="fas fa-star"></i>
                    ))}
                  </div>
                  <span className="text-gray-600">{data.hotelRating}{t('itinerary.starHotel')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Stay Details */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.stayDetails')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <p className="text-sm text-gray-600 mb-1">{t('itinerary.checkIn')}</p>
                <div className="flex items-center">
                  <i style={{ color: "var(--primary-color)" }} className={`far fa-calendar-alt ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                  <span className="text-sm font-medium">{data.checkInDate}</span>
                  <span className={`text-sm text-gray-500 ${isRTL ? 'mr-1.5' : 'ml-1.5'}`}>
                    ({data.checkInTime})
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-1">{t('itinerary.checkOut')}</p>
                <div className="flex items-center">
                  <i style={{ color: "var(--primary-color)" }} className={`far fa-calendar-alt ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                  <span className="text-sm font-medium">{data.checkOutDate}</span>
                  <span className={`text-sm text-gray-500 ${isRTL ? 'mr-1.5' : 'ml-1.5'}`}>
                    ({data.checkOutTime})
                  </span>
                </div>
              </div>
            </div>
            <div className="mt-3">
              <p className="text-sm text-gray-600 mb-1">{t('itinerary.roomType')}</p>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-bed ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                <span className="text-sm font-medium">
                  {data.roomType}
                </span>
                <span className={`text-sm text-gray-500 ${isRTL ? 'mr-1.5' : 'ml-1.5'}`}>
                  ({data.guestCount.rooms} {t(data.guestCount.rooms === 1 ? 'itinerary.room' : 'itinerary.rooms')},
                  {data.guestCount.adults} {t(data.guestCount.adults === 1 ? 'itinerary.adult' : 'itinerary.adults')})
                </span>
              </div>
            </div>
          </div>

          {/* Guest Details */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.guestDetails')}
            </h4>
            <div className="space-y-2">
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-user ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <div>
                  <p className="text-sm font-medium">{t('itinerary.primaryGuest')}</p>
                  <p className="text-sm text-gray-600">{data.primaryGuest.title} {data.primaryGuest.name}</p>
                </div>
              </div>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-envelope ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <p className="text-sm text-gray-600">{data.primaryGuest.email}</p>
              </div>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-phone ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <p className="text-sm text-gray-600">{data.primaryGuest.phone}</p>
              </div>
            </div>
          </div>

          {/* Included Amenities */}
          <div className="bg-gray-50 rounded-md p-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.includedAmenities')}
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-wifi ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <span className="text-sm text-gray-600">{t('itinerary.freeWifi')}</span>
              </div>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-utensils ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <span className="text-sm text-gray-600">
                  {t('itinerary.breakfastIncluded')}
                </span>
              </div>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-swimming-pool ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <span className="text-sm text-gray-600">{t('itinerary.poolAccess')}</span>
              </div>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-parking ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <span className="text-sm text-gray-600">{t('itinerary.freeParking')}</span>
              </div>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-spa ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <span className="text-sm text-gray-600">{t('itinerary.spaAccess')}</span>
              </div>
              <div className="flex items-center">
                <i style={{ color: "var(--primary-color)" }} className={`fas fa-dumbbell ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
                <span className="text-sm text-gray-600">{t('itinerary.fitnessCenter')}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column (1/3 width on medium screens and up) */}
        <div className="md:col-span-1">
          {/* Price Details */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.priceDetails')}
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">
                  {t('itinerary.roomRate')} ({data.priceDetails.nights} {t('itinerary.nights')})
                </span>
                <span className="text-sm font-medium">${data.priceDetails.roomRate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">{t('itinerary.taxesAndFees')}</span>
                <span className="text-sm font-medium">${data.priceDetails.taxesAndFees}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">{t('itinerary.resortFee')}</span>
                <span className="text-sm font-medium">${data.priceDetails.resortFee}</span>
              </div>
              <div className="pt-2 border-t border-gray-200 flex justify-between">
                <span className="text-sm font-bold">{t('itinerary.totalAmount')}</span>
                <span style={{ color: "var(--primary-color)" }} className="text-sm font-bold">${totalAmount}</span>
              </div>
            </div>
          </div>

          {/* Payment Status */}
          <div className="bg-gray-50 rounded-md p-4 mb-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.paymentStatus')}
            </h4>
            <div className="space-y-2">
              <div className="flex items-center text-green-600">
                <i className={`fas fa-check-circle ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                <span className="text-sm font-medium">
                  {data.paymentInfo.isPaid ? t('itinerary.paidInFull') : t('common.paymentPending')}
                </span>
              </div>
              {data.paymentInfo.isPaid && (
                <div className="text-gray-600">
                  <p className="text-sm mb-1 font-medium">{t('itinerary.paymentMethod')}</p>
                  <div className="flex items-center">
                    <i className={`fab fa-cc-${data.paymentInfo.cardType.toLowerCase()} ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}></i>
                    <span className="text-sm">{data.paymentInfo.cardType} {t('itinerary.cardEnding')} {data.paymentInfo.cardLastDigits}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Cancellation Policy */}
          <div className="bg-gray-50 rounded-md p-4">
            <h4 className="text-base font-bold text-gray-800 mb-3">
              {t('itinerary.cancellationPolicy')}
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              {t('itinerary.freeCancellation')} {data.cancellationDeadline} (48 hours before
              check-in). {t('itinerary.cancellationFee')}
            </p>
            <button className="mt-2 w-full bg-white border border-red-500 text-red-500 hover:bg-red-50 px-4 py-1.5 rounded text-sm transition-colors">
              {t('itinerary.cancelBooking')}
            </button>
          </div>
        </div>
      </div>

      {/* Need Assistance Section */}
      <div className="mt-5 bg-blue-50 rounded-md p-4">
        <div className="flex items-start">
          <i className={`fas fa-info-circle text-blue-500 mt-0.5 ${isRTL ? 'ml-2.5' : 'mr-2.5'}`}></i>
          <div>
            <h4 className="text-base font-bold text-gray-800 mb-2">
              {t('itinerary.needAssistance')}
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              {t('itinerary.supportMessage')}
            </p>
            <button className="bg-white text-blue-500 hover:bg-blue-500 hover:text-white px-4 py-1.5 rounded text-sm transition-colors">
              {t('itinerary.contactSupport')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItineraryCard;
