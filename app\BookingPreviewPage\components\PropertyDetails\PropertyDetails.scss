@use "/styles/variable" as *;
@use "sass:color";

.property-details-container {
  width: 100%;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  row-gap: 20px;

  @media (max-width: $breakpoint-md) {
    border-radius: 0%;
    padding: 0;
    row-gap: 10px;
    background-color: unset;
    
  }

  .propery-header {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
    padding-bottom: 20px;
    background-color: #ffffff;

    @media (max-width: $breakpoint-md) {
      padding: 20px;
      background-color: #ffffff;
      
      
    }

    .property-info {
      display: flex;
      flex-direction: row;
      column-gap: 15px;
      align-items: center;

      @media (max-width: $breakpoint-sm) {
        align-items: start;
        
      }

      

      &__image {
        width: 100px;
        height: 100px;
        border-radius: 10px;
        position: relative;
        overflow: hidden;
        background: rgba(0, 0, 0, 0.2);

        img {
          object-fit: cover;
        }
      }

      &__info{
        width: calc(100% - 115px);
      }

      &__title-star-rating {
        display: flex;
        flex-direction: row;
        column-gap: 5px;
        align-items: center;
        margin-bottom: 5px;

        @media (max-width: $breakpoint-sm) {
          flex-direction: column;
          align-items: start;
   
          
        }

        .title {
          color: $booking_black;
          font-size: 20px;
          font-weight: 700;
        }

        .star-rating {
          .fa-star {
            font-size: 11px;
            color: $rating_color;
            margin-right: 2px;

            @media (max-width: $breakpoint-sm) {
              font-size: 8px;
            }
          }
        }
      }

      &__location {
        font-size: 16px;
        color: #5e616e;
        font-weight: 500;
        margin-bottom: 15px;

        @media (max-width: $breakpoint-sm) {
          font-size: 13px;
          margin-bottom: 0;
        }
      }

      &__room-info {
        display: flex;
        flex-direction: row;
        gap: 20px;
        align-items: center;
        font-size: 16px;
        color: $booking_black;

        @media (max-width: $breakpoint-md) {
          display: none;
          
        }

        .info {
          display: flex;
          flex-direction: row;
          align-items: center;

          .fa-solid {
            font-size: 15px;
            margin-right: 5px;
          }
        }
      }
    }

    .checkin-checkout-container {
      max-width: 400px;
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      @media (max-width: $breakpoint-md) {
        display: none;
        
      }



      .checkin-out-section {
        .date-label {
          font-size: 12px;
          color: #5e616e;
          margin-bottom: 5px;
          font-weight: 500;
        }

        .date {
          font-size: 20px;
          color: $booking_black;
          font-weight: 600;
        }

        .time {
          font-size: 12px;
          color: #5e616e;
          font-weight: 400;
        }
      }

      .day-count {
        background-color: #fdf1ed;
        padding: 0 4px;
        border: 1px solid #f8d7cb;
        border-radius: 9999px;

        span {
          font-size: 14px;
          padding: 0 5px;
          color: #ec5b24;
        }
      }
    }
  }

  .room-details-container {
    background-color: #ffffff;

    @media (max-width: $breakpoint-md) {
      padding: 20px;
    }

    .checkin-checkout-container {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;


      padding-bottom: 15px;

   

      .checkin-out-section {
        .date-label {
          font-size: 12px;
          color: #5e616e;
          margin-bottom: 5px;
          font-weight: 500;

          .fa-solid {
            margin-right: 3px;
          }
        }

        .date {
          font-size: 16px;
          color: $booking_black;
          font-weight: 600;
        }

        .time {
          font-size: 12px;
          color: #5e616e;
          font-weight: 400;
        }
      }

      .day-count {
        background-color: #fdf1ed;
        padding: 0 4px;
        border: 1px solid #f8d7cb;
        border-radius: 9999px;

        span {
          font-size: 14px;
          padding: 0 5px;
          color: #ec5b24;
        }
      }
    }

    .guest-room-info{
      padding-top: 15px;
      .icon-label {
        font-size: 12px;
        color: #5e616e;
        margin-bottom: 5px;
        font-weight: 500;
        margin-bottom: 5px;

        .fa-solid {
          margin-right: 3px;
        }
      }

      .value{
        font-size: 16px;
        font-weight: 500;
        color: #17181c;
      }

    }

    .room-header {
      font-size: 20px;
      color: $booking_black;
      font-weight: 700;
      margin-bottom: 15px;
    }

    .room-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      column-gap: 15px;
      margin-bottom: 20px;

      @media (max-width: $breakpoint-md) {
        padding-top: 15px;
        
      }

      @media (max-width: $breakpoint-sm) {
        align-items: start;
        
      }

      &__image {
        width: 80px;
        height: 80px;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
        background: rgba(0, 0, 0, 0.2);

        img {
          object-fit: cover;
        }
      }

      &__details {
        width: calc(100% - 95px);
        .room-name {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 1px;
        }

        .room-details {
          display: flex;
          flex-direction: row;
          column-gap: 10px;
          align-items: center;

          .detail-xs {
            color: $booking_black;
            display: flex;
            flex-direction: row;
            align-items: center;
            .label {
              font-size: 12px;
              font-weight: 500;
            }
          }

          .detail {
            color: $booking_black;
            display: flex;
            flex-direction: row;
            align-items: center;

            @media (max-width: $breakpoint-sm) {
              display: none;
              
            }

            .fa-solid {
              font-size: 15px;
              margin-right: 4px;

              @media (max-width:$breakpoint-md) {
                display: none;
              }
            }

            .material-icons {
              font-size: 18px;
              margin-right: 4px;
              @media (max-width:$breakpoint-md) {
                display: none;
              }
            }

            .label {
              font-size: 12px;
              font-weight: 500;
            }
          }
        }

        .view-benefits-btn{
          margin-top: 15px;
          font-size: 16px;
          font-weight: 600;
          color: $primary-color;
          cursor: pointer;
          transition: color 0.2s ease;

          &:hover{
            color: color.adjust($primary-color, $lightness: -5%);
          }
        }
      }
    }

    .room-benefits {
      @media (max-width: $breakpoint-md) {
        display: none;
        
      }
      &__header {
        font-size: 18px;
        font-weight: bold;
        color: $booking_black;
        margin-bottom: 10px;
      }

      &__list {
        display: flex;
        flex-direction: row;
        gap: 15px;
        flex-wrap: wrap;

        .list-item {
          display: flex;
          flex-direction: row;

          align-items: center;
          column-gap: 5px;
          color: $booking_black;

          .label {
            font-size: 16px;
            font-weight: 500;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
