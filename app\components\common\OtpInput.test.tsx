import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import OtpInput from './OtpInput';

describe('OtpInput Component', () => {
  const mockOnChange = jest.fn();
  
  beforeEach(() => {
    mockOnChange.mockClear();
  });

  test('renders correct number of input fields', () => {
    render(<OtpInput length={4} value={['', '', '', '']} onChange={mockOnChange} />);
    const inputs = screen.getAllByRole('textbox');
    expect(inputs).toHaveLength(4);
  });

  test('handles input change correctly', () => {
    render(<OtpInput length={4} value={['', '', '', '']} onChange={mockOnChange} />);
    const inputs = screen.getAllByRole('textbox');
    
    fireEvent.change(inputs[0], { target: { value: '1' } });
    
    expect(mockOnChange).toHaveBeenCalledWith(['1', '', '', '']);
  });

  test('handles paste event correctly', () => {
    render(<OtpInput length={4} value={['', '', '', '']} onChange={mockOnChange} />);
    const inputs = screen.getAllByRole('textbox');
    
    const pasteEvent = {
      clipboardData: {
        getData: () => '1234'
      },
      preventDefault: jest.fn()
    };
    
    fireEvent.paste(inputs[0], pasteEvent);
    
    expect(mockOnChange).toHaveBeenCalledWith(['1', '2', '3', '4']);
    expect(pasteEvent.preventDefault).toHaveBeenCalled();
  });

  test('handles backspace correctly', () => {
    render(<OtpInput length={4} value={['1', '2', '', '']} onChange={mockOnChange} />);
    const inputs = screen.getAllByRole('textbox');
    
    // Focus on second input and press backspace when it's empty
    inputs[1].focus();
    fireEvent.keyDown(inputs[1], { key: 'Backspace' });
    
    expect(mockOnChange).toHaveBeenCalledWith(['', '2', '', '']);
  });

  test('handles arrow key navigation', () => {
    render(<OtpInput length={4} value={['1', '2', '3', '4']} onChange={mockOnChange} />);
    const inputs = screen.getAllByRole('textbox');
    
    // Focus on second input and press left arrow
    inputs[1].focus();
    fireEvent.keyDown(inputs[1], { key: 'ArrowLeft' });
    
    // Check if first input is focused
    expect(document.activeElement).toBe(inputs[0]);
    
    // Press right arrow
    fireEvent.keyDown(inputs[0], { key: 'ArrowRight' });
    
    // Check if second input is focused
    expect(document.activeElement).toBe(inputs[1]);
  });

  test('applies custom class names', () => {
    render(
      <OtpInput 
        length={4} 
        value={['', '', '', '']} 
        onChange={mockOnChange} 
        className="custom-container"
        inputClassName="custom-input"
      />
    );
    
    const container = screen.getByRole('textbox').parentElement;
    expect(container).toHaveClass('custom-container');
    
    const inputs = screen.getAllByRole('textbox');
    inputs.forEach(input => {
      expect(input).toHaveClass('custom-input');
    });
  });

  test('disables inputs when disabled prop is true', () => {
    render(<OtpInput length={4} value={['', '', '', '']} onChange={mockOnChange} disabled={true} />);
    
    const inputs = screen.getAllByRole('textbox');
    inputs.forEach(input => {
      expect(input).toBeDisabled();
    });
  });
});
