@use "/styles/variable" as *;

.rooms-container {
  border-top: 1px solid $border-color;
  border-bottom: 1px solid $border-color;
}

// Traveler selector container
.traveler-selector-container {
  border: 1px solid $border-color;
  border-radius: 8px;
  overflow: hidden;

  // Room container
  .room-container {
    padding: 16px;
    border-bottom: 1px solid $border-color;

    &:last-child {
      border-bottom: none;
    }
  }

  // Controls for incrementing/decrementing
  .traveler-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .control-buttons {
      display: flex;
      align-items: center;

      button {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid $border-color;
        background-color: white;
        border-radius: 4px;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .count {
        margin: 0 12px;
        min-width: 24px;
        text-align: center;
      }
    }
  }
}

// RTL specific styles
html[dir="rtl"] {
  .traveler-selector-container {
    .traveler-control {
      .label {
        text-align: right;
      }
    }

    // Ensure consistent borders in RTL mode
    border: 1px solid $border-color;
  }
}