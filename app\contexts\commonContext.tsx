"use client";
import React, { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { HotelSearchFormData } from "../HotelSearchResult/components/HotelSearchBar/HotelSearchBar";
import { HotelDetailResponse } from "../HotelDetail/hotel-detail-result.model";
import { HotelSearchData } from "../../models/hotel/search-page.model";

// Define the shape of the context

interface userDetails{
  first_name: string;
  second_name: string;
  email: string;
  phone: string;
}




interface CommonContextProps {
  isLoggedIn: boolean;
  setIsLoggedIn: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentPage: string;
  setCurrentPage: React.Dispatch<React.SetStateAction<string>>;
  userData: userDetails | undefined;
  setUserData: React.Dispatch<React.SetStateAction<userDetails | undefined>>;
  screenSize: number;
  setScreenSize: React.Dispatch<React.SetStateAction<number>>;
  hotelSearchFormData : HotelSearchFormData | undefined;
  setHotelSearchFormData: React.Dispatch<React.SetStateAction<HotelSearchFormData | undefined>>;
  hotelSearchData: HotelSearchData | undefined;
  setHotelSearchData: React.Dispatch<React.SetStateAction<HotelSearchData | undefined>>;
  hotelDetailsResponse : HotelDetailResponse | undefined;
  setHotelDetailsResponse : React.Dispatch<React.SetStateAction<HotelDetailResponse | undefined>>;
  selectedHotelId: string | undefined;
  setSelectedHotelId: React.Dispatch<React.SetStateAction<string | undefined>>;
  searchKey: string | undefined;
  setSearchKey: React.Dispatch<React.SetStateAction<string | undefined>>;
  isShareGroupVisible: boolean;
  setIsShareGroupVisible:  React.Dispatch<React.SetStateAction<boolean>>;
  isMobileModalOpen: boolean;
  setIsMobileModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

// Create the context
const CommonContext = createContext<CommonContextProps | undefined>(undefined);

// Provider Component
export const CommonProvider = ({ children }: { children: ReactNode }) => {
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage]  = useState('');
  const [userData,setUserData] = useState<userDetails | undefined>()
  const [screenSize, setScreenSize] = useState<number>(() => {
    if (typeof window !== "undefined") {
      return window.innerWidth;
    }
    return 1600; // Default fallback for SSR
  });
  const [hotelSearchFormData, setHotelSearchFormData] = useState<HotelSearchFormData>();
  const [hotelSearchData, setHotelSearchData] = useState<HotelSearchData>();
  const [hotelDetailsResponse, setHotelDetailsResponse] = useState<HotelDetailResponse>();
  const [selectedHotelId, setSelectedHotelId] = useState<string>();
  const [searchKey, setSearchKey] = useState<string>();
  const [isShareGroupVisible, setIsShareGroupVisible] = useState<boolean>(false);
  const [isMobileModalOpen, setIsMobileModalOpen] = useState<boolean>(false);

  // Load search data from localStorage on initialization
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        // Load hotel search form data
        const savedFormData = localStorage.getItem('hotelSearchFormData');
        if (savedFormData) {
          const parsedFormData = JSON.parse(savedFormData);
          setHotelSearchFormData(parsedFormData);
        }

        // Load hotel search API data
        const savedSearchData = localStorage.getItem('hotelSearchData');
        if (savedSearchData) {
          const parsedSearchData = JSON.parse(savedSearchData);
          setHotelSearchData(parsedSearchData);
        }

        // Load search key
        const savedSearchKey = localStorage.getItem('searchKey');
        if (savedSearchKey) {
          setSearchKey(savedSearchKey);
        }
      } catch (error) {
        console.error('Error loading search data from localStorage:', error);
      }
    }
  }, []);

  return (
    <CommonContext.Provider value={{ isLoggedIn, setIsLoggedIn, isLoading, setIsLoading, currentPage, setCurrentPage , userData , setUserData , screenSize , setScreenSize , hotelSearchFormData, setHotelSearchFormData, hotelSearchData, setHotelSearchData, hotelDetailsResponse, setHotelDetailsResponse, selectedHotelId, setSelectedHotelId, searchKey, setSearchKey, isShareGroupVisible, setIsShareGroupVisible, isMobileModalOpen, setIsMobileModalOpen }}>
      {children}
    </CommonContext.Provider>
  );
};

// Hook for easy access
export const useCommonContext = () => {
  const context = useContext(CommonContext);
  if (!context) {
    throw Error("useCommonContext must be used within a CommonProvider");
  }
  return context;
};
