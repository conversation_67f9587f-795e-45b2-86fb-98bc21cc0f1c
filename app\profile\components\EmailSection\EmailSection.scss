@use "/styles/variable" as *;

.form-field__input-buttons {
  padding: 10px 0;
  display: flex;
  gap: 10px;

  .btn {
    padding: 0.625rem 1.25rem; // 10px 20px -> rem units
    font-size: 1rem; // 16px -> rem
    font-weight: 600;
    border-radius: 3px;
    cursor: pointer;
    border: 1px solid $primary-color;
    white-space: nowrap;
    transition: background-color 0.3s ease, color 0.3s ease;

    &.saveBtn {
      background-color: $primary-color;
      color: #fff;

      &:hover {
        background-color: lighten($primary-color, 5%);
      }
    }

    &.cancelBtn {
      background-color: #fff;
      color: $primary-color;

      &:hover {
        background-color: $primary-color;
        color: #fff;
      }
    }
  }

  // Optional media queries for responsive tweaks
  @media (max-width: 768px) {
    .btn {
      padding: 0.5rem 1rem;
      font-size: 0.95rem;
    }
  }

  @media (max-width: 480px) {
    .btn {
      width: 100%;
      text-align: center;
      padding: 0.75rem;
      font-size: 0.95rem;
    }
  }
}
