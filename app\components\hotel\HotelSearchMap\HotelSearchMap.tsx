'use client';

import React, { useState, useEffect, useCallback } from 'react';
import MapWrapper from '@/components/map/MapWrapper';
import FullScreenMap from '@/components/map/FullScreenMap';
import { useTranslation } from '@/app/hooks/useTranslation';
import { useLanguage } from '@/app/contexts/languageContext';
import './HotelSearchMap.scss';
import { HotelFilterData } from '@/app/components/utilities/helpers/hotel/filterHotels';
import { addFilterInList } from '@/app/components/utilities/helpers/hotel/hotelFilterService';
import DetailMap from '@/components/map/detail-map/DetailMap';
import { Hotel } from '@/models/hotel/list-page.model';

interface HotelSearchMapProps {
  hotels?: Hotel[]
  destination?: {
    name: string;
    latitude: number;
    longitude: number;
  };
  onHotelSelect?: (hotelId: string) => void;
  actualHotels?: Hotel[];
  filterData?: HotelFilterData;
  onFilterChange?: (filterData: HotelFilterData | undefined) => void;
  // New prop to determine if we should show the detail map
  isDetail?: boolean;
  // Data needed for DetailMap
  detailData?: {
    name: string;
    rating: number;
    stars: string;
    position: [number, number];
    description: string;
    reviews: string;
    locationScore: string;
    address: string;
    attractions: Array<{
      name: string;
      distance: string;
    }>;
  };
  // Additional handlers for DetailMap
  onDetailFavoriteClick?: () => void;
  onDetailCloseClick?: () => void;
  onDetailShowPrices?: () => void;
}

// Dummy hotel data for testing - using Hotel model structure
const DUMMY_HOTELS: Hotel[] = [
  {
    specialAmenities: ["Free WiFi"],
    locationId: 1,
    hotelId: 1,
    offer: {
      couponCode: "HOTEL1",
      instantDiscount: 500,
      displayedInstantDiscount: 500,
      cashback: 100,
      displayedCashback: 100,
      applyMessage: "Discount applied automatically"
    },
    name: "Blue Lagoon Resort",
    address: "Arambol, Goa",
    city: "Goa",
    userRating: "8.5",
    userRatingCategory: "Excellent",
    starRating: 4,
    userRatingCount: 234,
    imageInfoList: [{
      url: "/images/hotel1.jpg",
      pictureId: "1",
      caption: "Blue Lagoon Resort",
      imageCategory: "hotel",
      rank: 1
    }],
    roomDetails: [{
      type: "Deluxe Room",
      bedroom: 1,
      livingRoom: 1,
      bathroom: 1,
      size: "30m²",
      bed: "1 King Bed"
    }],
    comfortRating: 4.5,
    taxesAndCharges: 500,
    category: "Hotel",
    hotelType: "Hotel",
    accommodationType: "Hotel",
    topOfferings: ["Free WiFi", "Breakfast Included"],
    roomsCountLeft: 5,
    distanceFromSearchedEntity: "2.5 km",
    fomoTags: [],
    amenities: [{
      name: "Free WiFi",
      amenityUrl: ""
    }],
    geoLocationInfo: {
      lat: 15.7,
      lon: 73.91
    },
    fareDetail: {
      displayedBaseFare: 120,
      totalPrice: 120
    },
    about: "Blue Lagoon Resort is a comfortable hotel located in Arambol, Goa.",
    isVisible: true
  }
];

// Dummy destination
const DUMMY_DESTINATION = {
  name: "Goa, India",
  latitude: 15.5,
  longitude: 73.85
};

// Default detail data if needed
const DEFAULT_DETAIL_DATA = {
  name: "Blue Lagoon Resort",
  rating: 8.7,
  stars: "★★★★",
  position: [15.7, 73.91] as [number, number],
  description: "Excellent",
  reviews: "1,234 reviews",
  locationScore: "Great location - 9.2",
  address: "Arambol Beach, Goa, India",
  attractions: [
    { name: "Arambol Beach", distance: "0.1 km" },
    { name: "Sweet Water Lake", distance: "1.5 km" },
    { name: "Mandrem Beach", distance: "3.2 km" }
  ]
};

const HotelSearchMap: React.FC<HotelSearchMapProps> = ({
  hotels = [],
  destination,
  onHotelSelect,
  actualHotels: parentActualHotels,
  filterData: parentFilterData,
  onFilterChange,
  isDetail = false,
  detailData,
  onDetailFavoriteClick,
  onDetailCloseClick,
  onDetailShowPrices
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [mapHotels, setMapHotels] = useState(hotels);
  const [mapDestination, setMapDestination] = useState(destination);
  const [actualHotels, setActualHotels] = useState<Hotel[]>(parentActualHotels || []);
  const [mapFilterData, setMapFilterData] = useState<HotelFilterData | undefined>(parentFilterData);
  const [filteredHotels, setFilteredHotels] = useState<Hotel[]>([]);
  
  // State to control maps visibility
  const [isMapOpen, setIsMapOpen] = useState(false);
  const [isDetailMapOpen, setIsDetailMapOpen] = useState(false);

  // Initialize detail data if not provided
  const [activeDetailData, setActiveDetailData] = useState(detailData || DEFAULT_DETAIL_DATA);

  // Use dummy data if no hotels are provided
  useEffect(() => {
    if (hotels.length === 0) {
      setMapHotels(DUMMY_HOTELS);
      setMapDestination(DUMMY_DESTINATION);
    } else {
      setMapHotels(hotels);
      setMapDestination(destination);
    }
  }, [hotels, destination]);



  // Update detail data when prop changes
  useEffect(() => {
    if (detailData) {
      setActiveDetailData(detailData);
    }
  }, [detailData]);

// Use actual hotels from parent if provided, otherwise use mapHotels directly
useEffect(() => {
  if (parentActualHotels && parentActualHotels.length > 0) {
    setActualHotels(parentActualHotels);
  } else if (mapHotels.length > 0) {
    // Since mapHotels is now of type Hotel[], use it directly
    setActualHotels(mapHotels);
  }
}, [parentActualHotels, mapHotels]);

  // Use filter data from parent if provided
  useEffect(() => {
    if (parentFilterData && typeof parentFilterData === 'object') {
      // Check if parentFilterData has the expected properties
      if (parentFilterData.priceRange || parentFilterData.starRatings || parentFilterData.amenities) {
        setMapFilterData(parentFilterData);
      }
    }
  }, [parentFilterData]);

  // Apply filters when filter data or actual hotels change
  useEffect(() => {
    try {
      if (mapFilterData && actualHotels.length > 0) {
        const filtered = addFilterInList(mapFilterData, actualHotels);
        setFilteredHotels(filtered);
      } else {
        setFilteredHotels(actualHotels);
      }
    } catch (error) {
      console.error('Error applying filters in HotelSearchMap:', error);
      // Fallback to unfiltered hotels
      setFilteredHotels(actualHotels);
    }
  }, [mapFilterData, actualHotels]);

  // Handle filter changes from FullScreenMap
  const handleFilterChange = useCallback((newFilterData: HotelFilterData | undefined) => {
    setMapFilterData(newFilterData);
    // Notify parent component of filter changes
    if (onFilterChange) {
      onFilterChange(newFilterData);
    }
  }, [onFilterChange]);

  // Define center location
  const center = mapDestination ? {
    latitude: mapDestination.latitude,
    longitude: mapDestination.longitude,
    address: mapDestination.name
  } : undefined;

  // Open normal map
  const openMap = () => {
    setIsMapOpen(true);
  };

  // Open detail map
  const openDetailMap = () => {
    setIsDetailMapOpen(true);
  };

  // Close maps
  const closeMap = () => {
    setIsMapOpen(false);
  };

  const closeDetailMap = () => {
    setIsDetailMapOpen(false);
    if (onDetailCloseClick) onDetailCloseClick();
  };

  // Define map center
  const mapCenter: [number, number] = center
    ? [center.latitude, center.longitude]
    : [15.5, 73.85]; // Default center (Goa)

  // Create markers for hotels
  const hotelMarkers = mapHotels.map(hotel => ({
    id: hotel.hotelId.toString(),
    position: [hotel.geoLocationInfo.lat, hotel.geoLocationInfo.lon] as [number, number],
    iconType: 'hotel' as const,
    details: {
      id: hotel.hotelId.toString(),
      name: hotel.name,
      rating: hotel.starRating,
      price: `$${hotel.fareDetail.totalPrice}`
    }
  }));

  return (
    <div className="hotel-search-map idle">
      {/* Idle state - show map background with button */}
      <div className="map-background">
        <MapWrapper
          center={center ? [center.latitude, center.longitude] : undefined}
          zoom={12}
          markers={[]}
          style={{ height: '100%', width: '100%', borderRadius: '8px' }}
          interactive={false} // Disable zoom controls and interaction
        />
        <div className="show-map-button-container">
          <button
            className="show-map-button"
            onClick={isDetail ? openDetailMap : openMap}
            aria-label={t('hotel.card.showOnMap')}
          >
            <i className={`fa-solid fa-location-dot ${isRTL ? 'ml-2' : 'mr-2'}`}></i>
            {t('hotel.card.showOnMap')}
          </button>
        </div>
      </div>

      {/* Full-screen map component */}
      {!isDetail && (
        <FullScreenMap
          isOpen={isMapOpen}
          onClose={closeMap}
          center={mapCenter}
          zoom={12}
          markers={hotelMarkers}
          title={mapDestination?.name || t('map.selectedArea')}
          hotels={filteredHotels.length > 0 ? filteredHotels : actualHotels}
          destination={center ? {
            name: center.address || '',
            latitude: center.latitude,
            longitude: center.longitude
          } : undefined}
          isHotelSearchMap={true}
          filterData={mapFilterData}
          onFilterChange={handleFilterChange}
          onHotelSelect={(hotelId) => {
            if (onHotelSelect) {
              onHotelSelect(hotelId.toString());
            }
          }}
        />
      )}

      {/* Fixed position DetailMap */}
      {isDetail && (
        <div className={`detail-map-fixed-container ${isDetailMapOpen ? 'active' : ''}`}>
          <div className="detail-map-content">
            {/* Custom close button */}
            <button 
              className="detail-map-close" 
              onClick={closeDetailMap}
              aria-label={t('common.close')}
            >
              <i className="fa-solid fa-times"></i>
            </button>
            
            {/* DetailMap component */}
            <DetailMap
              location={activeDetailData}
              style={{ height: '100%', width: '100%' }}
              onFavoriteClick={onDetailFavoriteClick}
              onCloseClick={closeDetailMap}
              onShowPrices={onDetailShowPrices}
            />
            
            {/* Optional footer with hotel information */}
            <div className="detail-map-footer">
              <div className="hotel-details">
                <h4>{activeDetailData.name}</h4>
                <p className="location">{activeDetailData.address}</p>
              </div>
              <div className="hotel-actions">
                <button onClick={onDetailFavoriteClick}>
                  <i className="fa-regular fa-heart"></i> {t('hotel.favorite')}
                </button>
                <button className="primary" onClick={onDetailShowPrices}>
                  {t('hotel.showPrices')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HotelSearchMap;