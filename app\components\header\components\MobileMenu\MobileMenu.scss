@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.slide-from-right-modal-mobile {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: z-index(modal)+1;

  opacity: 0;
  pointer-events: none;
  transition: opacity 0.4s ease; // Background fades in

  &.show {
    opacity: 1;
    pointer-events: auto; // Can be clicked
  }

  .modal-content {
    background: white;
    width: 85%;
    height: 100vh;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    overflow-y: auto;
    transform: translateX(100%); // Start off-screen (right)
    transition: transform 0.5s ease-in-out; // Slide-in effect

    &.cancellation-policy-modal{
      max-width: 440px;
      width: 100%;
    }

    .header {
      padding: 16px 24px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      background-color: $primary-color;
      color: white;

      h2 {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        letter-spacing: 0.5px;

        @media (max-width: $breakpoint-md) {
          font-size: 16px;
        }
      }

      .close-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }

        .fa-xmark {
          font-size: 16px;
          cursor: pointer;
          color: white;
        }
      }
    }
  }

  &.show .modal-content {
    transform: translateX(0); // Slides into view when modal opens
  }

  // RTL support
  &.rtl {
    .modal-content {
      right: auto;
      left: 0;
      transform: translateX(-100%); // Start off-screen (left)
    }

    &.show .modal-content {
      transform: translateX(0);
    }

    .header {
      flex-direction: row-reverse;
    }

    .content {
      text-align: right;

      ul li {
        .fa-solid {
          margin-right: 0;
          margin-left: 10px;
        }
      }
    }
  }
}
