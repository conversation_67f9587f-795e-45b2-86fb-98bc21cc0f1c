@use "/styles/variable" as *;
@use "/styles/zIndex" as *;

.fullscreen-hotel-search-map {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: z-index(popover);
  display: flex;
  flex-direction: column;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }

  .fullscreen-map__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid $border_color;
    background-color: $white_color;
    height: 60px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    z-index: z-index(base);

    h2 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: #333;
    }

    .search-container {
      position: relative;
      width: 300px;

      .search-input {
        width: 100%;
        padding: 8px 15px 8px 35px;
        border: 1px solid $border_color;
        border-radius: 4px;
        font-size: 14px;
        outline: none;

        &:focus {
          border-color: $primary_color;
        }
      }

      .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: $gray_color;
      }
    }

    .header-buttons {
      display: flex;
      gap: 10px;

      .close-btn {
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 8px 15px;
        background-color: $white_color;
        border: 1px solid $border_color;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;

        &:hover {
          background-color: $light_gray_color;
        }

        i {
          font-size: 12px;
        }
      }
    }
  }

  .fullscreen-map__content {
    display: flex;
    height: calc(100% - 60px);
    overflow: hidden;
    position: relative;

    // Hotel List Panel - Left Side
    .hotel-list-panel {
      width: 300px;
      height: 100%;
      overflow-y: auto;
      border-right: 1px solid $border_color;
      background-color: $white_color;
      flex-shrink: 0;
      position: relative;
      z-index: z-index(base);

      .hotel-list-body {
        padding: 0;

        .hotel-list-item {
          padding: 0;
          cursor: pointer;
          transition: all 0.2s ease;
          border-bottom: 1px solid $border_color;

          &:hover {
            background-color: #f9f9f9;
          }

          &.selected {
            background-color: #f0f8ff;
            border-left: 3px solid $primary_color;
          }

          // Compact Hotel Card
          .compact-hotel-card {
            padding: 10px;
            position: relative;

            // Amenity tags
            .amenity-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 5px;
              margin-bottom: 5px;

              .amenity-tag {
                font-size: 11px;
                color: $gray_color;
                background-color: #f5f5f5;
                padding: 2px 6px;
                border-radius: 3px;
                white-space: nowrap;
              }
            }

            // Discount tag
            .discount-tag {
              background-color: #e8f4f2;
              color: $primary_color;
              font-size: 11px;
              padding: 4px 8px;
              border-radius: 3px;
              margin-bottom: 5px;
            }

            // Last minute deal tag
            .last-minute-tag {
              display: inline-flex;
              align-items: center;
              gap: 5px;
              background-color: #fff0e0;
              color: #ff6b00;
              font-size: 11px;
              padding: 4px 8px;
              border-radius: 3px;
              margin-bottom: 5px;
            }

            // Hotel card content
            .hotel-card-content {
              display: flex;
              gap: 10px;

              .hotel-image {
                width: 100px;
                height: 100px;
                border-radius: 4px;
                overflow: hidden;
                flex-shrink: 0;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }

                .no-image {
                  width: 100%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: #f0f0f0;
                  color: $gray_color;
                  font-size: 12px;
                  text-align: center;
                  padding: 10px;
                }
              }

              .hotel-details {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 4px;

                .hotel-name {
                  font-size: 14px;
                  font-weight: 600;
                  color: $primary_color;
                  margin: 0;
                  line-height: 1.3;
                }

                .hotel-rating {
                  display: flex;
                  gap: 2px;

                  i {
                    color: #ffc107;
                    font-size: 12px;
                  }
                }

                .hotel-location {
                  font-size: 12px;
                  color: $gray_color;
                }

                .hotel-price {
                  margin-top: auto;
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;

                  .original-price {
                    font-size: 12px;
                    color: $gray_color;
                    text-decoration: line-through;
                  }

                  .current-price {
                    font-size: 16px;
                    font-weight: 700;
                    color: $black_color;
                  }

                  .price-details {
                    font-size: 10px;
                    color: $gray_color;
                  }
                }
              }
            }
          }
        }

        .no-hotels-found {
          padding: 30px;
          text-align: center;
          color: $gray_color;
        }
      }
    }

    // Map Panel - Center
    .map-panel {
      flex: 1;
      height: 100%;
      position: relative;
      z-index: z-index(base);

      .selected-hotel-card-container {
        position: absolute;
        bottom: 20px;
        left: 20px;
        z-index: z-index(modal);
        max-width: 350px;
        width: 100%;
      }
    }

    // Filter Panel - Right Side - Always Visible
    .filter-panel {
      width: 280px;
      height: 100%;
      // overflow-y: auto;
      border-left: 1px solid $border_color;
      background-color: $white_color;
      flex-shrink: 0;
      position: relative; /* Changed to relative to be part of the normal flow */
      z-index: z-index(base); /* Lower z-index since it's now part of the normal flow */

      .filter-panel__header {
        padding: 15px;
        border-bottom: 1px solid $border_color;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          font-size: 16px;
          font-weight: 600;
          margin: 0;
        }
      }

      .filter-panel__content {
        padding: 0;
        height: calc(100% - 50px);
        overflow-y: auto;

        // Override the hotel-filter-container styles
        .hotel-filter-container {
          width: 100% !important;
          border: none !important;
          border-radius: 0 !important;
          box-shadow: none !important;
          max-height: none !important;
          position: static !important;
          overflow: visible !important;
          height: auto !important;
          background-color: $white_color !important;

          .filter-head {
            display: none !important;
          }

          .filter-body {
            max-height: none !important;
            height: auto !important;
            overflow-y: auto !important;
            padding: 10px !important;

            .type-filter {
              min-width: auto !important;
              width: 100% !important;
            }
          }
        }
      }
    }
  }

  // RTL support
  html[dir="rtl"] & {
    .fullscreen-map__header {
      .search-container {
        .search-icon {
          left: auto;
          right: 10px;
        }

        .search-input {
          padding: 8px 35px 8px 15px;
        }
      }

      .header-buttons {
        .close-btn, .filter-btn {
          flex-direction: row-reverse;
        }
      }
    }

    .fullscreen-map__content {
      .filter-panel {
        border-right: 1px solid $border_color;
        border-left: none;
      }

      .hotel-list-panel {
        border-right: none;
        border-left: 1px solid $border_color;
      }

      .selected-hotel-card-container {
        left: auto;
        right: 20px;
      }
    }
  }

  // Responsive styles
  @media (max-width: $breakpoint-lg) {
    .fullscreen-map__content {
      .filter-panel {
        width: 250px;
      }

      .hotel-list-panel {
        width: 350px;
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    .fullscreen-map__content {
      flex-direction: column;

      .filter-panel {
        width: 100%;
        height: auto;
        max-height: 300px;
        position: relative;
        border: none;
        border-top: 1px solid $border_color;
        border-bottom: 1px solid $border_color;
        z-index: z-index(base);
      }

      .hotel-list-panel {
        width: 100%;
        height: auto;
        max-height: 300px;
        border-right: none;
        border-left: none;
        border-bottom: 1px solid $border_color;
      }

      .map-panel {
        height: calc(100% - 600px); /* Adjusted to account for both hotel list and filter panels */
        min-height: 300px;
      }
    }


  }
}
