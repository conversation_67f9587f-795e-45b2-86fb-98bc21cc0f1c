"use client";
import React, { useCallback, useEffect, useState } from "react";

import { Hotel } from "../../hotel-search-result.model";
import Image from "next/image";
import { useTranslation } from "@/app/hooks/useTranslation";
import { showToast } from "@/app/components/utilities/SonnerToasterCustom";
import { useCommonContext } from "@/app/contexts/commonContext";
import {
  BadgePercent,
  Heart,
  Star,
  MapPin,
  CheckCircle,
  CreditCard,
  PawPrint,
  Car,
  Hotel as HotelIcon,
  Building2,
  Home,
  TreePine,
  Bed,
  Castle
} from "lucide-react";
import PriceShimmer from "./PriceShimmer";
import { getAmenityIcon } from "../../data/amenities";

interface HotelCardProps {
  type: string;
  hotel: Hotel;
  handleClick: () => void;
  onCheckboxChange?: (hotel: Hotel, isChecked: boolean) => void;
  isChecked?: boolean;
  isSearchCompleted?: boolean;
  isPricingAvailable?: boolean;
}

function HotelCard({
  type,
  hotel,
  handleClick,
  onCheckboxChange,
  isChecked = false,
  isSearchCompleted = false,
  isPricingAvailable = false,
}: HotelCardProps) {
  const { t } = useTranslation();
  const [isHeartActive, setIsHeartActive] = useState<boolean>(false);
  const { isShareGroupVisible } = useCommonContext();
  const [isSelected, setIsSelected] = useState<boolean>(isChecked);

  // Function to filter out duplicate amenities that are already shown in top offerings
  const getFilteredAmenities = (amenities: any[], topOfferings: string[]) => {
    if (!amenities) return [];

    // Create a set of keywords from top offerings for efficient lookup
    const offeringKeywords = new Set<string>();
    topOfferings.forEach(offering => {
      const words = offering.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (word.length > 2) { // Only meaningful words
          offeringKeywords.add(word);
        }
      });
    });

    // Amenities that should not be shown in the general amenities section
    const excludedAmenities = [
      'pets allowed', 'pet friendly', 'pets welcome',
      'free cancellation', 'cancellation', 'refundable',
      'pay at hotel', 'payment', 'guarantee',
      'best price', 'price guarantee'
    ];

    // Filter amenities to remove duplicates, excluded items, and invalid entries
    const filtered = amenities.filter(amenity => {
      const amenityName = amenity.name || amenity;

      // Skip if amenity is empty, too short, or not a string
      if (!amenityName || typeof amenityName !== 'string' || amenityName.trim().length < 3) {
        return false;
      }

      const amenityLower = amenityName.toLowerCase().trim();

      // Skip excluded amenities
      for (const excluded of excludedAmenities) {
        if (amenityLower.includes(excluded)) {
          return false;
        }
      }

      // Check if this amenity contains any keywords from top offerings
      for (const keyword of offeringKeywords) {
        if (amenityLower.includes(keyword)) {
          return false;
        }
      }

      return true;
    });

    return filtered;
  };

  // Update local state when prop changes
  useEffect(() => {
    setIsSelected(isChecked);
  }, [isChecked]);

  const handlaSave = useCallback(() => {
    if (isHeartActive) {
      setIsHeartActive(false);
      showToast("Removed from favorites", "error");
    } else {
      setIsHeartActive(true);
      showToast("Added to favorites", "default");
    }
  }, [isHeartActive]);

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newCheckedState = e.target.checked;
    setIsSelected(newCheckedState);

    // Notify parent component about the change
    if (onCheckboxChange) {
      onCheckboxChange(hotel, newCheckedState);
    }
  };

  // Function to get rating badge color based on rating value
  const getRatingBadgeColor = (rating: string | number) => {
    const numRating = typeof rating === 'string' ? parseFloat(rating) : rating;

    if (numRating >= 9.0) {
      return "bg-green-600"; // Excellent
    } else if (numRating >= 8.0) {
      return "bg-blue-600"; // Very Good
    } else if (numRating >= 7.0) {
      return "bg-orange-500"; // Good
    } else if (numRating >= 6.0) {
      return "bg-red-500"; // Fair
    } else {
      return "bg-gray-500"; // Poor
    }
  };

  // Function to get accommodation type styling and icon
  const getAccommodationTypeStyle = (type: string) => {
    const lowerType = type.toLowerCase();

    if (lowerType.includes('hotel')) {
      return {
        bgColor: "bg-indigo-100",
        textColor: "text-indigo-800",
        icon: HotelIcon // Hotel building icon
      };
    } else if (lowerType.includes('resort')) {
      return {
        bgColor: "bg-emerald-100",
        textColor: "text-emerald-800",
        icon: TreePine // Resort/nature icon
      };
    } else if (lowerType.includes('apartment') || lowerType.includes('flat')) {
      return {
        bgColor: "bg-purple-100",
        textColor: "text-purple-800",
        icon: Building2 // Apartment building icon
      };
    } else if (lowerType.includes('villa') || lowerType.includes('house') || lowerType.includes('cottage')) {
      return {
        bgColor: "bg-green-100",
        textColor: "text-green-800",
        icon: Home // House icon
      };
    } else if (lowerType.includes('hostel') || lowerType.includes('lodge') || lowerType.includes('dormitory')) {
      return {
        bgColor: "bg-orange-100",
        textColor: "text-orange-800",
        icon: Bed // Bed icon for hostels
      };
    } else if (lowerType.includes('guest') || lowerType.includes('b&b') || lowerType.includes('bnb') || lowerType.includes('homestay')) {
      return {
        bgColor: "bg-pink-100",
        textColor: "text-pink-800",
        icon: Heart // Heart icon for personal touch
      };
    } else if (lowerType.includes('palace') || lowerType.includes('castle') || lowerType.includes('heritage')) {
      return {
        bgColor: "bg-amber-100",
        textColor: "text-amber-800",
        icon: Castle // Castle icon for luxury heritage properties
      };
    } else if (lowerType.includes('motel') || lowerType.includes('inn')) {
      return {
        bgColor: "bg-blue-100",
        textColor: "text-blue-800",
        icon: Car // Car icon for motels (roadside)
      };
    } else {
      return {
        bgColor: "bg-gray-100",
        textColor: "text-gray-800",
        icon: HotelIcon // Default hotel icon
      };
    }
  };

  return (
    <div className={`relative bg-white rounded-lg shadow-lg border border-gray-100 overflow-hidden ${
      type === "grid"
        ? "max-w-sm mx-auto mb-6"
        : "mb-6"
    }`}>
      {isShareGroupVisible && (
        <div
          className="absolute top-3 left-3 z-base bg-white bg-opacity-95 backdrop-blur-sm h-8 w-8 rounded-lg flex items-center justify-center shadow-md"
          onClick={(e) => e.stopPropagation()}
        >
          <input
            type="checkbox"
            className="transform scale-120 cursor-pointer"
            checked={isSelected}
            onChange={handleCheckboxChange}
          />
        </div>
      )}

      <div className={`flex flex-col ${type === "grid" ? "" : "md:flex-row"} ${type === "grid" ? "min-h-auto" : "min-h-[280px]"}`}>
        {/* Left Column - Hotel Image (30% for list, 100% for grid) */}
        <div className={`relative ${isSearchCompleted ? 'cursor-pointer' : 'cursor-not-allowed opacity-75'} ${type === "grid" ? "w-full h-48" : "md:w-[30%] h-48 md:h-auto"}`} onClick={isSearchCompleted ? handleClick : undefined}>
          {hotel?.imageInfoList?.length > 0 ? (
            <Image
              alt="hotel image"
              src={hotel.imageInfoList[0].url}
              fill
              className="object-cover object-center cursor-pointer"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-500 text-sm font-medium cursor-pointer">
              {t("hotel.card.noImageFound")}
            </div>
          )}

          {/* Heart Icon */}
          <button
            onClick={(e) => {
              handlaSave();
              e.stopPropagation();
            }}
            className="absolute top-3 right-3 bg-white bg-opacity-70 p-2 rounded-full cursor-pointer"
          >
            <Heart
              size={20}
              className={`${isHeartActive ? "text-red-500" : "text-red-500"}`}
              fill={isHeartActive ? "#ef4444" : "none"}
            />
          </button>
        </div>

        {/* Middle Column - Hotel Information (45% for list, 100% for grid) */}
        <div className={`p-4 cursor-pointer ${type === "grid" ? "w-full border-b border-gray-200" : "md:p-6 md:w-[45%] border-b md:border-b-0 md:border-r border-gray-200"}`} onClick={handleClick}>
          <div className="mb-3">
            <div className="flex items-center gap-2 mb-1">
              <h2 className="text-xl font-bold text-gray-800">
                {hotel?.name}
              </h2>
              {(() => {
                const accommodationType = hotel?.hotelType || "Hotel";
                const style = getAccommodationTypeStyle(accommodationType);
                const IconComponent = style.icon;
                return (
                  <span className={`px-2 py-0.5 ${style.bgColor} ${style.textColor} text-xs font-medium rounded whitespace-nowrap`}>
                    <IconComponent size={12} className="mr-1 inline" />
                    {accommodationType}
                  </span>
                );
              })()}
            </div>
            <div className="flex items-center mt-1">
              <div className="flex text-yellow-400 mr-2">
                {Array.from({ length: Math.max(0, hotel?.starRating || 4) }).map((_, index) => (
                  <Star key={index} size={16} fill="#fbbf24" className="text-yellow-400" />
                ))}
              </div>
            </div>
          </div>

          <div className="mb-4">
            <div className="flex items-center text-sm text-gray-600 mb-1">
              <MapPin size={14} className="mr-2 text-gray-500" />
              <span>{hotel?.city || hotel?.address || ""}</span>
            </div>
            {hotel?.distanceFromSearchedEntity && (
              <div className="flex items-center text-sm text-gray-500">
                <MapPin size={14} className="mr-2 text-gray-400" />
                <span>{hotel.distanceFromSearchedEntity}</span>
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            {hotel?.topOfferings?.slice(0, 3).map((offering, index) => (
              <span key={index} className={`px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap ${
                index === 0 ? "bg-green-100 text-green-800" :
                index === 1 ? "bg-blue-100 text-blue-800" :
                "bg-orange-100 text-orange-800"
              }`}>
                {index === 0 && <CheckCircle size={12} className="mr-1 inline" />}
                {index === 1 && <PawPrint size={12} className="mr-1 inline" />}
                {index === 2 && <CreditCard size={12} className="mr-1 inline" />}
                {offering}
              </span>
            ))}
            {hotel?.fareDetail?.refundable && (
              <span className="px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 whitespace-nowrap">
                <CheckCircle size={12} className="mr-1 inline" />
                Free Cancellation
              </span>
            )}
          </div>

          <div className="flex flex-wrap gap-x-4 gap-y-1 mb-2">
            {getFilteredAmenities(hotel?.amenities || [], hotel?.topOfferings || [])?.slice(0, 5).map((amenity, index) => {
              const amenityName = amenity.name || amenity; // Handle both object and string formats

              return (
                <div key={index} className="flex items-center text-sm text-gray-700 min-w-0">
                  {getAmenityIcon(amenityName, 16, "mr-2 text-gray-500 flex-shrink-0")}
                  <span className="whitespace-nowrap">{amenityName}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Right Column - Pricing & Actions (25% for list, 100% for grid) */}
        <div className={`p-4 ${type === "grid" ? "w-full" : "md:p-6 md:w-[25%]"} flex flex-col justify-between`}>
          <div className="cursor-pointer" onClick={handleClick}>
            {type === "grid" ? (
              /* Grid Layout - Split container */
              <div className="flex justify-between items-start mb-3">
                {/* Left side - Rating and reviews */}
                <div className="flex items-center gap-2">
                  <div className="text-left">
                    <div className="text-sm font-semibold text-gray-800">
                      {hotel?.userRatingCategory || "Good"}
                    </div>
                    {hotel?.userRatingCount && hotel.userRatingCount > 0 && (
                      <div className="text-xs text-gray-500">
                        {hotel.userRatingCount} reviews
                      </div>
                    )}
                  </div>
                  <div className={`${getRatingBadgeColor(hotel?.userRating || "7.5")} text-white text-sm font-bold px-2 py-1 rounded min-w-[40px] text-center`}>
                    {hotel?.userRating || "7.5"}
                  </div>
                </div>

                {/* Right side - Pricing */}
                <div className="text-right">
                  {isPricingAvailable ? (
                    <>
                      {hotel?.fareDetail?.displayedBaseFare && hotel?.fareDetail?.displayedBaseFare > hotel?.fareDetail?.totalPrice && (
                        <div className="flex items-center justify-end mb-1">
                          <span className="line-through text-gray-500 text-sm mr-2">
                            ₹{hotel.fareDetail.displayedBaseFare.toLocaleString()}
                          </span>
                          <span className="bg-red-100 text-red-700 text-xs font-medium px-2 py-0.5 rounded">
                            {Math.round(((hotel.fareDetail.displayedBaseFare - hotel.fareDetail.totalPrice) / hotel.fareDetail.displayedBaseFare) * 100)}% OFF
                          </span>
                        </div>
                      )}
                      <div className="mb-1">
                        <span className="text-2xl font-bold text-gray-800">
                          ₹{hotel?.fareDetail?.totalPrice?.toLocaleString() || "2,499"}
                        </span>
                        <span className="text-sm text-gray-600">/night</span>
                      </div>
                    </>
                  ) : (
                    <PriceShimmer layout="grid" />
                  )}
                  {isSearchCompleted && (
                    <div className="text-sm text-gray-500 mb-4">
                      <div>+₹{hotel?.taxesAndCharges || "5,200"} taxes & charges</div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              /* List Layout - Only rating badge */
              <>
                {/* Rating Badge */}
                <div className="flex items-center justify-end gap-2 mb-3">
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-800">
                      {hotel?.userRatingCategory || "Good"}
                    </div>
                    {hotel?.userRatingCount && hotel.userRatingCount > 0 && (
                      <div className="text-xs text-gray-500">
                        {hotel.userRatingCount} reviews
                      </div>
                    )}
                  </div>
                  <div className={`${getRatingBadgeColor(hotel?.userRating || "7.5")} text-white text-sm font-bold px-2 py-1 rounded min-w-[40px] text-center`}>
                    {hotel?.userRating || "7.5"}
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Pricing section for list view only */}
          {type !== "grid" && (
            <div className="space-y-2">
              <div className={`${isSearchCompleted ? 'cursor-pointer' : 'cursor-not-allowed'}`} onClick={isSearchCompleted ? handleClick : undefined}>
                {isPricingAvailable ? (
                  <>
                    {hotel?.fareDetail?.displayedBaseFare && hotel?.fareDetail?.displayedBaseFare > hotel?.fareDetail?.totalPrice && (
                      <div className="flex items-center justify-end mb-1">
                        <span className="line-through text-gray-500 text-sm mr-2">
                          ₹{hotel.fareDetail.displayedBaseFare.toLocaleString()}
                        </span>
                        <span className="bg-red-100 text-red-700 text-xs font-medium px-2 py-0.5 rounded">
                          {Math.round(((hotel.fareDetail.displayedBaseFare - hotel.fareDetail.totalPrice) / hotel.fareDetail.displayedBaseFare) * 100)}% OFF
                        </span>
                      </div>
                    )}
                    <div className="text-right mb-1">
                      <span className="text-2xl font-bold text-gray-800">
                        ₹{hotel?.fareDetail?.totalPrice?.toLocaleString() || "2,499"}
                      </span>
                      <span className="text-sm text-gray-600">/night</span>
                    </div>
                  </>
                ) : (
                  <PriceShimmer layout="list" />
                )}
                {isSearchCompleted && (
                  <div className="text-right text-sm text-gray-500 mb-4">
                    <div>+₹{hotel?.taxesAndCharges || "5,200"} taxes & charges</div>
                  </div>
                )}
              </div>
              <button
                className={`w-full py-2 px-4 font-medium rounded-lg transition-colors whitespace-nowrap ${
                  isSearchCompleted
                    ? 'bg-blue-600 text-white hover:bg-blue-700 cursor-pointer'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
                onClick={isSearchCompleted ? handleClick : undefined}
                disabled={!isSearchCompleted}
              >
                {isSearchCompleted ? 'See Availability' : 'Loading...'}
              </button>
            </div>
          )}

          {/* Button for grid view only - hidden on mobile */}
          {type === "grid" && (
            <div className="space-y-2 hidden md:block">
              <button
                className={`w-full py-2 px-4 font-medium rounded-lg transition-colors whitespace-nowrap ${
                  isSearchCompleted
                    ? 'bg-blue-600 text-white hover:bg-blue-700 cursor-pointer'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
                onClick={isSearchCompleted ? handleClick : undefined}
                disabled={!isSearchCompleted}
              >
                {isSearchCompleted ? 'See Availability' : 'Loading...'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Promotional Banner - Only show if offer exists */}
      {(hotel?.fareDetail?.offer_title || hotel?.fareDetail?.offer_description) && (
        <div className={`bg-yellow-50 border-t border-yellow-100 ${isSearchCompleted ? 'cursor-pointer' : 'cursor-not-allowed opacity-75'} ${type === "grid" ? "p-2" : "p-3"}`} onClick={isSearchCompleted ? handleClick : undefined}>
          <div className={`flex items-center ${type === "grid" ? "text-xs" : "text-sm"} text-yellow-800`}>
            <BadgePercent size={type === "grid" ? 14 : 16} className="mr-2 flex-shrink-0" />
            <div className="flex flex-wrap items-center gap-1">
              <span className="font-medium">
                {hotel.fareDetail.offer_title || hotel.fareDetail.offer_description || ""}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default HotelCard;


