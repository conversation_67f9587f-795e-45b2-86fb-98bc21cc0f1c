"use client";
import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from '@/app/hooks/useTranslation';
import Image from 'next/image';
import { LocationSuggestion } from '@/models/hotel/search-page.model';

// Enhanced city data with property counts and country codes
interface CityData {
  name: string;
  propertyCount: number;
  countryCode: string;
  country: string;
}

const cities: CityData[] = [
  { name: 'Delhi', propertyCount: 1245, countryCode: 'in', country: 'India' },
  { name: 'Mumbai', propertyCount: 987, countryCode: 'in', country: 'India' },
  { name: 'Bengaluru', propertyCount: 856, countryCode: 'in', country: 'India' },
  { name: 'Goa', propertyCount: 1532, countryCode: 'in', country: 'India' },
  { name: 'Dubai', propertyCount: 2345, countryCode: 'ae', country: 'United Arab Emirates' },
  { name: 'London', propertyCount: 3456, countryCode: 'gb', country: 'United Kingdom' },
  { name: 'Paris', propertyCount: 2987, countryCode: 'fr', country: 'France' },
  { name: 'New York', propertyCount: 3245, countryCode: 'us', country: 'United States' },
  { name: 'Singapore', propertyCount: 1876, countryCode: 'sg', country: 'Singapore' },
  { name: 'Bangkok', propertyCount: 2134, countryCode: 'th', country: 'Thailand' },
  { name: 'Tokyo', propertyCount: 2567, countryCode: 'jp', country: 'Japan' },
  { name: 'Sydney', propertyCount: 1765, countryCode: 'au', country: 'Australia' },
];

interface PopularPlace {
  name: string;
  city: string;
  propertyCount: number;
  countryCode: string;
  country: string;
}

interface RecentSearch {
  id: string;
  destination: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  rooms: number;
}

export const popularPlaces: PopularPlace[] = [
  { name: "Taj Mahal", city: "Agra", propertyCount: 245, countryCode: "in", country: "India" },
  { name: "Burj Khalifa", city: "Dubai", propertyCount: 2345, countryCode: "ae", country: "United Arab Emirates" },
  { name: "Big Ben", city: "London", propertyCount: 3456, countryCode: "gb", country: "United Kingdom" },
  { name: "Eiffel Tower", city: "Paris", propertyCount: 2987, countryCode: "fr", country: "France" },
  { name: "Statue of Liberty", city: "New York", propertyCount: 3245, countryCode: "us", country: "United States" },
  { name: "Colosseum", city: "Rome", propertyCount: 1876, countryCode: "it", country: "Italy" },
];

interface LocationSearchProps {
  onLocationSelect: (location: string) => void;
  onLocationObjectSelect?: (locationObject: LocationSuggestion) => void; // New callback for full location object
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  handleClose: () => void;
  autoFetchLocation?: boolean;
  popularPlacesData?: LocationSuggestion[];
  recentSearchesData?: LocationSuggestion[];
  apiSuggestions?: LocationSuggestion[];
  localFilteredResults?: LocationSuggestion[];
  showLocalResults?: boolean;
  isLoading?: boolean;
  onAutoSuggestApi?: (searchTerm: string) => Promise<void>;
}

// Shimmer loading component
const ShimmerItem = () => (
  <div className="flex items-center px-2 py-1.5 animate-pulse">
    <div className="w-5 h-5 bg-gray-200 rounded-full mr-1.5"></div>
    <div className="flex-1 min-w-0">
      <div className="flex items-center mb-1">
        <div className="h-3 bg-gray-200 rounded w-24 mr-2"></div>
        <div className="h-2 bg-gray-200 rounded w-12"></div>
      </div>
      <div className="h-2 bg-gray-200 rounded w-32"></div>
    </div>
  </div>
);

// Shimmer loading list
const ShimmerList = () => (
  <div className="py-1">
    {[...Array(5)].map((_, idx) => (
      <ShimmerItem key={idx} />
    ))}
  </div>
);

export default function LocationSearch({
  onLocationSelect,
  onLocationObjectSelect,
  searchQuery,
  setSearchQuery,
  handleClose,
  autoFetchLocation = false,
  popularPlacesData,
  recentSearchesData,
  apiSuggestions,
  localFilteredResults,
  showLocalResults = true,
  isLoading = false,
  onAutoSuggestApi,
}: LocationSearchProps) {
  const { t } = useTranslation();
  const [filteredCities, setFilteredCities] = useState<CityData[]>([]);
  const [isLocationLoading, setIsLocationLoading] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [allSuggestions, setAllSuggestions] = useState<(LocationSuggestion | CityData)[]>([]);

  // Use formatted data if provided, otherwise use original data
  const displayPopularPlaces = popularPlacesData || popularPlaces.map((place, index) => ({
    id: `popular_${index}`,
    name: place.name,
    fullName: `${place.name}, ${place.city}, ${place.country}`,
    type: 'popular_place',
    state: place.city || '',
    country: place.country,
    coordinates: { lat: 0, long: 0 },
    referenceScore: place.propertyCount || 0,
    code: place.countryCode || '',
    city: place.city || '',
    referenceId: `popular_${index}`
  }));

  const displayRecentSearches = recentSearchesData || [];

  // Load recent searches from localStorage
  useEffect(() => {
    if (!recentSearchesData && typeof window !== 'undefined') {
      const savedRecentSearches = localStorage.getItem('recentSearches');
      if (savedRecentSearches) {
        try {
          const parsedSearches = JSON.parse(savedRecentSearches);
          setRecentSearches(parsedSearches.slice(0, 4));
        } catch (error) {
          console.error('Error parsing recent searches:', error);
          setRecentSearches([]);
        }
      }
    }
  }, [recentSearchesData]);

  // Filter cities based on search query
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredCities([]);
    } else {
      const filtered = cities.filter(
        (city) =>
          city.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          city.country.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCities(filtered);
    }
  }, [searchQuery]);

  // Function to fetch current location with auto-suggest API integration
  const fetchCurrentLocation = useCallback(async () => {
    if (!navigator.geolocation) {
      setLocationError(t("search.location_error.not_supported"));
      return;
    }

    setIsLocationLoading(true);
    setLocationError(null);
    setSearchQuery(t("search.fetching_location"));

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;

        try {
          const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`);
          const data = await response.json();

          const city = data.address?.city || data.address?.town || data.address?.village || data.address?.county || data.address?.state;
          const fullLocationName = city ? `${city}, ${data.address?.country}` : data.display_name;

          // If onAutoSuggestApi is provided, use it to get suggestions and let parent handle selection
          if (onAutoSuggestApi && city) {
            try {
              setSearchQuery(t("search.searching_location"));

              // Call the auto-suggest API with ONLY the city name
              await onAutoSuggestApi(city.trim());

              // The parent component will handle the API response and selection
              // We just need to close the component here
              setIsLocationLoading(false);
              handleClose();

              console.log('Current location auto-suggest initiated with city:', {
                cityName: city,
                fullLocation: fullLocationName
              });
            } catch (apiError) {
              console.error("Error calling auto-suggest API for current location:", apiError);
              // Fallback to full location name if API call fails
              setSearchQuery(fullLocationName);
              onLocationSelect(fullLocationName);
              setIsLocationLoading(false);
              handleClose();
            }
          } else {
            // Fallback to original behavior if no auto-suggest API provided or no city found
            setSearchQuery(fullLocationName);
            onLocationSelect(fullLocationName);
            setIsLocationLoading(false);
            handleClose();
          }
        } catch (error) {
          console.error("Error reverse geocoding:", error);
          setSearchQuery(t("search.current_location"));
          onLocationSelect(t("search.current_location"));
          setIsLocationLoading(false);
          setLocationError(t("search.location_error.geocoding_failed"));
          handleClose();
        }
      },
      (error) => {
        console.error("Error getting location:", error);
        setSearchQuery("");
        setIsLocationLoading(false);
        let errorMessage = t("search.location_error.default");
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = t("search.location_error.permission_denied");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = t("search.location_error.unavailable");
            break;
          case error.TIMEOUT:
            errorMessage = t("search.location_error.timeout");
            break;
        }
        setLocationError(errorMessage);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 300000 }
    );
  }, [t, setSearchQuery, onLocationSelect, handleClose, onAutoSuggestApi]);

  // Auto-fetch location when component mounts
  useEffect(() => {
    if (autoFetchLocation && !searchQuery.trim() && !isLocationLoading) {
      fetchCurrentLocation();
    }
  }, [autoFetchLocation, searchQuery, isLocationLoading, fetchCurrentLocation]);

  // Debug API suggestions
  useEffect(() => {
    if (apiSuggestions) {
      console.log('LocationSearch received API suggestions:', {
        showLocalResults,
        apiSuggestionsLength: apiSuggestions.length,
        apiSuggestions: apiSuggestions
      });
    }
  }, [apiSuggestions, showLocalResults]);

  // Update combined suggestions list for keyboard navigation
  useEffect(() => {
    let suggestions: (LocationSuggestion | CityData)[] = [];
    
    if (searchQuery) {
      if (!showLocalResults && apiSuggestions && apiSuggestions.length > 0) {
        suggestions = [...apiSuggestions];
      } else if (showLocalResults && localFilteredResults && localFilteredResults.length > 0) {
        suggestions = [...localFilteredResults];
      } else if (filteredCities.length > 0) {
        suggestions = [...filteredCities];
      }
    } else {
      const allDefaultSuggestions: any[] = [];
      allDefaultSuggestions.push({
        id: 'current-location',
        name: 'Use current location',
        fullName: 'Use current location',
        type: 'current-location'
      });
      
      if (displayRecentSearches.length > 0) {
        allDefaultSuggestions.push(...displayRecentSearches);
      } else if (recentSearches.length > 0) {
        allDefaultSuggestions.push(...recentSearches.map(search => ({
          id: search.id,
          name: search.destination,
          fullName: search.destination,
          type: 'recent-search'
        })));
      }
      
      allDefaultSuggestions.push(...displayPopularPlaces);
      suggestions = allDefaultSuggestions;
    }
    
    setAllSuggestions(suggestions);
    setSelectedIndex(-1);
  }, [apiSuggestions, localFilteredResults, filteredCities, showLocalResults, searchQuery, displayPopularPlaces, displayRecentSearches, recentSearches]);

  // Scroll selected item into view
  useEffect(() => {
    if (selectedIndex >= 0) {
      const selectedElement = document.getElementById(`suggestion-${selectedIndex}`);
      if (selectedElement) {
        selectedElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  }, [selectedIndex]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setSelectedIndex(-1);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => (prev < allSuggestions.length - 1 ? prev + 1 : prev));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => (prev > 0 ? prev - 1 : -1));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && allSuggestions[selectedIndex]) {
        handleLocationSelection(allSuggestions[selectedIndex]);
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleClose();
    }
  };

  // Unified function to handle all location selections
  const handleLocationSelection = (suggestion: any) => {
    // Handle current location separately
    if (suggestion.type === 'current-location') {
      fetchCurrentLocation();
      return;
    }

    // Determine the location name to display
    let locationName: string;
    if (suggestion.type === 'recent-search') {
      locationName = suggestion.name || suggestion.destination;
    } else if ('fullName' in suggestion) {
      locationName = suggestion.fullName;
    } else {
      locationName = suggestion.name;
    }

    // Call the main location select callback
    onLocationSelect(locationName);

    // If this is an API suggestion with location ID, call the location details API
    if (suggestion.id && onLocationObjectSelect) {
      onLocationObjectSelect(suggestion);
    }

    // Close the dropdown
    handleClose();
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
    setFilteredCities([]);
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Fixed Search Input Header */}
      <div className="flex-shrink-0 bg-white sticky top-0 z-10 border-b border-gray-200">
        <div className="relative p-4">
          <div className="absolute inset-y-0 left-0 pl-7 flex items-center pointer-events-none">
            {isLocationLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            ) : (
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            )}
          </div>
          <input
            type="text"
            placeholder={t("search.destination_placeholder")}
            value={searchQuery}
            onChange={handleSearchChange}
            onKeyDown={handleKeyDown}
            className="w-full h-10 pl-10 pr-10 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            autoFocus
            role="combobox"
            aria-expanded={allSuggestions.length > 0}
            aria-activedescendant={selectedIndex >= 0 ? `suggestion-${selectedIndex}` : undefined}
            aria-autocomplete="list"
          />
          {searchQuery && (
            <button onClick={clearSearch} className="absolute inset-y-0 right-0 flex items-center pr-7">
              <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          )}
        </div>

        {/* Error message */}
        {locationError && (
          <div className="mx-4 mb-4 px-3 py-2 text-xs text-red-600 bg-red-50 border border-red-200 rounded">
            {locationError}
          </div>
        )}
      </div>

      {/* Scrollable Content Area - Takes remaining height */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden" role="listbox" aria-label="Location suggestions">
        {/* Search Results */}
        {searchQuery ? (
          <>
            {/* API suggestions */}
            {!showLocalResults && apiSuggestions && apiSuggestions.length > 0 ? (
              <div className="py-1">
                <div className="px-2 py-1 mb-1 bg-green-50">
                  <p className="text-xs uppercase font-semibold text-green-600 tracking-wider">
                    Search Results ({apiSuggestions.length})
                  </p>
                </div>
                {apiSuggestions.map((suggestion, idx) => {
                  const isSelected = selectedIndex === idx;
                  return (
                    <div
                      key={suggestion.id || idx}
                      id={`suggestion-${idx}`}
                      className={`flex items-center px-2 py-1.5 cursor-pointer transition-colors ${
                        isSelected ? 'bg-blue-50 border-l-2 border-blue-500' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => handleLocationSelection(suggestion)}
                      role="option"
                      aria-selected={isSelected}
                    >
                      <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-primary">
                        <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center">
                          <span className="text-xs font-medium text-gray-800 truncate">{suggestion.name}</span>
                          <span className="ml-1 text-xs text-gray-500 bg-blue-100 px-1 rounded-sm whitespace-nowrap">{suggestion.type}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500 truncate">{suggestion.fullName}</span>
                          <span className="text-xs uppercase font-semibold text-gray-500">{suggestion.country}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : isLoading ? (
              <>
                {/* Local filtered results while loading */}
                {localFilteredResults && localFilteredResults.length > 0 && (
                  <div className="py-1">
                    <div className="px-2 py-1 mb-1 bg-blue-50">
                      <p className="text-xs uppercase font-semibold text-blue-600 tracking-wider">Quick Results</p>
                    </div>
                    {localFilteredResults.map((suggestion, idx) => (
                      <div
                        key={suggestion.id || idx}
                        className="flex items-center px-2 py-1.5 cursor-pointer transition-colors hover:bg-gray-50"
                        onClick={() => handleLocationSelection(suggestion)}
                      >
                        <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-blue-500">
                          <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          </svg>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center">
                            <span className="text-xs font-medium text-gray-800 truncate">{suggestion.name}</span>
                            <span className="ml-1 text-xs text-gray-500 bg-gray-100 px-1 rounded-sm whitespace-nowrap">{suggestion.type}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500 truncate">{suggestion.fullName}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Shimmer loading */}
                <div className="py-1">
                  <div className="px-2 py-1 mb-1 bg-gray-100">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-2"></div>
                      <p className="text-xs uppercase font-semibold text-gray-500 tracking-wider">Searching more results...</p>
                    </div>
                  </div>
                  <ShimmerList />
                </div>
              </>
            ) : showLocalResults && localFilteredResults && localFilteredResults.length > 0 ? (
              <div className="py-1">
                <div className="px-2 py-1 mb-1 bg-blue-50">
                  <p className="text-xs uppercase font-semibold text-blue-600 tracking-wider">Quick Results</p>
                </div>
                {localFilteredResults.map((suggestion, idx) => (
                  <div
                    key={suggestion.id || idx}
                    className="flex items-center px-2 py-1.5 cursor-pointer transition-colors hover:bg-gray-50"
                    onClick={() => handleLocationSelection(suggestion)}
                  >
                    <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-blue-500">
                      <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <span className="text-xs font-medium text-gray-800 truncate">{suggestion.name}</span>
                        <span className="ml-1 text-xs text-gray-500 bg-gray-100 px-1 rounded-sm whitespace-nowrap">{suggestion.type}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 truncate">{suggestion.fullName}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredCities.length > 0 ? (
              <div className="py-1">
                <div className="px-2 py-1 mb-1 bg-gray-100">
                  <p className="text-xs uppercase font-semibold text-gray-500 tracking-wider">Cities</p>
                </div>
                {filteredCities.map((city, idx) => (
                  <div
                    key={idx}
                    className="flex items-center px-2 py-1.5 cursor-pointer transition-colors hover:bg-gray-50"
                    onClick={() => handleLocationSelection(city)}
                  >
                    <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-primary">
                      <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 616 0z"></path>
                      </svg>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <span className="text-xs font-medium text-gray-800 truncate">{city.name}</span>
                        <span className="ml-1 text-xs text-gray-500 bg-gray-100 px-1 rounded-sm whitespace-nowrap">{city.propertyCount}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 truncate">{city.country}</span>
                        <div className="flex items-center ml-1 gap-1 flex-shrink-0">
                          <div className="w-5 h-3 overflow-hidden rounded-sm border border-gray-200">
                            <Image src={`/assets/img/country-logo/${city.countryCode}.webp`} alt={city.country} width={20} height={12} className="object-cover" />
                          </div>
                          <span className="text-xs uppercase font-semibold text-gray-500">{city.countryCode}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-8 text-center text-gray-500">
                <p className="text-sm">No results found for "{searchQuery}"</p>
                <p className="text-xs mt-1">Try searching for a different location</p>
              </div>
            )}
          </>
        ) : (
          <>
            {/* Current Location Option */}
            <div className="py-1">
              <div
                className={`flex items-center px-2 py-1.5 cursor-pointer transition-colors ${
                  !searchQuery && selectedIndex === 0 ? 'bg-blue-50 border-l-2 border-blue-500' : 'hover:bg-gray-50'
                }`}
                onClick={() => fetchCurrentLocation()}
              >
                <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-blue-500">
                  {isLocationLoading ? (
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                  ) : (
                    <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 616 0z" />
                    </svg>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {isLocationLoading ? t("search.fetching_location") : t("search.use_current_location")}
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    {isLocationLoading ? t("search.please_wait") : t("search.detect_location_automatically")}
                  </p>
                </div>
              </div>
            </div>

            {/* Recent Searches */}
            {(displayRecentSearches.length > 0 || recentSearches.length > 0) && (
              <div className="py-1">
                <div className="px-2 py-1 mb-1 bg-gray-100">
                  <p className="text-xs uppercase font-semibold text-gray-500 tracking-wider">Recent Searches</p>
                </div>
                {displayRecentSearches.length > 0 ? (
                  displayRecentSearches.map((search) => (
                    <div
                      key={search.id}
                      className="flex items-center px-2 py-1.5 cursor-pointer transition-colors hover:bg-gray-50"
                      onClick={() => handleLocationSelection(search)}
                    >
                      <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-primary">
                        <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{search.name || search.fullName}</p>
                        <p className="text-xs text-gray-500 truncate">
                          {search.city && search.country ? `${search.city}, ${search.country}` : search.fullName}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  recentSearches.map((search) => (
                    <div
                      key={search.id}
                      className="flex items-center px-2 py-1.5 cursor-pointer transition-colors hover:bg-gray-50"
                      onClick={() => handleLocationSelection(search)}
                    >
                      <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-primary">
                        <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{search.destination}</p>
                        <p className="text-xs text-gray-500 truncate">{search.checkIn} - {search.checkOut} • {search.guests} guests</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {/* Popular Places */}
            <div className="py-1">
              <div className="px-2 py-1 mb-1 bg-gray-100">
                <p className="text-xs uppercase font-semibold text-gray-500 tracking-wider">Popular Places</p>
              </div>
              {displayPopularPlaces.map((place, idx) => (
                <div
                  key={place.id || idx}
                  className="flex items-center px-2 py-1.5 cursor-pointer transition-colors hover:bg-gray-50"
                  onClick={() => handleLocationSelection(place)}
                >
                  <div className="w-5 h-5 flex items-center justify-center rounded-full text-white mr-1.5 bg-secondary">
                    <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 616 0z"></path>
                    </svg>
                  </div>
                  <div className="flex item-center w-full">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <span className="text-xs font-medium text-gray-800 truncate">{place.name}</span>
                        <span className="ml-1 text-xs text-gray-500 bg-gray-100 px-1 rounded-sm whitespace-nowrap">
                          {place.referenceScore || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 truncate">{place.city || place.state}</span>
                      </div>
                    </div>
                    <div className="flex items-center ml-1 gap-1 flex-shrink-0">
                      {place.code && (
                        <>
                          <div className="relative w-6 h-4 overflow-hidden rounded-sm border border-gray-200">
                            <Image src={`/assets/img/country-logo/${place.code}.webp`} alt={place.country} fill className="object-cover" />
                          </div>
                          <span className="text-xs uppercase font-semibold text-gray-500">{place.code}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
