@use "/styles/variable" as * ;
.shimmer-hotel-info-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 0 30px 0;
  
    @keyframes shimmer {
      0% {
        background-position: -200px 0;
      }
      100% {
        background-position: 200px 0;
      }
    }
  
    $shimmer-bg: linear-gradient(90deg, #e0e0e0 25%, #d6d6d6 50%, #e0e0e0 75%);
    $shimmer-color: #d4d4d4;
  
    .shimmer-effect {
      background: $shimmer-bg;
      background-size: 400px 100%;
      animation: shimmer 1s infinite linear;
      border-radius: 4px;
    }
  
    .shimmer-info-header {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

                  
      @media (max-width: $breakpoint-md) {
        flex-direction: column;   
      }
  
      .heading {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 0 20px;
  
        h2 {
          width: 318px;
          height: 30px;
          @extend .shimmer-effect;

          @media (max-width: $breakpoint-md) {
            font-size: 28px;
          }
        }
  
        .rating {
          width: 40px;
          height: 14px;
          margin-left: 8px;
          @extend .shimmer-effect;
        }
      }
  
      .buttons {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;

        @media (max-width: $breakpoint-md) {
          padding-bottom: 15px;
        }
  
        .hotel-info-header-btn {
          width: 80px;
          height: 35px;
          @extend .shimmer-effect;
        }
  
        .hotel-info-header-reserveBtn {
          width: 113px;
          height: 45px;
          @extend .shimmer-effect;
        }
      }
    }
  
    .review-location {
      display: flex;
      flex-direction: row;
      justify-content: start;
      gap: 60px;

      
      @media(max-width: $isMobile){
        flex-direction: column;
        gap: 15px;
      }
  
      .review, .location {
        display: flex;
        flex-direction: row;
      }
  
      .review {
        .rating {
          width: 46px;
          height: 50px;
          @extend .shimmer-effect;
          border-radius: 10px;
        }
  
        .rating-detail {
            margin-left: 10px;
          .detail1, .detail2 {
            width: 80px;
            height: 14px;
            margin-top: 5px;
            @extend .shimmer-effect;
          }
        }
      }
  
      .location {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;
  
        .icon {
          width: 44px;
          height: 46px;
          @extend .shimmer-effect;
          border-radius: 5px;
        }
  
        .details {
          .detail1 {
            width: 265px;
            height: 14px;
            @extend .shimmer-effect;
            margin-bottom: 5px;
          }
  
          .detail2 {
            width: 90px;
            height: 14px;
            @extend .shimmer-effect;
          }
        }
      }
    }
  }
  