@use "/styles/variable" as *;
@use "sass:color";
@use "/styles/zIndex" as *;

.cancellation-policy {
  width: 100%;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 15px;

  @media (max-width: $breakpoint-md) {
    border-radius: 0%;
  }

  &__header {
    font-size: 20px;
    font-weight: 700;
    color: $booking_black;
    margin-bottom: 10px;
  }

  &__description {
    color: #5e616e;
    font-size: 16px;
    margin-bottom: 15px;

    @media (max-width: $breakpoint-sm) {
      font-size: 14px;
    }
  }

  &__timeline {
    width: 100%;
    display: flex;
    flex-direction: row;
    padding: 20px;
    margin-bottom: 15px;

    @media (max-width: $breakpoint-sm) {
      padding: 0;
    }

    .timeline-content {
      margin-bottom: 15px;
      //background-color: #fafafa;
      border-radius: 20px;
      width: 50%;
      display: flex;
      flex-direction: column;

      &.non-refundable {
        width: 100%;
      }

      .timeline-title {
        font-size: 14px;
        color: $booking_black;
        margin-bottom: 10px;
        padding: 0 20px;
        text-align: center;

        @media (max-width: $breakpoint-sm) {
          font-size: 13px;
        }
      }

      .timeline-divider {
        width: 100%;
        position: relative;
        .line {
          width: 100%;
          height: 4px;
          &.FULLY_REFUNDABLE {
            background-color: #054d44;
          }

          &.NON_REFUNDABLE {
            background-color: #ff0000;
          }

          &.PARTIALLY_REFUNDABLE {
            background-color: #ffa500;
          }
        }
      }

      .timeline-label-dot {
        display: flex;
        flex-direction: column;

        align-items: start;
        width: fit-content;
        position: absolute;
        top: -8px;

        &.second-style-dot {
          top: -5px;
        }

        &.right {
          right: 0;
          align-items: end;
        }

        .dot {
          // width: 16px;
          //   height: 16px;
          //   display: flex;
          //   align-items: center;
          //   justify-content: center;
          border-radius: 50%;
          //background-color: #054D44;
          //padding: 3px;
          background: #054d44;

          .icon {
            margin: 3px;

            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            border: 2px solid #fff;
            background-color: #054d44;

            &.icon2 {
              border: none;
            }

            .fa-solid {
              font-size: 6px;
              color: #fff;
            }
          }
        }

        .dot2 {
          width: 14px;
          height: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: #054d44;
          //background-color: #054D44;
          //padding: 3px;

          .icon {
            //border: 2px solid #fff;

            .fa-solid {
              font-size: 6px;
              color: #fff;
            }
          }
        }

        .label-time {
          display: flex;
          flex-direction: column;
        }

        .label {
          font-size: 12px;
          font-weight: 500;
          color: $booking_black;
        }

        .time {
          font-size: 12px;
          font-weight: 500;
          color: #5e616e;
        }
      }
    }
  }

  // &__timeline {
  //   width: 100%;
  //   display: flex;
  //   flex-direction: row;
  //   padding: 20px;
  //   border-radius: 20px;
  //   margin-bottom: 15px;
  //   background-color: #fafafa;

  //   .timeline-content {
  //     margin-bottom: 15px;
  //     border-radius: 20px;
  //     width: 50%;
  //     display: flex;
  //     flex-direction: column;

  //     &.non-refundable {
  //       width: 100%;
  //     }

  //     .timeline-title {
  //       font-size: 14px;
  //       color: #333;
  //       margin-bottom: 10px;
  //       padding: 0 20px;
  //       text-align: center;
  //     }

  //     .timeline-divider {
  //       width: 100%;
  //       position: relative;
  //       .line {
  //         width: 100%;
  //         height: 4px;
  //         &.FULLY_REFUNDABLE {
  //           background-color: #5A9999; /* Darker version of secondary color #76B1B1 */
  //         }

  //         &.NON_REFUNDABLE {
  //           background-color: #9A7962; /* Darker version of primary color #B19176 */
  //         }

  //         &.PARTIALLY_REFUNDABLE {
  //           background-color: #E09158; /* Orange-like shade for partially refundable */
  //         }
  //       }
  //     }

  //     .timeline-label-dot {
  //       display: flex;
  //       flex-direction: column;
  //       align-items: start;
  //       width: fit-content;
  //       position: absolute;
  //       top: -8px;

  //       &.second-style-dot {
  //         top: -5px;
  //       }

  //       &.right {
  //         right: 0;
  //         align-items: end;
  //       }

  //       .dot {
  //         border-radius: 50%;
  //         background: #9A7962; /* Darker version of primary color */

  //         .icon {
  //           margin: 3px;
  //           width: 16px;
  //           height: 16px;
  //           border-radius: 50%;
  //           display: flex;
  //           align-items: center;
  //           justify-content: center;
  //           border: 2px solid #fff;
  //           background-color: #9A7962; /* Darker version of primary color */

  //           &.icon2 {
  //             border: none;
  //           }

  //           .fa-solid {
  //             font-size: 6px;
  //             color: #fff;
  //           }
  //         }
  //       }

  //       .dot2 {
  //         width: 14px;
  //         height: 14px;
  //         display: flex;
  //         align-items: center;
  //         justify-content: center;
  //         border-radius: 50%;
  //         background-color: #9A7962; /* Darker version of primary color */

  //         .icon {
  //           .fa-solid {
  //             font-size: 6px;
  //             color: #fff;
  //           }
  //         }
  //       }

  //       .label-time {
  //         display: flex;
  //         flex-direction: column;
  //       }

  //       .label {
  //         font-size: 12px;
  //         font-weight: 500;
  //         color: #333;
  //       }

  //       .time {
  //         font-size: 12px;
  //         font-weight: 500;
  //         color: #5e616e;
  //       }
  //     }
  //   }
  // }

  &__link {
    .view-cancellation-link {
      font-size: 16px;
      font-weight: bold;
      //color: $secondary-color;
      cursor: pointer;

      @media (max-width: $breakpoint-sm) {
        margin-top: 30px;
      }
    }
  }
}

.cancellation-policy-modal-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  row-gap: 20px;

  .cancellation-details-timeline-refundable {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    .timeline-label-cancellation-details {
      display: flex;
      flex-direction: row;
      .timeline-divider {
        width: auto;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        z-index: z-index(base);
        .dot {
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          //background-color: #054D44;
          //padding: 3px;
          background: transparent;

          .icon {
            width: 14px;
            height: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            //border: 2px solid #fff;
            background-color: #054d44;

            .fa-solid {
              font-size: 6px;
              color: #fff;
            }
          }
        }

        .line {
          width: 4px;
          height: 100%;

          &.FULLY_REFUNDABLE {
            background-color: #054d44;
          }

          &.NON_REFUNDABLE {
            background-color: #ff0000;
          }

          &.PARTIALLY_REFUNDABLE {
            background-color: #ffa500;
          }

          &.non-refundable {
            background-color: #ff0000;
          }

          &.partially-refundable {
            background-color: #ffa500;
          }
        }
      }

      .timeline-label-cancellation-details {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .timline-label {
          font-size: 14px;
          font-weight: 500;
          color: $booking_black;
          margin: 0 0 0 3px;
          padding: 0;
          line-height: 1;
        }

        .cancellation-details {
          padding: 15px;
          margin: 20px 0px 20px -6px;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;

          &.FULLY_REFUNDABLE {
            border: 1.5px solid #054d44;
            background-color: color.mix(#ffffff, $primary_color, 80%);
          }

          &.NON_REFUNDABLE {
            border: 1.5px solid #ff0000;
            background-color: #ffeeee;
          }

          &.PARTIALLY_REFUNDABLE {
            border: 1.5px solid #ffa500;
            background-color: #fff5ee;
          }

          &.non-refundable {
            border: 1.5px solid #ff0000;
            background-color: #ffeeee;
          }

          &.partially-refundable {
            border: 1.5px solid #ffa500;
            background-color: #fff5ee;
          }

          .title {
            font-size: 14px;
            font-weight: bold;
            color: $booking_black;
          }

          .description {
            font-size: 14px;
            font-weight: 500;
            color: #5e616e;
          }
        }
      }
    }

    // .timeline-label-cancellation-details {
    //   display: flex;
    //   flex-direction: row;

    //   .timeline-divider {
    //     width: auto;
    //     height: 100%;
    //     display: flex;
    //     flex-direction: column;
    //     justify-content: space-between;
    //     align-items: center;
    //     z-index: 4;

    //     .dot {
    //       border-radius: 50%;
    //       display: flex;
    //       justify-content: center;
    //       align-items: center;
    //       background: transparent;

    //       .icon {
    //         width: 14px;
    //         height: 14px;
    //         display: flex;
    //         align-items: center;
    //         justify-content: center;
    //         border-radius: 50%;
    //         background-color: #9A7962; /* Darker version of primary color */

    //         .fa-solid {
    //           font-size: 6px;
    //           color: #fff;
    //         }
    //       }
    //     }

    //     .line {
    //       width: 4px;
    //       height: 100%;

    //       &.FULLY_REFUNDABLE {
    //         background-color: #5A9999; /* Darker version of secondary color */
    //       }

    //       &.NON_REFUNDABLE {
    //         background-color: #9A7962; /* Darker version of primary color */
    //       }

    //       &.PARTIALLY_REFUNDABLE {
    //         background-color: #E09158; /* Orange-like shade for partially refundable */
    //       }

    //       &.non-refundable {
    //         background-color: #9A7962; /* Darker version of primary color */
    //       }

    //       &.partially-refundable {
    //         background-color: #E09158; /* Orange-like shade for partially refundable */
    //       }
    //     }
    //   }

    //   .timeline-label-cancellation-details {
    //     display: flex;
    //     flex-direction: column;
    //     justify-content: space-between;

    //     .timline-label {
    //       font-size: 14px;
    //       font-weight: 500;
    //       color: #333;
    //       margin: 0 0 0 3px;
    //       padding: 0;
    //       line-height: 1;
    //     }

    //     .cancellation-details {
    //       padding: 15px;
    //       margin: 20px 0px 20px -6px;
    //       border-top-right-radius: 10px;
    //       border-bottom-right-radius: 10px;

    //       &.FULLY_REFUNDABLE {
    //         border: 1.5px solid #5A9999; /* Darker version of secondary color */
    //         background-color: rgba(90, 153, 153, 0.15); /* Light tint of darker secondary */
    //       }

    //       &.NON_REFUNDABLE {
    //         border: 1.5px solid #9A7962; /* Darker version of primary color */
    //         background-color: rgba(154, 121, 98, 0.15); /* Light tint of darker primary */
    //       }

    //       &.PARTIALLY_REFUNDABLE {
    //         border: 1.5px solid #E09158; /* Orange-like shade */
    //         background-color: rgba(224, 145, 88, 0.15); /* Light tint of orange shade */
    //       }

    //       &.non-refundable {
    //         border: 1.5px solid #9A7962; /* Darker version of primary color */
    //         background-color: rgba(154, 121, 98, 0.15); /* Light tint of darker primary */
    //       }

    //       &.partially-refundable {
    //         border: 1.5px solid #E09158; /* Orange-like shade */
    //         background-color: rgba(224, 145, 88, 0.15); /* Light tint of orange shade */
    //       }

    //       .title {
    //         font-size: 14px;
    //         font-weight: bold;
    //         color: #333;
    //       }

    //       .description {
    //         font-size: 14px;
    //         font-weight: 500;
    //         color: #5e616e;
    //       }
    //     }
    //   }
    // }
  }

  .note-container {
    &__title {
      color: $booking_black;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    &__description {
      font-size: 14px;
      color: #5e616e;
      margin-bottom: 20px;
    }
  }
}
