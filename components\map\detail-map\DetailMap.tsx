import React, { useState, useEffect, JSX } from 'react';
import { Heart, X, Navigation, Star, MapPin } from 'lucide-react';
import MapWrapper from '../MapWrapper';
import { MapMarker } from '../Map';

// Define interfaces
interface AttractionType {
  name: string;
  distance: string;
}

interface LocationType {
  name: string;
  rating: number;
  stars: string;
  position: [number, number];
  description: string;
  reviews: string;
  locationScore: string;
  address: string;
  attractions: Array<AttractionType>;
}

// interface MapMarker {
//   id: string;
//   position: [number, number];
//   iconType: string;
//   details: {
//     id: string;
//     name: string;
//     rating: number;
//     price: string;
//     description: string;
//     showViewButton: boolean;
//   };
// }

interface DetailMapProps {
  location?: LocationType;
  style?: React.CSSProperties;
  onFavoriteClick?: () => void;
  onCloseClick?: () => void;
  onShowPrices?: () => void;
}

const DetailMap: React.FC<DetailMapProps> = ({ 
  location,
  style,
  onFavoriteClick,
  // onCloseClick,
  onShowPrices
}) => {
  // Example location data
  const demoLocation: LocationType = {
    name: "Grand Plaza Hotel & Spa",
    rating: 8.9,
    stars: "★★★★★",
    position: [51.505, -0.09], // London coordinates
    description: "Exceptional",
    reviews: "2,387 reviews",
    locationScore: "Perfect location - 9.8",
    address: "123 Main St, City, Country",
    attractions: [
      { name: "Thykoodam Beach", distance: "0.5 km" },
      { name: "City Center", distance: "1.2 km" },
      { name: "Chilavannur Lake", distance: "0.8 km" }
    ]
  };

  // Use provided location or fallback to demo data
  const displayLocation = location || demoLocation;
  
  const [marker, setMarker] = useState<MapMarker | null>(null);
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [showCard, setShowCard] = useState<boolean>(true);

  useEffect(() => {
    // Create marker from location data
    if (displayLocation) {
      setMarker({
        id: '1',
        position: displayLocation.position,
        iconType: 'hotel' as const,
        details: {
          id: '1',
          name: displayLocation.name,
          rating: displayLocation.rating,
          price: '$120',
          description: displayLocation.description,
          showViewButton: true
        }
      });
    }
  }, [displayLocation]);

  const toggleFavorite = (): void => {
    setIsFavorite(!isFavorite);
    if (onFavoriteClick) onFavoriteClick();
  };

  const handleCloseCard = (): void => {
    setShowCard(false);
    // if (onCloseClick) onCloseClick();
  };

  const handleMarkerClick = (markerId: string): void => {
    console.log(`Marker ${markerId} clicked`);
    setShowCard(true);
  };

  const handleShowPrices = (): void => {
    if (onShowPrices) onShowPrices();
  };

  const renderStars = (starText: string): JSX.Element => {
    const starCount = (starText.match(/★/g) || []).length;
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <Star 
            key={i} 
            size={14} 
            className={i < starCount ? "text-yellow-400 fill-yellow-400" : "text-gray-300"} 
          />
        ))}
      </div>
    );
  };

  return (
    <div className="relative w-full h-96 bg-gray-100 rounded-lg" style={style}>
      {/* Map Component */}
      <div className="w-full h-full rounded-lg overflow-hidden">
        {marker && (
          <MapWrapper
            markers={[marker]}
            center={marker.position}
            zoom={15}
            style={{ height: '100%', width: '100%' }}
            fitBounds={false}
            interactive={true}
            onMarkerClick={handleMarkerClick}
          />
        )}
      </div>
      
      {/* Hotel info card */}
      {showCard && (
        <div style={{zIndex:1500}} className="absolute top-4 left-4 z-20 bg-white rounded-lg shadow-lg w-72 overflow-hidden border border-gray-100">
          <div className="p-3">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h2 className="text-lg font-bold text-gray-800">{displayLocation.name}</h2>
                <div className="mt-1">
                  {renderStars(displayLocation.stars)}
                </div>
              </div>
              <div className="flex space-x-1">
                <button 
                  type='button'
                  className={`p-1.5 rounded-full transition-colors ${isFavorite ? 'bg-red-50 text-red-500' : 'hover:bg-gray-100 text-gray-500'}`}
                  onClick={toggleFavorite}
                >
                  <Heart size={16} className={isFavorite ? "fill-red-500" : ""} />
                </button>
                <button 
                  type='button'
                  className="p-1.5 hover:bg-gray-100 rounded-full text-gray-500"
                  onClick={handleCloseCard}
                >
                  <X size={16} />
                </button>
              </div>
            </div>
            
            <div className="flex items-center mb-2">
              <div className="bg-blue-600 text-white font-bold p-1 px-2 rounded text-xs mr-2 flex items-center justify-center min-w-6">
                {displayLocation.rating}
              </div>
              <div>
                <div className="font-medium text-sm text-gray-800">{displayLocation.description}</div>
                <div className="text-xs text-gray-600">{displayLocation.reviews}</div>
              </div>
            </div>
            
            <div className="flex items-center text-xs text-gray-700 mb-2">
              <MapPin size={14} className="mr-1 text-gray-500" />
              <span>{displayLocation.locationScore}</span>
            </div>
            
            <div className="text-xs text-gray-600 mb-3 flex items-center">
              <Navigation size={14} className="mr-1 text-gray-500" />
              <span>{displayLocation.address}</span>
            </div>
            
            <button 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors flex items-center justify-center"
              onClick={handleShowPrices}
            >
              Check availability
              <span className="ml-1">→</span>
            </button>
          </div>
          
          {/* Attractions Section */}
          <div className="bg-gray-50 p-3 border-t border-gray-100">
            <h3 className="font-semibold text-gray-800 mb-2 text-sm">Nearby Attractions</h3>
            <div className="space-y-1">
              {displayLocation.attractions.map((attraction, index) => (
                <div key={index} className="flex justify-between text-xs">
                  <span className="text-gray-800">{attraction.name}</span>
                  <span className="text-gray-500 font-medium">{attraction.distance}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
      
      {/* Show on Map Button */}
      <div className="absolute bottom-4 right-4 z-10">
        <button className="bg-white hover:bg-gray-50 text-blue-600 font-medium py-1.5 px-3 text-sm rounded-lg shadow-lg flex items-center transition-colors">
          <MapPin size={16} className="mr-1" />
          Show on map
        </button>
      </div>
    </div>
  );
};

export default DetailMap;