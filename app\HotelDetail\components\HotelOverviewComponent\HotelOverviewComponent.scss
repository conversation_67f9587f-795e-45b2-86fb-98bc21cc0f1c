@use "/styles/variable" as *;
@use "sass:color";

.hotel-overview-container {
  width: 100%;
  margin-bottom: 20px;
  padding: 5px 0 0 0;
  display: flex;
  flex-direction: row;

  &__left-section {
    width: 75%;
    padding: 6px 8px 0;

    .left-section-row1 {
      width: 100%;
      display: flex;
      flex-direction: row;
      column-gap: 8px;
      //justify-content: center;
      flex-wrap: nowrap;
      margin-bottom: 10px;

      @media (max-width: $breakpoint-lg) {
        flex-wrap: wrap;
      }

      &__flex-col {
        width: 100%;
      }

      .col1 {
        width: 65%;
        height: 400px;
        border-radius: 6px;
        position: relative;
        overflow: hidden;

        img {
          max-width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 6px;
          display: block;
        }
      }

      .col2 {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: 20px;
        position: relative;
        overflow: hidden;
        max-width: 35%;

        .sub-col {
          //width: calc(100% - 10px);
          height: 190px;
          border-radius: 6px;
          position: relative;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            border-radius: 6px;
            object-fit: cover;
            display: block;
          }
        }
      }
    }

    .left-section-row2 {
      display: flex;
      gap: 8px;
      position: relative;

      &__col {
        flex: 1;
        height: 103px;
        border-radius: 6px;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          border-radius: 6px;
          object-fit: cover;
          display: block;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.4); 
          border-radius: 6px;
          cursor: pointer;
        }
      }

      .morePhotos {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 2px 5px;
        background-color: #2a2b30;
        position: absolute;
        bottom: 10px;
        right: 10px;
        border-radius: 12px;
        transition: background-color 0.5s ease;
        cursor: pointer;

        .fa-images {
          color: #ffffff;
        }

        span {
          font-size: 13px;
          font-weight: 500;
          color: #ffffff;
          padding: 0 5px;
        }

        &:hover {
          background-color: color.adjust(#2a2b30, $lightness: -20%);
        }
      }
    }
  }

  &__right-section {
    padding: 6px 8px 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 513px; /* Match the height of the left section (400px + 103px + 10px gap) */
    
    .reviewFloater {
      border: 1px solid #e4e4e4;
      margin-bottom: 16px;

      .hp-gallery-score-card {
        padding: 12px;
        display: flex;
        justify-content: end;
        border-bottom: 2px solid #e4e4e4;

        .review {
          display: flex;
          flex-direction: row;
          .rating {
            padding: 5px;
            background-color: $primary-color;
            font-size: 16px;
            color: #fafafa;
            font-weight: 600;
            border-radius: 6px 6px 6px 0;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .rating-detail {
            text-align: right;
            margin: 0 10px 0 0;

            .detail1,
            .detail2 {
              margin: 0;
              padding: 0;
              line-height: 1.1;
            }

            .detail1 {
              font-size: 16px;
              font-weight: 600;
              color: #17181c;
            }

            .detail2 {
              font-size: 12px;
              font-weight: 500;
              color: #5e616e;
            }
          }
        }
      }

      .best-review-score-card {
        padding: 12px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        &__label {
          font-size: 14px;
          color: #000;
          font-weight: 600;
        }

        &__count {
          padding: 2px 5px;
          font-size: 16px;
          color: #000;
          font-weight: 400;
          border: 1px solid #000;
          border-radius: 6px 6px 6px 0;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    
    /* Style for the map container to take remaining height */
    :global(.hotel-search-map-container) {
      flex: 1;
      height: 100%;
      border: 1px solid #e4e4e4;
      border-radius: 6px;
      overflow: hidden;
    }
  }
}