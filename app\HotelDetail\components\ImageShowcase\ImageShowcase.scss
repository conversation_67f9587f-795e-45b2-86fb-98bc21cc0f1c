@use "/styles/variable" as *;

.image-showcase-container {
  flex: 3;
  overflow: hidden;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;

  // Navigation container for arrows and content
  .navigation-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
    
    // Arrow styles updated to match the design
    .arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.5);
      color: #2478d7;
      border: none;
      height: 60px;
      width: 60px;
      font-size: 40px;
      cursor: pointer;
      z-index: z-index(base);
      position: absolute;
      top: 50%;
      transform: translateY(-70%);
      border-radius: 4px;
      transition: all 0.2s ease;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.8);
        color: #0056b3;
      }
      
      &.left {
        left: 20px;
      }
      
      &.right {
        right: 20px;
      }
    }
  }
  
  .image-showcase {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    /* Main image container */
    .main-image-container {
      //width: 100%;
      width: 65%;

      height: calc(100% - 100px);
      position: relative;
      overflow: hidden;
      border-radius: 7px;
      
      @media (max-width: $isMobile) {
        height: 40vh;
      }
      
      .main-image {
        width: 100%;
        height: 100%;
        position: relative;
        transition: transform 0.5s ease-in-out;
      }
      
      &.next .main-image {
        animation: slideInFromRight 0.5s ease-in-out forwards;
      }
      
      &.prev .main-image {
        animation: slideInFromLeft 0.5s ease-in-out forwards;
      }
      
      &.no-transition .main-image {
        transition: none !important;
        animation: none !important;
      }
    }
    
    /* Image counter */
    .image-counter {
      position: absolute;
      bottom: 110px;
      left: 50%;
      transform: translateX(-50%);
      color: #333;
      font-size: 16px;
      background-color: rgba(255, 255, 255, 0.7);
      padding: 5px 15px;
      border-radius: 20px;
      z-index: z-index(base);
      font-weight: 500;
      
      @media (max-width: $isMobile) {
        bottom: 80px;
        font-size: 14px;
      }
    }
    
    /* Thumbnails */
    .thumbnail-container {
      width: 85%;
      height: 100px;
      display: flex;
      justify-content: flex-start;
      gap: 10px;
      padding: 15px;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      position: relative;
      //background-color: #f5f5f5;
      scrollbar-width: none; // for Firefox
      height: 100px;

      &::-webkit-scrollbar {
        display: none; // for WebKit (Chrome, Safari, Edge)
      }
      
      .thumbnail {
        min-width: 120px;
        width: 120px;
        height: 70px;
        border-radius: 4px;
        position: relative;
        transition: all 0.2s ease;
        cursor: pointer;
        border: 2px solid transparent;
        overflow: hidden;
        flex-shrink: 0;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        
        &.active {
          border: 2px solid #0770e4;
          box-shadow: 0 0 0 2px rgba(7, 112, 228, 0.3);
        }
      }
    }
    
    /* Mobile styles */
    &.mobile {
      .mobile-scroll-container {
        display: flex;
        overflow-x: auto;
        width: 100%;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        scroll-snap-type: x mandatory;
        height: 40vh;
        
        &::-webkit-scrollbar {
          display: none;
        }
        
        .mobile-image-item {
          flex: 0 0 100%;
          width: 100%;
          height: 100%;
          position: relative;
          scroll-snap-align: center;
          scroll-snap-stop: always;
        }
      }
      
      .thumbnail-container {
        padding: 10px;
        height: 80px;
        
        .thumbnail {
          min-width: 80px;
          width: 80px;
          height: 60px;
        }
      }
      
      .image-counter {
        bottom: 70px;
      }
    }
  }
}

/* Animations */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Fallback for no images */
.no-images {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 300px;
  background-color: #f5f5f5;
  color: #666;
  font-size: 18px;
}