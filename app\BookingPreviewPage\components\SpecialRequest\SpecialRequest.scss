@use "/styles/variable" as *;

.special-request{
    width: 100%;
    padding: 20px;
    background-color: #ffffff;
    border-radius: 15px;

    @media (max-width: $breakpoint-md) {
        border-radius: 0%;
        
    }

    &__header{
        color: $booking_black;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    &__description{
        font-size: 14px;
        color: #5E616E;
        margin-bottom: 10px;
    }

    &__input-charCount-buttons{
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
        overflow: hidden;
        visibility: hidden;
        
        transition: 
        opacity 0.3s ease-in-out,
         transform 0.2s ease-in-out,
          max-height 0.2s ease-in-out,
           visibility 0.2s ;

           &.show{
            opacity: 1;
            transform: translateY(0);
            max-height: 500px;
            visibility: visible;
           }

        .input-charCount{
            margin-bottom: 15px;
           
            .input{
                background-color: #FFFFFF;
                padding: 15px;
                border-radius: 10px;
                border: 0.05px solid rgba(0, 0, 0, 0.2);
                width: 100%;
                outline: 0;

                &:focus{
                    border-color: #0770E4;
                   
                }
    
            }
    
            .charCount{
               display: flex;
               justify-content: end;
               align-items: start;
    
    
    
                .count{
                   
                    font-size: 12px;
                    font-weight: 500;
                    color: #5E616E;
                 
    
                }
    
            }
    
        }

        .buttons{
            display: flex;
            flex-direction: row;
            align-items: center;
            column-gap: 15px;
        }

    }

    

    &__button{
        font-size: 16px;
        font-weight: bold;
        color: $secondary-color;
    }

    
}