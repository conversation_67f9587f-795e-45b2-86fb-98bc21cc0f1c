export interface Root {
  data: Data
}

export interface Data {
  offers: Offer[]
  selectedCouponCode: string
}

export interface Offer {
  couponCode: string
  instantDiscount?: number
  displayedInstantDiscount?: number
  cashback?: number
  displayedCashback?: number
  burnMoneyInfo: BurnMoneyInfo
  applyMessage: string
  termsAndConditions: string
  defaultFare: DefaultFare
  paymentOptionWrapper: PaymentOptionWrapper
  paymentType: string
  duplicateCouponCodeInOffer: boolean
  cashbackText?: string
}

export interface BurnMoneyInfo {
  burnAmount: number
  burnIxiMoneyAmount: number
  burnIxiMoneyMaxAmount: number
  title: string
  text: string
}

// New interfaces to replace 'any' types
export interface DefaultFare {
  amount: number
  currency: string
  breakdown?: FareBreakdown[]
}

export interface FareBreakdown {
  type: string
  amount: number
  description?: string
}

export interface PaymentOptionWrapper {
  id: string
  name: string
  type: string
  isDefault?: boolean
  options?: PaymentOption[]
}

export interface PaymentOption {
  id: string
  name: string
  description?: string
  icon?: string
  additionalInfo?: Record<string, unknown>
}