@use "sass:color";
@use "/styles/variable" as *;

.mobile-menu-content {
  padding-bottom: 20px;

  .user-greeting {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .user-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-right: 16px;
      overflow: hidden;

      .avatar-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: $primary-color;
        color: white;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .user-info {
      .welcome-text {
        margin: 0;
        font-size: 14px;
        color: #666;
      }

      .user-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }
  }
}

.mobile-menu-group {
  margin-top: 8px;

  &__header {
    display: block;
    font-size: 12px;
    background-color: rgb(248, 247, 249);
    padding: 12px 24px;
    text-transform: uppercase;
    color: #000000;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  &__content {

    ul {
      padding: 8px 0;
      margin: 0;
      list-style: none;

      .menu-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        margin: 0;
        font-size: 15px;
        font-weight: 400;
        color: #2e2e2e;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: rgba(0, 59, 149, 0.05);
          color: $primary-color;
        }

        &.active {
          background-color: rgba(0, 59, 149, 0.08);
          color: $primary-color;
          font-weight: 500;
        }

        .menu-icon {
          color: $primary-color;
          width: 20px;
          height: 20px;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .menu-label {
          flex: 1;
        }
      }


    }

    // Services grid styling
    .services-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      //padding: 16px 0;
      padding: 16px 10px;

      .service-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12px 8px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        background-color: transparent;

        &:hover {
          background-color: rgba(0, 59, 149, 0.05);
          transform: translateY(-2px);
        }

        &.active {
          background-color: rgba(0, 59, 149, 0.1);
          border: 2px solid $primary-color;
          transform: translateY(-1px);

          .service-label {
            color: $primary-color;
            font-weight: 600;
          }
        }

        .service-icon {
          width: 32px;
          height: 32px;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }

        .service-label {
          font-size: 12px;
          font-weight: 500;
          color: #2e2e2e;
          text-align: center;
          line-height: 1.2;
        }
      }
    }


  }
}

.menu-item-type2-wrapper{
      display: flex;
      flex-direction: row;
      position: absolute;
      bottom: 0;
      width: 100%;

      .currency-selector-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 13px;
  width: 376px;

  @media (max-width: $breakpoint-sm) {
    width: 200px;
    max-height: 275px;
    overflow-y: auto;
  }

  .currency-selector {
    width: 80px;
    display: flex;
    justify-content: space-between;
    border-radius: 9px;
    padding: 5px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: border-color 0.2 ease;
    cursor: pointer;
    background: none;

    @media (max-width: $breakpoint-sm) {
      width: 70px;
    }

    &:hover,
    &.active {
      border-color: $primary-color;

      .currency-name {
        color: $primary-color;
      }
    }

    .currency-name,
    .currency-symbol {
      font-size: 11px;
    }

    .currency-name {
      color: $black-color;
      font-weight: 500;
      transition: color 0.2 ease;
    }

    .currency-symbol {
      color: #ccc;
    }
  }
}

        .menu-item-type2 {
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        margin: 0;
        font-size: 15px;
        font-weight: 400;
        color: #2e2e2e;
        cursor: pointer;
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        white-space: nowrap;

        &:hover {
          background-color: rgba(0, 59, 149, 0.05);
          color: $primary-color;
        }

        .menu-icon {
          color: $primary-color;
          width: 20px;
          height: 20px;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .menu-label {
          font-size: 15px;
          font-weight: 400;
        }
      }
      
    }

// RTL support
.rtl {
  .mobile-menu-content {
    .user-greeting {
      flex-direction: row-reverse;
      text-align: right;

      .user-avatar {
        margin-right: 0;
        margin-left: 16px;
      }
    }
  }

  .mobile-menu-group {
    &__header {
      text-align: right;
    }

    &__content {
      ul {
        .menu-item {
          flex-direction: row-reverse;
          text-align: right;

          .menu-icon {
            margin-right: 0;
            margin-left: 12px;
          }
        }
      }
    }
  }

  .menu-item-type2 {
    .menu-icon {
      margin-right: 0;
      margin-left: 8px;
    }
  }
}

// Mobile Menu Modal styles - with prefixed class names for global scope
.mobile-menu-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .mobile-menu-modal-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    animation: mobileMenuModalSlideIn 0.3s ease-out;

    @keyframes mobileMenuModalSlideIn {
      from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }

    .mobile-menu-modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px 16px;
      border-bottom: 1px solid #e5e7eb;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .mobile-menu-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        color: #6b7280;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f3f4f6;
          color: #374151;
        }
      }
    }

    .mobile-menu-modal-body {
      padding: 16px 24px 24px;
      max-height: 60vh;
      overflow-y: auto;

      .mobile-menu-language-option {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 12px 16px;
        border: none;
        background: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 4px;

        &:hover {
          background-color: #f3f4f6;
        }

        &.active {
          background-color: rgba(0, 59, 149, 0.1);
          color: $primary-color;
        }

        .mobile-menu-flag-container {
          width: 24px;
          height: 16px;
          margin-right: 12px;
          border-radius: 2px;
          overflow: hidden;
          border: 1px solid #e5e7eb;

          .mobile-menu-flag-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        span {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .mobile-menu-currency-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;

        .mobile-menu-currency-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 12px 8px;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          background: none;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: $primary-color;
            background-color: rgba(0, 59, 149, 0.05);
          }

          &.active {
            border-color: $primary-color;
            background-color: rgba(0, 59, 149, 0.1);
            color: $primary-color;
          }

          .mobile-menu-currency-code {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 2px;
          }

          .mobile-menu-currency-symbol {
            font-size: 11px;
            color: #6b7280;
          }
        }
      }
    }
  }
}



