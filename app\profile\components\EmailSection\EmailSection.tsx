"use client";
import OtpInput from "@/app/components/common/OtpInput";
import { showToast } from "@/app/components/utilities/SonnerToasterCustom";
import React, { useState, useEffect } from "react";
import './EmailSection.scss'

// Define the types for the EmailSection props
interface EmailSectionProps {
  initialEmail: string;
  onSaveEmail: (email: string) => void;
  parentErrorMessage?: string;
}

const EmailSection: React.FC<EmailSectionProps> = ({
  initialEmail,
  onSaveEmail,
  parentErrorMessage,
}) => {
  const [editMode, setEditMode] = useState(!initialEmail);
  const [email, setEmail] = useState(initialEmail);
  const [errorMessage, setErrorMessage] = useState(parentErrorMessage || "");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(!!initialEmail);
  const [otp, setOtp] = useState(['', '', '', '', '', '']);

  // New state for OTP handling
  const [showOtpScreen, setShowOtpScreen] = useState(false);
  
  // Cooldown timer state
  const [cooldownTime, setCooldownTime] = useState(0);
  const cooldownPeriod = 60; // Cooldown period in seconds

  // Update internal state if parent props change
  useEffect(() => {
    setEmail(initialEmail);
    setIsVerified(!!initialEmail);
  }, [initialEmail]);

  useEffect(() => {
    if (parentErrorMessage) {
      setErrorMessage(parentErrorMessage);
    }
  }, [parentErrorMessage]);

  // Countdown timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (cooldownTime > 0) {
      timer = setTimeout(() => {
        setCooldownTime(prevTime => prevTime - 1);
      }, 1000);
    }
    
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [cooldownTime]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    // Clear error when user starts typing and reset verification status
    if (errorMessage) {
      setErrorMessage("");
    }
    setIsVerified(false);
    setShowOtpScreen(false);
  };

  const validateEmail = (email: string) => {
    if (!email) return "Email is required";
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) ? "" : "Please enter a valid email address";
  };

  const handleVerify = async () => {
    // Validate email before sending to backend
    const validationError = validateEmail(email);
    if (validationError) {
      setErrorMessage(validationError);
      return;
    }

    // Start verification process
    setIsVerifying(true);
    setErrorMessage("");

    try {
      // This will be replaced with actual API call when backend is ready
      await sendOtpToEmail(email);
      
      // If OTP sent successfully
      setShowOtpScreen(true);
      setIsVerifying(false);
      // Start cooldown timer after successful OTP request
      setCooldownTime(cooldownPeriod);
      showToast(`A verification code has been sent to ${email}`, "success", "top-right");
    } catch (error: unknown) {
      // Handle verification errors
      const errorMessage = error instanceof Error 
        ? error.message 
        : "Failed to send OTP. Please try again.";
      setErrorMessage(errorMessage);
      setIsVerifying(false);
      showToast(errorMessage, "error", "top-right");
    }
  };

  // Simulate API call to send OTP - will be replaced with actual backend integration
  const sendOtpToEmail = (email: string): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate success for most emails
        if (email.includes("invalid")) {
          reject(new Error("This email address appears to be invalid."));
        } else if (email.includes("taken")) {
          reject(new Error("This email address is already in use."));
        } else {
          resolve(true);
        }
      }, 1500); // Simulate network delay
    });
  };

  const handleOtpVerification = async (otpValue: string) => {
    try {
      setIsVerifying(true);
      // This will be replaced with actual API call to verify OTP
      await verifyOtp(email, otpValue);
      
      // If OTP verification successful
      setIsVerified(true);
      setShowOtpScreen(false);
      onSaveEmail(email);
      setEditMode(false);
      showToast("Email verified successfully!", "success", "top-right");
    } catch (error: unknown) {
      // Handle OTP verification errors
      const errorMessage = error instanceof Error 
        ? error.message 
        : "Invalid OTP. Please try again.";
      showToast(errorMessage, "error", "top-right");
    } finally {
      setIsVerifying(false);
    }
  };

  // Simulate API call to verify OTP - will be replaced with actual backend integration
  const verifyOtp = (email: string, otp: string): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // For testing, consider "123456" as valid OTP
        if (otp === "123456") {
          resolve(true);
        } else {
          reject(new Error("Invalid OTP. Please check and try again."));
        }
      }, 1000); // Simulate network delay
    });
  };

  const handleCancel = () => {
    // Reset to parent's value on cancel
    setEmail(initialEmail);
    setErrorMessage("");
    setEditMode(false);
    setShowOtpScreen(false);
    setIsVerified(!!initialEmail);
  };

  const handleResendOtp = async () => {
    // Don't allow resending if we're still in cooldown period
    if (cooldownTime > 0) {
      return;
    }
    
    try {
      setIsVerifying(true);
      // This will be replaced with actual API call when backend is ready
      await sendOtpToEmail(email);
      setIsVerifying(false);
      // Reset cooldown timer
      setCooldownTime(cooldownPeriod);
      showToast(`A new verification code has been sent to ${email}`, "success", "top-right");
    } catch (error: unknown) {
      setIsVerifying(false);
      const errorMessage = error instanceof Error 
        ? error.message 
        : "Failed to resend OTP. Please try again.";
      showToast(errorMessage, "error", "top-right");
    }
  };

  const handleOtpChange = (newOtp: string[]) => {
    setOtp(newOtp);
  };

  // Format remaining time for display
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  return (
    <div className="profile__form-field2">
      <div className="form-field__details-group">
        <div className="form-field__userName">
          <div className="form-field__label-fullname">
            <p className="label">Email</p>
            {showOtpScreen ? (
              <div className="mt-2">
                <p className="text-sm mb-2">Enter the verification code sent to {email}</p>
                <OtpInput 
                  length={6}
                  value={otp}
                  onChange={handleOtpChange}
                  autoFocus={true}
                  className="mb-4"
                />

                   <button
                    className={`text-blue-500 text-sm ${cooldownTime > 0 || isVerifying ? "opacity-50 cursor-not-allowed" : ""}`}
                    onClick={handleResendOtp}
                    disabled={cooldownTime > 0 || isVerifying}
                  >
                    {isVerifying ? "Sending..." : cooldownTime > 0 
                      ? `Resend in ${formatTime(cooldownTime)}` 
                      : "Resend verification code"}
                  </button>
                <div className="form-field__input-buttons">
                         <button
                  onClick={handleCancel}
                  type="button"
                  className="btn cancelBtn"
                >
                  Cancel
                </button>
                  <button
                    onClick={() => handleOtpVerification(otp.join(''))}
                    className="btn saveBtn"
                    disabled={otp.some(digit => digit === '') || isVerifying}
                  >
                    {isVerifying ? "Verifying..." : "Verify OTP"}
                  </button>
               
                </div>
              </div>
            ) : editMode || !email ? (
              <div className="verify-textbox-btn-group">
                <input
                  className="verify-textbox-btn-group__textbox"
                  type="text"
                  name="email"
                  value={email}
                  onChange={handleInputChange}
                  disabled={isVerifying}
                  placeholder="Enter your email address"
                />
          <div className="verify-textbox-btn-group__buttons-wrapper">
                  <button
                  className={`verify-textbox-btn-group__cancelBtn ${isVerifying ? "opacity-50" : ""}`}
                  type="button"
                  onClick={handleCancel}
                 
                >
                  Cancel
                </button>
                  <button
                  className={`verify-textbox-btn-group__verifyBtn ${isVerifying ? "opacity-50" : ""}`}
                  type="button"
                  onClick={handleVerify}
                  disabled={isVerifying}
                >
                  {isVerifying ? "Sending OTP..." : "Verify"}
                </button>

             
          </div>
              </div>
            ) : (
              <div className="flex items-center">
                <p className="fullname">{email}</p>
                {/* {isVerified && (
                  <span className="ml-2 text-green-500">✓</span>
                )} */}
              </div>
            )}
            {errorMessage && !showOtpScreen && (
              <p className="profile-form-error text-red-500">{errorMessage}</p>
            )}
          </div>
          
          {!editMode && email && !showOtpScreen && (
            <span onClick={() => setEditMode(true)} className="form-field__action">Edit</span>
          )}
          
          {/* {(editMode || showOtpScreen) && email && !showOtpScreen && (
            <span onClick={handleCancel} className="form-field__action">Cancel</span>
          )} */}
        </div>
      </div>
    </div>
  );
};

export default EmailSection;