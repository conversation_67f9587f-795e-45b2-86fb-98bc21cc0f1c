@use "/styles/variable" as *;
@use "sass:color";

.HotelGrid {
  width: 100%;
  height: auto;
  //height: 272px;
  display: flex;
  flex-direction: column;
  //   background-color: #E6F7F5  ;
  border: 1px solid $black_color4;
  border-radius: 6px;

    cursor: pointer;

  .grid-image {
    width: 260px;
    height: 260px;
    position: relative;

    @media(max-width: $isMobile){
      height: 170px;
    }

    img {
      max-width: 100%;
      height: 100%;
      border-radius: 6px 6px 0 0;
    }
    .heartIcon {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      position: absolute;
      top: 7px;
      right: 7px;
      transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out,
        transform 0.2s ease;
      cursor: pointer;

      .fa-heart {
        color: #cccccc;
        font-size: 13px;
        transition: color 0.3s ease-in-out;
      }

      &:hover {
        background-color: #f2f2f2; // Slightly darker white
        transform: scale(1.05); // Slight pop effect on hover
      }

      &.active {
        background-color: #ffebee; // Soft pinkish background for active state
        transform: scale(1.1); // Slight pop effect on click
        .fa-heart {
          color: #e63946; // Reddish heart when active
        }
        animation: pulse 0.3s ease-in-out;
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
      100% {
        transform: scale(1);
      }
    }
  }

  .grid-details {
    display: flex;
    flex-direction: column;
    //height: 240px;
    //width: 526px;

    .details {
      display: flex;
      flex-direction: row;

      .detail1 {
        display: flex;
        flex-direction: column;
        gap: 3px;
        padding: 0 12px 12px 12px;



   
        .header {
          padding: 10px 0 0 0; 

          .heading {
            font-size: 16px;
            font-weight: 800;
            color: $primary_color;
            
            transition: color 0.2s ease;
            cursor: pointer;
            
            &:hover{
              color: black;
              
            }
          }

        .icons{
          .fa-star {
            font-size: 10px;
            color: $rating_color;
            margin-right: 2px;
          }
          .thumbsUpIcon {
            padding: 3px;
            background-color: orange;
            color: #fff;
            font-size: 11px;
          }

        }

          // .rating-stars-thumbsup {
          //   display: flex;
          //   flex-direction: row;
          //   align-items: center;
          //   .fa-star {
          //     font-size: 11px;
          //     color: $rating_color;
          //     margin-right: 2px;
          //   }
          //   .thumbsUpIcon {
          //     width: 20px;
          //     height: 20px;
          //     display: flex;
          //     justify-content: center;
          //     align-items: center;
          //     margin-left: 5px;
          //     background-color: orange;

          //     .fa-thumbs-up {
          //       color: #fff;
          //       font-size: 12px;
          //     }
          //   }
          // }
        }

        .rating-review {
          display: flex;
          flex-direction: row;
          justify-content: start;
          align-items: center;
          gap: 7px;
          margin-bottom: 3px;

          .review {
            display: flex;
            flex-direction: row;
            align-items: center;

            .msg {
              font-size: 12px;
              font-weight: 600;
            }

            .dot {
              margin-bottom: 10px;
              font-weight: 600;
              padding: 0 3px;
              color: $black_color2;
            }

            .count {
              font-size: 12px;
              color: $black_color2;
              display: flex;
              flex-direction: row;
              align-items: center;
            }
          }

          .rating {
            background-color: #05514d;
            padding: 1px 5px;
            color: #fff;
            font-weight: 600;
            border-radius: 6px 6px 6px 0;
            span {
              font-size: 12px;
            }
          }
        }

        .overview {
          font-size: 12px;
          color: $primary_color;
          font-weight: 500;
        }

        .location-distance {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          font-size: 12px;
          align-items: center;
          margin-bottom: 6px;

          .links {
            display: flex;
            flex-direction: row;
            align-items: center;
            a {
              color: $primary_color;
              text-decoration: underline $primary_color;
              font-weight: 600;
              cursor: pointer;
            }

            .dot {
              margin-bottom: 5px;
              font-weight: 800;
              padding: 0 4px;
              color: $black_color4;
            }
          }
        }

        .list {
          padding: 4px;
          margin: -4px;
          min-height: 166px;
          border-top: 1px solid $black_color4;

          .list-title {
            font-size: 12px;
            font-weight: 700;
            margin-bottom: 2px;
            margin-top: 5px;
          }

          ul {
            display: flex;
            flex-direction: row;
            list-style-type: disc;
            flex-wrap: wrap;
            overflow: hidden;
            max-width: calc(100%);

            li {
              font-size: 12px;
              margin-right: 9px;
              padding-right: 9px;

              &::marker {
                font-size: 10px;
              }

              &:first-child {
                list-style-type: none;
              }
            }
          }

          .message {
            font-size: 12px;
            color: $red_color4;
            font-weight: 800;
          }
        }

        .travel-details {
          display: flex;
          flex-direction: column;
          align-items: end;

          .detail {
            color: $black_color2;
            font-size: 12px;
            text-align: right;
            white-space: nowrap;
          }

          .price {
            font-size: 20px;
            font-weight: 600;
          }
        }
      }

      .detail2 {
        .overview {
          text-align: right;
        }
      }
    }

    .see-availability-button-field {
      display: flex;
      justify-content: end;
      align-items: center;

      .seeAvailabilityBtn {
        padding: 5px 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $primary_color;
        color: #fff;
        cursor: pointer;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        transition: color 0.2s ease;

        .fa-greater-than {
          font-size: 10px;
          font-weight: 400;
          margin-left: 10px;
        }

        &:hover {
          background-color: color.adjust($primary_color, $lightness: -3%);
        }
      }
    }
  }
}
